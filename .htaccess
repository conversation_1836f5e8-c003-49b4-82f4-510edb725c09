RewriteEngine On
RewriteBase /bbgl/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全设置
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# 禁止访问敏感目录和文件
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 禁止访问配置和日志目录
RedirectMatch 404 /\..*$
RedirectMatch 404 /config/
RedirectMatch 404 /logs/

