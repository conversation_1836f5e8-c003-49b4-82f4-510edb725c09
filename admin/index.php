<?php
/**
 * 管理后台专用入口文件
 * 通过 /admin/ 目录直接访问管理后台，无需URL重写
 */

// 严格类型与基础设置
declare(strict_types=1);
ini_set('display_errors', '1');
error_reporting(E_ALL);

// 时区统一为北京时间
date_default_timezone_set('Asia/Shanghai');

// 设置管理后台标识
$_SERVER['IS_ADMIN_REQUEST'] = true;

// 修正BASE_URL，确保静态资源路径正确
// 当前在 /admin/ 目录下，需要回到上级目录
$currentScript = $_SERVER['SCRIPT_NAME'] ?? '/admin/index.php';
$parentDir = dirname(dirname($currentScript)) . '/';
$_SERVER['SCRIPT_NAME'] = $parentDir . 'index.php';
$_SERVER['REQUEST_URI'] = $parentDir . 'admin/';

// 引入主应用的配置和自动加载
require_once __DIR__ . '/../config/bootstrap.php';

// 检查用户是否已登录
if (!empty($_SESSION['user'])) {
    // 已登录，重定向到系统配置页面（完整的管理后台界面）
    $redirectUrl = $parentDir . 'index.php?r=sys/config';
    header('Location: ' . $redirectUrl);
    exit;
} else {
    // 未登录，显示管理员登录页面
    $authController = new \App\Controllers\AuthController();
    echo $authController->loginPage();
}
?>
