<?php
/**
 * 管理员登录页面专用入口
 */

declare(strict_types=1);
ini_set('display_errors', '1');
error_reporting(E_ALL);
date_default_timezone_set('Asia/Shanghai');

// 设置管理后台标识
$_SERVER['IS_ADMIN_REQUEST'] = true;

// 修正BASE_URL，确保路径正确
$currentScript = $_SERVER['SCRIPT_NAME'] ?? '/admin/login.php';
$parentDir = dirname(dirname($currentScript)) . '/';
$_SERVER['SCRIPT_NAME'] = $parentDir . 'index.php';
$_SERVER['REQUEST_URI'] = $parentDir . 'admin/login.php';

// 引入主应用配置
require_once __DIR__ . '/../config/bootstrap.php';

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // POST请求 - 处理登录
    $authController = new \App\Controllers\AuthController();
    $result = $authController->login();

    // 如果登录成功且没有重定向，手动重定向到管理后台
    if (empty($result) && !empty($_SESSION['user'])) {
        $redirectUrl = $parentDir . 'index.php?r=sys/config';
        header('Location: ' . $redirectUrl);
        exit;
    }

    echo $result;
} else {
    // GET请求 - 显示登录页面
    $authController = new \App\Controllers\AuthController();
    echo $authController->loginPage();
}
?>
