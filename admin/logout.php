<?php
/**
 * 管理员登出功能
 */

declare(strict_types=1);
ini_set('display_errors', '1');
error_reporting(E_ALL);
date_default_timezone_set('Asia/Shanghai');

// 设置管理后台标识
$_SERVER['IS_ADMIN_REQUEST'] = true;

// 修正BASE_URL，确保路径正确
$currentScript = $_SERVER['SCRIPT_NAME'] ?? '/admin/logout.php';
$parentDir = dirname(dirname($currentScript)) . '/';
$_SERVER['SCRIPT_NAME'] = $parentDir . 'index.php';
$_SERVER['REQUEST_URI'] = $parentDir . 'admin/logout.php';

// 引入主应用配置
require_once __DIR__ . '/../config/bootstrap.php';

// 处理登出
$authController = new \App\Controllers\AuthController();
echo $authController->logout();
?>
