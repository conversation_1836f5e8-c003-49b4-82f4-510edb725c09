<?php
/**
 * 管理后台通用路由处理器
 * 用法：/admin/router.php?r=controller/action
 */

declare(strict_types=1);
ini_set('display_errors', '1');
error_reporting(E_ALL);
date_default_timezone_set('Asia/Shanghai');

// 设置管理后台标识
$_SERVER['IS_ADMIN_REQUEST'] = true;

// 引入主应用配置
require_once __DIR__ . '/../config/bootstrap.php';

// 获取路由参数
$route = $_GET['r'] ?? 'admin';

// 确保是管理后台路由
$adminRoutes = ['admin', 'auth', 'sys', 'users', 'reports', 'templates', 'devices', 'static'];
$isAdminRoute = false;

foreach ($adminRoutes as $adminRoute) {
    if ($route === $adminRoute || strpos($route, $adminRoute . '/') === 0) {
        $isAdminRoute = true;
        break;
    }
}

if (!$isAdminRoute) {
    // 不是管理后台路由，重定向到管理后台首页
    header('Location: ' . dirname($_SERVER['SCRIPT_NAME']) . '/');
    exit;
}

// 设置路由参数并调用主路由器
$_GET['r'] = $route;
$_SERVER['REQUEST_URI'] = '/index.php?r=' . urlencode($route);

// 调用主应用路由器
\App\Core\Router::dispatch();
?>
