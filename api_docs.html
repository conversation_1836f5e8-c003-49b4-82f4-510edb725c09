<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - 设备资料录入管理系统</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        .api-endpoint {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .method-get { border-left-color: #28a745; }
        .method-post { border-left-color: #ffc107; }
        .method-put { border-left-color: #17a2b8; }
        .method-delete { border-left-color: #dc3545; }
        .response-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        .test-button {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-md-3">
                <div class="sticky-top" style="top: 20px;">
                    <h5>API目录</h5>
                    <ul class="list-unstyled">
                        <li><a href="#auth">认证接口</a></li>
                        <li><a href="#user">用户接口</a></li>
                        <li><a href="#menu">菜单接口</a></li>
                        <li><a href="#reports">报表接口</a></li>
                        <li><a href="#devices">设备接口</a></li>
                        <li><a href="#static">静态页面接口</a></li>
                        <li><a href="#system">系统接口</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-9">
                <h1>API文档</h1>
                <p class="lead">设备资料录入管理系统 RESTful API 接口文档</p>
                
                <div class="alert alert-info">
                    <h6><i class="fa fa-info-circle me-2"></i>基础信息</h6>
                    <p><strong>Base URL:</strong> <code id="baseUrl">http://localhost:8089/bbgl/</code></p>
                    <p><strong>认证方式:</strong> Session Cookie</p>
                    <p><strong>响应格式:</strong> JSON</p>
                </div>

                <!-- 认证接口 -->
                <section id="auth">
                    <h2>认证接口</h2>
                    
                    <div class="api-endpoint method-post">
                        <h5>POST /index.php?r=frontend/login</h5>
                        <p>用户登录</p>
                        <h6>请求参数</h6>
                        <ul>
                            <li><code>username</code> (string) - 用户名</li>
                            <li><code>password</code> (string) - 密码</li>
                        </ul>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "message": "登录成功"
}
                        </div>
                        <button class="btn btn-sm btn-primary test-button" onclick="testLogin()">测试接口</button>
                    </div>
                    
                    <div class="api-endpoint method-post">
                        <h5>POST /index.php?r=frontend/logout</h5>
                        <p>用户登出</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "message": "已退出登录"
}
                        </div>
                        <button class="btn btn-sm btn-primary test-button" onclick="testLogout()">测试接口</button>
                    </div>
                </section>

                <!-- 用户接口 -->
                <section id="user">
                    <h2>用户接口</h2>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=api/user</h5>
                        <p>获取当前用户信息</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "real_name": "管理员",
    "roles": ["admin"],
    "is_admin": true,
    "login_time": "2025-09-05 11:30:00"
  }
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('api/user')">测试接口</button>
                    </div>
                </section>

                <!-- 菜单接口 -->
                <section id="menu">
                    <h2>菜单接口</h2>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=frontend/getMenuData</h5>
                        <p>获取前端菜单数据</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": {
    "entry": [...],
    "query": [...],
    "admin": [...]
  }
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('frontend/getMenuData')">测试接口</button>
                    </div>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=api/menu</h5>
                        <p>获取菜单配置（API版本）</p>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('api/menu')">测试接口</button>
                    </div>
                </section>

                <!-- 报表接口 -->
                <section id="reports">
                    <h2>报表接口</h2>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=api/reports</h5>
                        <p>获取报表列表</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": [
    {
      "id": 1001,
      "name": "泵运行记录（连续）",
      "template_code": "pump_continuous",
      "report_type": "daily",
      "enabled": 1,
      "category_name": "现场设备",
      "category_code": "site",
      "devices": [...]
    }
  ]
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('api/reports')">测试接口</button>
                    </div>
                </section>

                <!-- 设备接口 -->
                <section id="devices">
                    <h2>设备接口</h2>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=api/devices</h5>
                        <p>获取设备列表</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": [
    {
      "id": 10,
      "name": "溶气泵",
      "type": "pump",
      "description": "溶气泵设备",
      "enabled": 1
    }
  ]
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('api/devices')">测试接口</button>
                    </div>
                </section>

                <!-- 静态页面接口 -->
                <section id="static">
                    <h2>静态页面接口</h2>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=frontend/getStaticPages</h5>
                        <p>获取静态页面列表</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": [
    {
      "filename": "entry_report_1001_device_10.html",
      "path": "static_pages/entry_report_1001_device_10.html",
      "title": "数据录入 - 泵运行记录（连续） - 溶气泵",
      "type": "entry"
    }
  ]
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('frontend/getStaticPages')">测试接口</button>
                    </div>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=static/show</h5>
                        <p>显示静态页面</p>
                        <h6>请求参数</h6>
                        <ul>
                            <li><code>file</code> (string) - 静态页面文件名</li>
                        </ul>
                        <p>返回处理后的HTML页面</p>
                    </div>
                </section>

                <!-- 系统接口 -->
                <section id="system">
                    <h2>系统接口</h2>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=api/stats</h5>
                        <p>获取系统统计信息</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": {
    "reports": 5,
    "devices": 10,
    "users": 3,
    "static_pages": 8,
    "today_entries": 25,
    "system_version": "1.0.0",
    "php_version": "8.4.5",
    "server_time": "2025-09-05 11:45:00"
  }
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('api/stats')">测试接口</button>
                    </div>
                    
                    <div class="api-endpoint method-get">
                        <h5>GET /index.php?r=api/health</h5>
                        <p>系统健康检查</p>
                        <h6>响应示例</h6>
                        <div class="response-example">
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-09-05 11:45:00",
    "database": "connected",
    "directories": {...},
    "php_version": "8.4.5",
    "memory_usage": 2097152,
    "memory_peak": 4194304
  }
}
                        </div>
                        <button class="btn btn-sm btn-success test-button" onclick="testApi('api/health')">测试接口</button>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 测试结果模态框 -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">API测试结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="testResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/jquery-3.6.0.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        // 设置基础URL
        const baseUrl = window.location.origin + window.location.pathname.replace('api_docs.html', '');
        document.getElementById('baseUrl').textContent = baseUrl;
        
        // 测试API接口
        async function testApi(endpoint) {
            try {
                const response = await fetch(baseUrl + 'index.php?r=' + endpoint);
                const data = await response.json();
                
                showTestResult(endpoint, response.status, data);
            } catch (error) {
                showTestResult(endpoint, 'Error', { error: error.message });
            }
        }
        
        // 测试登录接口
        async function testLogin() {
            const username = prompt('请输入用户名:', 'admin');
            const password = prompt('请输入密码:', 'admin123');
            
            if (!username || !password) return;
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch(baseUrl + 'index.php?r=frontend/login', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                showTestResult('frontend/login', response.status, data);
            } catch (error) {
                showTestResult('frontend/login', 'Error', { error: error.message });
            }
        }
        
        // 测试登出接口
        async function testLogout() {
            try {
                const response = await fetch(baseUrl + 'index.php?r=frontend/logout', {
                    method: 'POST'
                });
                
                const data = await response.json();
                showTestResult('frontend/logout', response.status, data);
            } catch (error) {
                showTestResult('frontend/logout', 'Error', { error: error.message });
            }
        }
        
        // 显示测试结果
        function showTestResult(endpoint, status, data) {
            const resultHtml = `
                <h6>接口: ${endpoint}</h6>
                <p><strong>状态码:</strong> <span class="badge ${status === 200 ? 'bg-success' : 'bg-danger'}">${status}</span></p>
                <h6>响应数据:</h6>
                <pre class="response-example">${JSON.stringify(data, null, 2)}</pre>
            `;
            
            document.getElementById('testResult').innerHTML = resultHtml;
            new bootstrap.Modal(document.getElementById('testModal')).show();
        }
    </script>
</body>
</html>
