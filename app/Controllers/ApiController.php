<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

/**
 * API控制器
 * 提供RESTful API接口
 */
class ApiController
{
    /**
     * 获取当前用户信息
     */
    public function user(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }
        
        try {
            $user = $_SESSION['user'] ?? [];
            $roles = $_SESSION['roles'] ?? [];
            
            return json_encode([
                'success' => true,
                'data' => [
                    'id' => $user['id'] ?? null,
                    'username' => $user['username'] ?? '',
                    'real_name' => $user['real_name'] ?? '',
                    'roles' => $roles,
                    'is_admin' => in_array('admin', $roles),
                    'login_time' => $_SESSION['login_time'] ?? null
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取用户信息失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取系统统计信息
     */
    public function stats(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }
        
        try {
            $pdo = DB::conn();
            
            // 统计报表数量
            $reportCount = $pdo->query("SELECT COUNT(*) FROM reports WHERE enabled=1")->fetchColumn();
            
            // 统计设备数量
            $deviceCount = $pdo->query("SELECT COUNT(*) FROM devices")->fetchColumn();
            
            // 统计用户数量
            $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            
            // 统计静态页面数量
            $staticDir = __DIR__ . '/../../static_pages';
            $staticPageCount = 0;
            if (is_dir($staticDir)) {
                $staticPageCount = count(glob($staticDir . '/*.html'));
            }
            
            // 统计今日录入数据量（如果有数据表的话）
            $todayEntryCount = 0;
            try {
                $today = date('Y-m-d');
                $todayEntryCount = $pdo->query("SELECT COUNT(*) FROM entry_data WHERE DATE(created_at) = '$today'")->fetchColumn();
            } catch (\Exception $e) {
                // 如果没有entry_data表，忽略错误
            }
            
            return json_encode([
                'success' => true,
                'data' => [
                    'reports' => (int)$reportCount,
                    'devices' => (int)$deviceCount,
                    'users' => (int)$userCount,
                    'static_pages' => $staticPageCount,
                    'today_entries' => (int)$todayEntryCount,
                    'system_version' => '1.0.0',
                    'php_version' => PHP_VERSION,
                    'server_time' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取统计信息失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取报表列表
     */
    public function reports(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }
        
        try {
            $pdo = DB::conn();
            $userRoles = $_SESSION['roles'] ?? [];
            
            // 构建查询条件
            $sql = "SELECT r.id, r.name, r.template_code, r.report_type, r.enabled, 
                           rc.name AS category_name, rc.code AS category_code
                    FROM reports r 
                    LEFT JOIN report_categories rc ON r.category_id = rc.id 
                    WHERE r.enabled = 1";
            
            // 如果不是管理员，根据角色过滤
            if (!in_array('admin', $userRoles) && !in_array('mod', $userRoles)) {
                $roleConditions = [];
                foreach (['site', 'cb26', 'ctrl'] as $role) {
                    if (in_array($role, $userRoles)) {
                        $roleConditions[] = "rc.code LIKE '%$role%'";
                    }
                }
                if (!empty($roleConditions)) {
                    $sql .= " AND (" . implode(' OR ', $roleConditions) . ")";
                }
            }
            
            $sql .= " ORDER BY r.id";
            
            $reports = $pdo->query($sql)->fetchAll() ?: [];
            
            // 获取每个报表的设备信息
            foreach ($reports as &$report) {
                $deviceSql = "SELECT d.id, d.name, d.type 
                             FROM report_devices rd 
                             JOIN devices d ON rd.device_id = d.id 
                             WHERE rd.report_id = ? 
                             ORDER BY d.name";
                $stmt = $pdo->prepare($deviceSql);
                $stmt->execute([$report['id']]);
                $report['devices'] = $stmt->fetchAll() ?: [];
            }
            
            return json_encode(['success' => true, 'data' => $reports]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取报表列表失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取设备列表
     */
    public function devices(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }
        
        try {
            $pdo = DB::conn();
            
            $sql = "SELECT id, name, type, description, enabled FROM devices ORDER BY name";
            $devices = $pdo->query($sql)->fetchAll() ?: [];
            
            return json_encode(['success' => true, 'data' => $devices]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取设备列表失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取菜单配置
     */
    public function menu(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }
        
        try {
            // 使用FrontendController的菜单构建逻辑
            $frontendController = new FrontendController();
            $menuData = $frontendController->buildMenuData();
            
            return json_encode(['success' => true, 'data' => $menuData]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取菜单配置失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 健康检查
     */
    public function health(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        try {
            // 检查数据库连接
            $pdo = DB::conn();
            $pdo->query("SELECT 1")->fetch();
            
            // 检查关键目录
            $directories = [
                'logs' => __DIR__ . '/../../logs',
                'static_pages' => __DIR__ . '/../../static_pages',
                'uploads' => __DIR__ . '/../../uploads'
            ];
            
            $dirStatus = [];
            foreach ($directories as $name => $path) {
                $dirStatus[$name] = [
                    'exists' => is_dir($path),
                    'writable' => is_writable($path),
                    'path' => $path
                ];
            }
            
            return json_encode([
                'success' => true,
                'data' => [
                    'status' => 'healthy',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'database' => 'connected',
                    'directories' => $dirStatus,
                    'php_version' => PHP_VERSION,
                    'memory_usage' => memory_get_usage(true),
                    'memory_peak' => memory_get_peak_usage(true)
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode([
                'success' => false,
                'data' => [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        }
    }
    
    /**
     * 通用错误响应
     */
    private function errorResponse(string $message, int $code = 400): string
    {
        http_response_code($code);
        return json_encode([
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 通用成功响应
     */
    private function successResponse($data = null, string $message = 'success'): string
    {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        return json_encode($response);
    }
}
