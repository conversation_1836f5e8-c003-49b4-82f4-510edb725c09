<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\DB;

class AuthController
{
    public function loginPage(): string
    {
        if (!empty($_SESSION['user'])) {
            // 根据请求类型跳转到不同页面
            if (class_exists('\App\Core\RouteDispatcher') && \App\Core\RouteDispatcher::isAdmin()) {
                $redirectUrl = $this->buildRedirectUrl('admin/');
                header('Location: ' . $redirectUrl);
            } else {
                $redirectUrl = $this->buildRedirectUrl('');
                header('Location: ' . $redirectUrl);
            }
            exit;
        }

        // 从 Cookie 读取记住的账号和密码
        $rememberedUsername = $_COOKIE['remember_username'] ?? '';
        $rememberedPassword = $_COOKIE['remember_password'] ?? '';

        // 判断是否为管理后台登录
        $isAdminLogin = class_exists('\App\Core\RouteDispatcher') && \App\Core\RouteDispatcher::isAdmin();

        ob_start();
        if ($isAdminLogin) {
            $title = '管理后台登录 - 设备资料录入管理系统';
            include __DIR__ . '/../Views/admin/login.php';
        } else {
            $title = '登录 - 设备资料录入管理系统';
            $bodyClass = 'login-page';
            include __DIR__ . '/../Views/auth/login.php';
        }
        return ob_get_clean();
    }

    public function login(): string
    {
        // 只处理 POST 请求，GET 请求应该调用 loginPage
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return $this->loginPage();
        }

        $u = trim($_POST['username'] ?? '');
        $p = (string)($_POST['password'] ?? '');
        if ($u === '' || $p === '') {
            // 返回登录页面并显示错误
            ob_start();
            $isAdminLogin = isset($_SERVER['IS_ADMIN_REQUEST']) && $_SERVER['IS_ADMIN_REQUEST'];
            if ($isAdminLogin) {
                $title = '管理后台登录 - 设备资料录入管理系统';
                $error = '请填写账号和密码';
                $rememberedUsername = $u;
                $rememberedPassword = '';
                include __DIR__ . '/../Views/admin/login.php';
            } else {
                $title = '登录 - 设备资料录入管理系统';
                $error = '请填写账号和密码';
                $rememberedUsername = $u;
                $rememberedPassword = '';
                include __DIR__ . '/../Views/auth/login.php';
            }
            return ob_get_clean();
        }

        $pdo = DB::conn();
        $stmt = $pdo->prepare('SELECT id,username,password_hash,real_name FROM users WHERE username=? LIMIT 1');
        $stmt->execute([$u]);
        $row = $stmt->fetch();
        if (!$row || !password_verify($p, $row['password_hash'])) {
            // 返回登录页面并显示错误
            ob_start();
            $isAdminLogin = isset($_SERVER['IS_ADMIN_REQUEST']) && $_SERVER['IS_ADMIN_REQUEST'];
            if ($isAdminLogin) {
                $title = '管理后台登录 - 设备资料录入管理系统';
                $error = '账号或密码不正确';
                $rememberedUsername = $u;
                $rememberedPassword = '';
                include __DIR__ . '/../Views/admin/login.php';
            } else {
                $title = '登录 - 设备资料录入管理系统';
                $error = '账号或密码不正确';
                $rememberedUsername = $u;
                $rememberedPassword = '';
                include __DIR__ . '/../Views/auth/login.php';
            }
            return ob_get_clean();
        }

        // 加载角色（在创建session之前检查权限）
        $roles = $pdo->prepare('SELECT r.code FROM roles r JOIN user_role ur ON ur.role_id=r.id WHERE ur.user_id=?');
        $roles->execute([$row['id']]);
        $userRoles = array_map(fn($x)=>$x['code'],$roles->fetchAll());

        // 如果是管理后台登录，检查是否有admin权限
        if (isset($_SERVER['IS_ADMIN_REQUEST']) && $_SERVER['IS_ADMIN_REQUEST']) {
            if (!in_array('admin', $userRoles)) {
                // 没有管理员权限，返回登录页面并显示错误
                ob_start();
                $title = '管理后台登录 - 设备资料录入管理系统';
                $error = '您没有管理后台访问权限';
                $rememberedUsername = $u;
                $rememberedPassword = '';
                include __DIR__ . '/../Views/admin/login.php';
                return ob_get_clean();
            }
        }

        // 验证通过，创建session
        $_SESSION['user'] = ['id'=>(int)$row['id'],'username'=>$row['username'],'real_name'=>$row['real_name']];
        $_SESSION['roles'] = $userRoles;

        // 处理记住功能
        $rememberUsername = !empty($_POST['remember_username']);
        $rememberPassword = !empty($_POST['remember_password']);

        if ($rememberUsername) {
            // 记住账号 30 天
            setcookie('remember_username', $u, time() + (30 * 24 * 3600), '/bbgl/', '', false, true);
        } else {
            // 清除记住的账号
            setcookie('remember_username', '', time() - 3600, '/bbgl/', '', false, true);
        }

        if ($rememberPassword) {
            // 记住密码 30 天（注意：实际项目中不建议明文存储密码）
            setcookie('remember_password', $p, time() + (30 * 24 * 3600), '/bbgl/', '', false, true);
        } else {
            // 清除记住的密码
            setcookie('remember_password', '', time() - 3600, '/bbgl/', '', false, true);
        }

        // 根据请求来源决定重定向位置
        if (isset($_SERVER['IS_ADMIN_REQUEST']) && $_SERVER['IS_ADMIN_REQUEST']) {
            // 管理员登录，重定向到系统配置页面
            $redirectUrl = $this->buildRedirectUrl('index.php?r=sys/config');
            header('Location: ' . $redirectUrl);
        } else {
            // 普通用户登录，重定向到首页
            header('Location: ' . BASE_URL);
        }
        return '';
    }

    public function logout(): string
    {
        $_SESSION = [];
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params['path'], $params['domain'], $params['secure'], $params['httponly']
            );
        }
        session_destroy();
        $loginUrl = $this->buildRedirectUrl('index.php?r=auth/login');
        header('Location: ' . $loginUrl);
        return '';
    }

    /**
     * 构建重定向URL，自动适应任何部署环境
     */
    private function buildRedirectUrl(string $path): string
    {
        // 获取当前请求的基础信息
        $scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // 动态获取基础路径
        $basePath = $this->getBasePath();

        // 处理路径
        if (empty($path)) {
            return $scheme . '://' . $host . $basePath;
        }

        // 确保路径不以/开头（避免双斜杠）
        $path = ltrim($path, '/');

        return $scheme . '://' . $host . $basePath . $path;
    }

    /**
     * 动态获取应用的基础路径，适应任何部署环境
     */
    private function getBasePath(): string
    {
        // 获取当前脚本的路径
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '/index.php';

        // 提取目录路径（去掉文件名）
        $basePath = dirname($scriptName);

        // 规范化路径
        $basePath = str_replace('\\', '/', $basePath); // Windows路径兼容
        $basePath = rtrim($basePath, '/'); // 移除末尾斜杠

        // 确保以斜杠结尾
        if ($basePath === '' || $basePath === '.') {
            return '/'; // 根目录部署
        }

        return $basePath . '/';
    }
}

