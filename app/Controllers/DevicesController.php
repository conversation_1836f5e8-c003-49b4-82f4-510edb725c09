<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;
use App\Services\AuditService;

class DevicesController
{
    /**
     * 设备管理页面
     */
    public function index(): string
    {
        Auth::requireLogin();
        // 检查管理员权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            http_response_code(403);
            return '权限不足：需要管理员权限';
        }

        ob_start();
        $title = '设备管理';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = '设备管理';
        include __DIR__ . '/../Views/layout/page_head.php';

        // 获取设备列表
        $pdo = DB::conn();
        $devices = $pdo->query("
            SELECT d.*,
                   GROUP_CONCAT(DISTINCT CONCAT(p.id,':',p.pump_no) ORDER BY p.pump_no SEPARATOR ',') as pumps,
                   COUNT(DISTINCT rd.id) as report_count
            FROM devices d
            LEFT JOIN pumps p ON d.id = p.device_id
            LEFT JOIN report_devices rd ON d.id = rd.device_id
            GROUP BY d.id
            ORDER BY d.name
        ")->fetchAll();

        // 解析泵数据
        foreach ($devices as &$device) {
            $device['pump_list'] = [];
            if ($device['pumps']) {
                foreach (explode(',', $device['pumps']) as $pump) {
                    $parts = explode(':', $pump);
                    $device['pump_list'][] = ['id' => (int)$parts[0], 'pump_no' => $parts[1]];
                }
            }
            unset($device['pumps']);
            $device['report_count'] = (int)$device['report_count'];
        }

        echo '<div class="card mb-3"><div class="card-body">';
        echo '<div class="d-flex justify-content-between align-items-center mb-3">';
        echo '<h5 class="mb-0">设备列表</h5>';
        echo '<button class="btn btn-primary btn-sm" onclick="BBGL.devices.showModal()">新增设备</button>';
        echo '</div>';

        echo '<div class="table-responsive">';
        echo '<table id="deviceTable" class="table table-hover table-sm">';
        echo '<thead><tr><th style="width:50px">ID</th><th style="width:120px">设备名称</th><th style="width:100px">设备编码</th><th style="width:80px">关联设备数</th><th style="width:80px">关联报表数</th><th style="width:60px">状态</th><th style="width:200px">操作</th></tr></thead>';
        echo '<tbody>';
        // 表格内容将通过JavaScript动态加载
        echo '</tbody></table>';
        echo '</div>';

        // 首屏渲染时，将服务端获取到的一次性列表塞到初始状态，避免空白与首个请求出错
        echo '<script>window.__DEVICES_INITIAL__ = ' . json_encode(['success'=>true,'data'=>$devices], JSON_UNESCAPED_UNICODE | JSON_HEX_QUOT | JSON_HEX_APOS) . ';</script>';
        echo '</div></div>';

        // 设备详情和泵管理区域
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<div class="card"><div class="card-body">';
        echo '<h5 class="mb-3">设备详情</h5>';
        echo '<div id="deviceDetail">';
        echo '<p class="text-muted">请从上方表格选择一个设备查看详情。</p>';
        echo '</div>';
        echo '</div></div>';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<div class="card"><div class="card-body">';
        echo '<h5 class="mb-3">设备管理</h5>';
        echo '<div id="pumpManagement">';
        echo '<p class="text-muted">请选择一个设备管理其关联的子设备。</p>';
        echo '</div>';
        echo '</div></div>';
        echo '</div>';
        echo '</div>';

        // 设备编辑模态框
        $this->renderDeviceModal();

        // 报表配置模态框
        $this->renderReportConfigModal();

        // 将 JavaScript 代码保存到变量中，避免 echo 语法问题
        $jsCode = $this->generateDeviceManagementJS();
        echo '<script>' . $jsCode . '</script>';

        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    private function renderDeviceModal(): void
    {
        echo '<div class="modal fade" id="deviceModal" tabindex="-1">';
        echo '<div class="modal-dialog">';
        echo '<div class="modal-content">';
        echo '<div class="modal-header">';
        echo '<h5 class="modal-title" id="deviceModalTitle">新增设备</h5>';
        echo '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>';
        echo '</div>';
        echo '<div class="modal-body">';
        echo '<form id="deviceForm">';
        echo '<input type="hidden" id="deviceId" name="id">';

        echo '<div class="mb-3">';
        echo '<label class="form-label">设备名称 *</label>';
        echo '<input type="text" class="form-control" id="deviceName" name="name" required>';
        echo '</div>';

        echo '<div class="mb-3">';
        echo '<label class="form-label">设备编码 *</label>';
        echo '<div class="input-group">';
        echo '<input type="text" class="form-control" id="deviceCode" name="code" required>';
        echo '<button type="button" class="btn btn-outline-secondary" onclick="BBGL.devices.generateCode()">自动生成</button>';
        echo '</div>';
        echo '<div class="form-text">格式：设备类型缩写 + 3位数字，如：COMP_001、FAN_001</div>';
        echo '</div>';



        echo '<div class="mb-3">';
        echo '<label class="form-label">设备描述</label>';
        echo '<textarea class="form-control" id="deviceDescription" name="description" rows="3"></textarea>';
        echo '</div>';

        echo '</form>';
        echo '</div>';
        echo '<div class="modal-footer">';
        echo '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>';
        echo '<button type="button" class="btn btn-primary" onclick="BBGL.devices.save()">保存</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    private function renderReportConfigModal(): void
    {
        echo '<div class="modal fade" id="reportConfigModal" tabindex="-1">';
        echo '<div class="modal-dialog modal-lg">';
        echo '<div class="modal-content">';
        echo '<div class="modal-header">';
        echo '<h5 class="modal-title" id="reportConfigModalTitle">设备报表配置</h5>';
        echo '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>';
        echo '</div>';
        echo '<div class="modal-body">';
        echo '<div id="reportConfigContent">';
        echo '<p class="text-muted">加载中...</p>';
        echo '</div>';
        echo '</div>';
        echo '<div class="modal-footer">';
        echo '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>';
        echo '<button type="button" class="btn btn-primary" onclick="BBGL.devices.saveReportConfig()">保存配置</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    // API: 获取设备详情
    public function getDevice(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        $deviceId = (int)($_GET['id'] ?? 0);
        if (!$deviceId) {
            echo json_encode(['success' => false, 'message' => '设备ID不能为空']);
            return '';
        }

        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT * FROM devices WHERE id=?");
        $stmt->execute([$deviceId]);
        $device = $stmt->fetch();

        if (!$device) {
            echo json_encode(['success' => false, 'message' => '设备不存在']);
            return '';
        }

        echo json_encode(['success' => true, 'data' => $device], JSON_UNESCAPED_UNICODE);
        return '';
    }

    // API: 删除设备
    public function deleteDevice(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $deviceId = (int)($input['id'] ?? 0);

        if (!$deviceId) {
            echo json_encode(['success' => false, 'message' => '设备ID不能为空']);
            return '';
        }

        $pdo = DB::conn();

        try {
            // 检查是否有关联的泵
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM pumps WHERE device_id=?");
            $stmt->execute([$deviceId]);
            $pumpCount = $stmt->fetchColumn();

            // 检查是否有关联的报表
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM report_devices WHERE device_id=?");
            $stmt->execute([$deviceId]);
            $reportCount = $stmt->fetchColumn();

            if ($pumpCount > 0 || $reportCount > 0) {
                echo json_encode(['success' => false, 'message' => "无法删除：设备关联了 {$pumpCount} 个泵和 {$reportCount} 个报表"]);
                return '';
            }

            $stmt = $pdo->prepare("DELETE FROM devices WHERE id=?");
            $stmt->execute([$deviceId]);

            echo json_encode(['success' => true, 'message' => '设备删除成功']);
        } catch (\Throwable $e) {
            echo json_encode(['success' => false, 'message' => '删除失败：' . $e->getMessage()]);
        }

        return '';
    }

    // API: 保存设备
    public function saveDevice(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $deviceId = (int)($input['id'] ?? 0);
        $name = trim($input['name'] ?? '');
        $code = trim($input['code'] ?? '');
        $description = trim($input['description'] ?? '');

        // 验证必填字段
        if (!$name || !$code) {
            echo json_encode(['success' => false, 'message' => '设备名称和编码不能为空']);
            return '';
        }

        $pdo = DB::conn();

        try {
            if ($deviceId) {
                // 更新
                $stmt = $pdo->prepare("UPDATE devices SET name=?, code=?, description=? WHERE id=?");
                $stmt->execute([$name, $code, $description, $deviceId]);
                $message = '设备更新成功';
            } else {
                // 新增
                $stmt = $pdo->prepare("INSERT INTO devices (name, code, description) VALUES (?, ?, ?)");
                $stmt->execute([$name, $code, $description]);
                $deviceId = $pdo->lastInsertId();
                $message = '设备创建成功';
            }

            echo json_encode(['success' => true, 'message' => $message, 'id' => $deviceId]);
        } catch (\Throwable $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo json_encode(['success' => false, 'message' => '设备编码已存在']);
            } else {
                echo json_encode(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
            }
        }

        return '';
    }

    /**
     * 获取所有设备列表 (API)
     */
    public function getDevicesList(): string
    {
        Auth::requireLogin();

        // 严格清理所有缓冲，避免多余输出破坏JSON
        while (ob_get_level() > 0) { ob_end_clean(); }

        header('Content-Type: application/json; charset=UTF-8');

        try {
            $pdo = DB::conn();
            $stmt = $pdo->query("
                SELECT d.*,
                       GROUP_CONCAT(DISTINCT CONCAT(p.id,':',p.pump_no) ORDER BY p.pump_no SEPARATOR ',') as pumps,
                       COUNT(DISTINCT rd.id) as report_count
                FROM devices d
                LEFT JOIN pumps p ON d.id = p.device_id
                LEFT JOIN report_devices rd ON d.id = rd.device_id
                GROUP BY d.id
                ORDER BY d.name
            ");
            $devices = $stmt->fetchAll();

            // 解析泵数据
            foreach ($devices as &$device) {
                $device['pump_list'] = [];
                if ($device['pumps']) {
                    foreach (explode(',', $device['pumps']) as $pump) {
                        $parts = explode(':', $pump);
                        $device['pump_list'][] = ['id' => (int)$parts[0], 'pump_no' => $parts[1]];
                    }
                }
                unset($device['pumps']);
                $device['report_count'] = (int)$device['report_count'];
            }

            $response = json_encode(['success' => true, 'data' => $devices], JSON_UNESCAPED_UNICODE);
            echo $response;
            return '';
        } catch (\Exception $e) {
            $response = json_encode(['success' => false, 'message' => $e->getMessage()]);
            echo $response;
            return '';
        }
    }

    /**
     * 列出设备的泵号
     */
    public function listPumps(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');
        $deviceId = (int)($_GET['device_id'] ?? 0);
        if (!$deviceId) { echo json_encode(['success'=>false,'message'=>'设备ID不能为空']); return ''; }
        $pdo = DB::conn();
        $stmt = $pdo->prepare('SELECT id, pump_no FROM pumps WHERE device_id = ? ORDER BY pump_no');
        $stmt->execute([$deviceId]);
        echo json_encode(['success'=>true,'data'=>$stmt->fetchAll()], JSON_UNESCAPED_UNICODE);
        return '';
    }

    /**
     * 为设备新增泵号
     */
    public function createPump(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success'=>false,'message'=>'权限不足']); return '';
        }
        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            $deviceId = (int)($input['device_id'] ?? 0);
            $pumpNo = trim($input['pump_no'] ?? '');
            if (!$deviceId || $pumpNo === '') { throw new \Exception('设备ID和泵号不能为空'); }
            $pdo = DB::conn();
            // 检查重复
            $stmt = $pdo->prepare('SELECT id FROM pumps WHERE device_id=? AND pump_no=?');
            $stmt->execute([$deviceId, $pumpNo]);
            if ($stmt->fetch()) { throw new \Exception('该泵号已存在'); }
            // 插入
            $pdo->prepare('INSERT INTO pumps (device_id, pump_no) VALUES (?, ?)')->execute([$deviceId,$pumpNo]);
            $pumpId = $pdo->lastInsertId();
            AuditService::log('pumps.create', 'pump:'.$pumpId, ['device_id'=>$deviceId,'pump_no'=>$pumpNo]);
            echo json_encode(['success'=>true,'id'=>$pumpId,'message'=>'泵号添加成功']);
        } catch (\Exception $e) {
            echo json_encode(['success'=>false,'message'=>$e->getMessage()]);
        }
        return '';
    }

    /**
     * 删除设备上的泵号
     */
    public function deletePump(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success'=>false,'message'=>'权限不足']); return '';
        }
        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            $pumpId = (int)($input['id'] ?? 0);
            if (!$pumpId) { throw new \Exception('泵ID不能为空'); }
            $pdo = DB::conn();
            // 检查报表关联
            $stmt = $pdo->prepare('SELECT COUNT(*) FROM report_devices WHERE pump_id=?');
            $stmt->execute([$pumpId]);
            if ($stmt->fetchColumn() > 0) { throw new \Exception('该泵已被报表配置引用，无法删除'); }
            // 获取泵信息
            $stmt = $pdo->prepare('SELECT device_id, pump_no FROM pumps WHERE id=?');
            $stmt->execute([$pumpId]);
            $pump = $stmt->fetch();
            if (!$pump) { throw new \Exception('泵不存在'); }
            // 删除
            $pdo->prepare('DELETE FROM pumps WHERE id=?')->execute([$pumpId]);
            AuditService::log('pumps.delete', 'pump:'.$pumpId, $pump);
            echo json_encode(['success'=>true,'message'=>'删除成功']);
        } catch (\Exception $e) {
            echo json_encode(['success'=>false,'message'=>$e->getMessage()]);
        }
        return '';
    }

    /**
     * 创建新设备
     */
    public function create(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        // 检查权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            $name = trim($input['name'] ?? '');
            $code = trim($input['code'] ?? '');
            $description = trim($input['description'] ?? '');

            // 验证必填字段
            if (empty($name) || empty($code)) {
                throw new \Exception('设备名称和编码不能为空');
            }

            $pdo = DB::conn();

            // 检查编码是否已存在
            $stmt = $pdo->prepare("SELECT id FROM devices WHERE code = ?");
            $stmt->execute([$code]);
            if ($stmt->fetch()) {
                throw new \Exception('设备编码已存在');
            }

            // 插入设备
            $stmt = $pdo->prepare("INSERT INTO devices (name, code, description) VALUES (?, ?, ?)");
            $stmt->execute([$name, $code, $description]);
            $deviceId = $pdo->lastInsertId();

            // 记录审计日志
            AuditService::log('devices.create', 'device:' . $deviceId, [
                'name' => $name,
                'code' => $code,
                'description' => $description
            ]);

            echo json_encode(['success' => true, 'message' => '设备创建成功', 'id' => $deviceId]);
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }

        return '';
    }

    /**
     * 更新设备
     */
    public function update(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        // 检查权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            $deviceId = (int)($input['id'] ?? 0);
            $name = trim($input['name'] ?? '');
            $code = trim($input['code'] ?? '');
            $description = trim($input['description'] ?? '');

            // 验证必填字段
            if (!$deviceId || empty($name) || empty($code)) {
                throw new \Exception('设备ID、名称和编码不能为空');
            }

            $pdo = DB::conn();

            // 检查设备是否存在
            $stmt = $pdo->prepare("SELECT type, name, code, description FROM devices WHERE id = ?");
            $stmt->execute([$deviceId]);
            $oldDevice = $stmt->fetch();
            if (!$oldDevice) {
                throw new \Exception('设备不存在');
            }

            // 检查编码是否与其他设备冲突
            $stmt = $pdo->prepare("SELECT id FROM devices WHERE code = ? AND id != ?");
            $stmt->execute([$code, $deviceId]);
            if ($stmt->fetch()) {
                throw new \Exception('设备编码已被其他设备使用');
            }

            // 更新设备
            $stmt = $pdo->prepare("UPDATE devices SET name = ?, code = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $code, $description, $deviceId]);

            // 记录审计日志
            AuditService::log('devices.update', 'device:' . $deviceId, [
                'old' => $oldDevice,
                'new' => ['name' => $name, 'code' => $code, 'description' => $description]
            ]);

            echo json_encode(['success' => true, 'message' => '设备更新成功']);
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }

        return '';
    }

    /**
     * 删除设备
     */
    public function delete(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        // 检查权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            $deviceId = (int)($input['id'] ?? 0);

            if (!$deviceId) {
                throw new \Exception('设备ID不能为空');
            }

            $pdo = DB::conn();

            // 检查设备是否存在
            $stmt = $pdo->prepare("SELECT type, name, code FROM devices WHERE id = ?");
            $stmt->execute([$deviceId]);
            $device = $stmt->fetch();
            if (!$device) {
                throw new \Exception('设备不存在');
            }

            // 检查是否有关联的报表
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM report_devices WHERE device_id = ?");
            $stmt->execute([$deviceId]);
            $reportCount = $stmt->fetchColumn();
            if ($reportCount > 0) {
                throw new \Exception('该设备已关联报表，无法删除。请先解除报表关联。');
            }

            $pdo->beginTransaction();

            // 删除相关的泵记录
            $pdo->prepare("DELETE FROM pumps WHERE device_id = ?")->execute([$deviceId]);

            // 删除设备
            $pdo->prepare("DELETE FROM devices WHERE id = ?")->execute([$deviceId]);

            $pdo->commit();

            // 记录审计日志
            AuditService::log('devices.delete', 'device:' . $deviceId, [
                'type' => $device['type'],
                'name' => $device['name'],
                'code' => $device['code']
            ]);

            echo json_encode(['success' => true, 'message' => '设备删除成功']);
        } catch (\Exception $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }

        return '';
    }

    /**
     * 生成设备管理页面的JavaScript代码（IIFE模块化）
     */
    private function generateDeviceManagementJS(): string
    {
        $baseUrl = BASE_URL;
        return <<<JS
// 设备管理模块 - IIFE封装，避免全局污染
(function() {
    'use strict';

    // API配置
    const API = {
        listDevices: "{$baseUrl}index.php?r=api/devices",
        getDevice: "{$baseUrl}index.php?r=devices/get",
        saveDevice: "{$baseUrl}index.php?r=devices/save",
        deleteDevice: "{$baseUrl}index.php?r=devices/delete",
        createDevice: "{$baseUrl}index.php?r=api/devices/create",
        updateDevice: "{$baseUrl}index.php?r=api/devices/update",
        listPumps: "{$baseUrl}index.php?r=api/devices/pumps",
        createPump: "{$baseUrl}index.php?r=api/devices/pumps/create",
        deletePump: "{$baseUrl}index.php?r=api/devices/pumps/delete",
        getReports: "{$baseUrl}index.php?r=api/reports/devices",
        saveReports: "{$baseUrl}index.php?r=api/reports/save-devices"
    };

    // 模块状态
    const state = {
        devicesList: []
    };



    // HTML转义函数
    function escapeHtml(text) {
        if (typeof text !== 'string') return text;
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function showMessage(message, type = "info") {
        const alertClass = {
            "success": "alert-success",
            "error": "alert-danger",
            "warning": "alert-warning",
            "info": "alert-info"
        }[type] || "alert-info";

        const alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">'
            + escapeHtml(message) + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';

        let container = document.getElementById("messageContainer");
        if (!container) {
            container = document.createElement("div");
            container.id = "messageContainer";
            container.style.position = "fixed";
            container.style.top = "80px";
            container.style.right = "20px";
            container.style.zIndex = "9999";
            container.style.maxWidth = "400px";
            document.body.appendChild(container);
        }

        container.innerHTML = alertHtml;

        if (type !== "error") {
            setTimeout(() => {
                const alert = container.querySelector(".alert");
                if (alert) {
                    alert.classList.remove("show");
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 150);
                }
            }, 3000);
        }
    }

    // 加载设备列表
    function loadDeviceList() {
        fetch(API.listDevices)
            .then(response => response.json())
            .then(result => {
                if (!result || !result.success || !Array.isArray(result.data)) {
                    console.error("设备列表API返回异常:", result);
                    showMessage("接口返回异常，请稍后重试", "error");
                    return;
                }
                state.devicesList = result.data;
                renderDeviceList(state.devicesList);
            })
            .catch(error => {
                console.error("加载设备列表失败:", error);
                showMessage("加载设备列表失败", "error");
            });
    }

    // 渲染设备列表
    function renderDeviceList(devices) {
        const tbody = document.querySelector("#deviceTable tbody");
        if (!tbody) return;

        let html = "";
        if (!Array.isArray(devices)) {
            console.warn("renderDeviceList 传入的 devices 不是数组:", devices);
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        devices.forEach(device => {
            const pumpCount = device.pump_list ? device.pump_list.length : 0;

            html += '<tr>'
                + '<td>' + device.id + '</td>'
                + '<td>' + escapeHtml(device.name) + '</td>'
                + '<td><code>' + escapeHtml(device.code) + '</code></td>'
                + '<td>' + pumpCount + ' 个</td>'
                + '<td>' + (device.report_count || 0) + ' 个</td>'
                + '<td><span class="badge bg-success">正常</span></td>'
                + '<td>'
                +   '<button class="btn btn-sm btn-outline-primary me-1" onclick="BBGL.devices.edit(' + device.id + ')">编辑</button>'
                +   '<button class="btn btn-sm btn-outline-info me-1" onclick="BBGL.devices.managePumps(' + device.id + ')">管理设备</button>'
                +   '<button class="btn btn-sm btn-outline-warning me-1" onclick="BBGL.devices.manageReports(' + device.id + ')">报表配置</button>'
                +   (device.report_count === 0 ? '<button class="btn btn-sm btn-outline-danger" onclick="BBGL.devices.delete(' + device.id + ', \'' + escapeHtml(device.name) + '\')">删除</button>' : '<button class="btn btn-sm btn-outline-secondary" disabled title="已关联报表，无法删除">删除</button>')
                + '</td>'
              + '</tr>';
        });

        tbody.innerHTML = html;
    }

    // 显示设备模态框
    function showDeviceModal(deviceId = null) {
        const modal = new bootstrap.Modal(document.getElementById("deviceModal"));
        const form = document.getElementById("deviceForm");
        const title = document.getElementById("deviceModalTitle");

        if (deviceId) {
            title.textContent = "编辑设备";
            loadDevice(deviceId);
        } else {
            title.textContent = "新增设备";
            form.reset();
            document.getElementById("deviceId").value = "";
        }

        modal.show();
    }

    // 加载设备数据
    function loadDevice(deviceId) {
        fetch(API.getDevice + "&id=" + deviceId)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const device = result.data;
                    document.getElementById("deviceId").value = device.id;
                    document.getElementById("deviceName").value = device.name;
                    document.getElementById("deviceCode").value = device.code;
                    document.getElementById("deviceDescription").value = device.description || "";
                } else {
                    showMessage(result.message || "加载设备失败", "error");
                }
            })
            .catch(error => {
                console.error("加载设备失败:", error);
                showMessage("网络错误，加载失败", "error");
            });
    }

    // 保存设备
    function saveDevice() {
        const form = document.getElementById("deviceForm");
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // 验证必填字段
        if (!data.name || !data.code) {
            showMessage("请填写所有必填字段", "error");
            return;
        }

        const isEdit = !!data.id;
        const url = isEdit ? API.updateDevice : API.createDevice;

        fetch(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            credentials: "same-origin",
            body: JSON.stringify(data)
        })
        .then(r => r.json())
        .then(result => {
            if (result.success) {
                showMessage(isEdit ? "设备更新成功" : "设备创建成功", "success");
                bootstrap.Modal.getInstance(document.getElementById("deviceModal")).hide();
                loadDeviceList(); // 重新加载设备列表
            } else {
                showMessage(result.message || "保存失败", "error");
            }
        })
        .catch(error => {
            console.error("保存设备失败:", error);
            showMessage("网络错误，保存失败", "error");
        });
    }

    // 删除设备
    function deleteDevice(deviceId, deviceName) {
        if (!confirm('确定要删除设备 "' + deviceName + '" 吗？此操作不可恢复。')) {
            return;
        }

        fetch(API.deleteDevice, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            credentials: "same-origin",
            body: JSON.stringify({id: deviceId})
        })
        .then(r => r.json())
        .then(result => {
            if (result.success) {
                showMessage("设备删除成功", "success");
                loadDeviceList(); // 重新加载设备列表
            } else {
                showMessage(result.message || "删除失败", "error");
            }
        })
        .catch(error => {
            console.error("删除设备失败:", error);
            showMessage("网络错误，删除失败", "error");
        });
    }

    // 自动生成设备编码
    function generateDeviceCode() {
        const nameInput = document.getElementById("deviceName");
        const codeInput = document.getElementById("deviceCode");

        if (!nameInput || !codeInput) return;

        const deviceName = nameInput.value.trim();
        if (!deviceName) {
            showMessage("请先输入设备名称", "warning");
            return;
        }

        // 使用统一前缀
        const prefix = 'DEV';

        // 获取现有设备列表，找到最大编号
        const existingDevices = state.devicesList || [];
        const allDevices = existingDevices;

        let maxNumber = 0;
        allDevices.forEach(device => {
            const match = device.code.match(new RegExp(prefix + '_(\\d+)'));
            if (match) {
                const num = parseInt(match[1], 10);
                if (num > maxNumber) {
                    maxNumber = num;
                }
            }
        });

        const nextNumber = maxNumber + 1;
        const paddedNumber = nextNumber.toString().padStart(3, '0');
        const generatedCode = prefix + '_' + paddedNumber;

        codeInput.value = generatedCode;
        showMessage("已生成设备编码: " + generatedCode, "success");
    }

    // 管理泵
    function managePumps(deviceId) {
        const device = (state.devicesList || []).find(d => d.id == deviceId);
        if (!device) {
            showMessage("设备不存在", "error");
            return;
        }

        let html = ''
            + '<div class="mb-3">'
            +   '<h6>设备: ' + escapeHtml(device.name) + '</h6>'
            +   '<p class="text-muted small">管理此设备的子设备编号</p>'
            + '</div>'
            + '<div class="card">'
            +   '<div class="card-header"><h6 class="mb-0">添加新设备编号</h6></div>'
            +   '<div class="card-body">'
            +     '<div class="input-group">'
            +       '<input type="text" id="newPump_' + deviceId + '" class="form-control" placeholder="输入设备编号 (如: A, B, 1#, 2#)" maxlength="10">'
            +       '<button class="btn btn-primary" onclick="BBGL.devices.createPump(' + deviceId + ')">添加编号</button>'
            +     '</div>'
            +     '<div class="form-text">建议使用简短标识符，如：A、B、1#、2#等</div>'
            +   '</div>'
            + '</div>'
            + '<div class="card mt-3">'
            +   '<div class="card-header"><h6 class="mb-0">现有设备编号</h6></div>'
            +   '<div class="card-body">';

        if (device.pump_list && device.pump_list.length > 0) {
            html += '<div class="d-flex flex-wrap gap-2">';
            device.pump_list.forEach(pump => {
                html += '<span class="badge bg-secondary fs-6 position-relative">'
                     + escapeHtml(pump.pump_no)
                     + '<button type="button" class="btn-close btn-close-white ms-2"'
                     + ' onclick="BBGL.devices.deletePump(' + pump.id + ', ' + deviceId + ')"'
                     + ' style="font-size: 0.7em;"'
                     + ' title="删除设备编号"></button>'
                     + '</span>';
            });
            html += '</div>';
        } else {
            html += '<div class="text-muted">暂无设备编号，请添加</div>';
        }

        html += '</div></div>';

        document.getElementById("pumpManagement").innerHTML = html;
    }

    // 创建设备编号
    function createPump(deviceId) {
        const input = document.getElementById("newPump_" + deviceId);
        const val = (input && input.value || "").trim();
        if (!val) {
            alert("请输入设备编号");
            return;
        }

        fetch(API.createPump, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            credentials: "same-origin",
            body: JSON.stringify({
                device_id: deviceId,
                pump_no: val
            })
        })
        .then(r => r.json())
        .then(data => {
            if (data.success) {
                input.value = "";
                // 刷新内存状态并重绘
                state.devicesList = (state.devicesList || []).map(d => {
                    if (d.id == deviceId) {
                        const newPump = {id: data.id || 0, pump_no: val};
                        const list = Array.isArray(d.pump_list) ? d.pump_list.slice() : [];
                        list.push(newPump);
                        return {...d, pump_list: list};
                    }
                    return d;
                });
                renderDeviceList(state.devicesList);
                managePumps(deviceId);
                showMessage("设备编号添加成功", "success");
            } else {
                alert(data.message || "添加失败");
            }
        })
        .catch(err => {
            console.error(err);
            alert("网络错误");
        });
    }

    // 删除设备编号
    function deletePump(pumpId, deviceId) {
        if (!confirm("确定删除该设备编号吗？")) return;

        fetch(API.deletePump, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            credentials: "same-origin",
            body: JSON.stringify({id: pumpId})
        })
        .then(r => r.json())
        .then(data => {
            if (data.success) {
                // 从内存状态移除该泵
                state.devicesList = (state.devicesList || []).map(d => {
                    if (d.id == deviceId) {
                        const list = (d.pump_list || []).filter(p => p.id != pumpId);
                        return {...d, pump_list: list};
                    }
                    return d;
                });
                renderDeviceList(state.devicesList);
                managePumps(deviceId);
                showMessage("设备编号删除成功", "success");
            } else {
                alert(data.message || "删除失败");
            }
        })
        .catch(err => {
            console.error(err);
            alert("网络错误");
        });
    }

    // 管理报表
    function manageReports(deviceId) {
        const device = (state.devicesList || []).find(d => d.id == deviceId);
        if (!device) {
            showMessage("设备不存在", "error");
            return;
        }

        let html = ''
            + '<div class="mb-3">'
            +   '<h6>设备详情</h6>'
            +   '<p class="text-muted small">设备基本信息和报表关联</p>'
            + '</div>'
            + '<div class="card">'
            +   '<div class="card-body">'
            +     '<h6 class="card-title">' + escapeHtml(device.name) + '</h6>'
            +     '<p class="card-text">'
            +       '<strong>设备编码：</strong><code>' + escapeHtml(device.code) + '</code><br>'
            +       '<strong>关联设备数：</strong>' + (device.pump_list ? device.pump_list.length : 0) + ' 个<br>'
            +       '<strong>关联报表：</strong>' + (device.report_count || 0) + ' 个'
            +     '</p>'
            +     '<div class="mt-3">'
            +       '<h6>操作</h6>'
            +       '<div class="btn-group" role="group">'
            +         '<button class="btn btn-outline-primary btn-sm" onclick="BBGL.devices.edit(' + device.id + ')"><i class="fa fa-edit"></i> 编辑设备</button>'
            +         '<a href="{$baseUrl}index.php?r=sys/reports" class="btn btn-outline-info btn-sm"><i class="fa fa-cog"></i> 配置报表关联</a>'
            +       '</div>'
            +     '</div>'
            +   '</div>'
            + '</div>';

        document.getElementById("deviceDetail").innerHTML = html;
    }

    // 保存报表配置（占位函数）
    function saveReportConfig() {
        showMessage("报表配置功能正在开发中", "info");
    }

    // 页面初始化
    document.addEventListener("DOMContentLoaded", function() {
        // 确保BBGL命名空间存在
        window.BBGL = window.BBGL || {};
        window.BBGL.devices = {
            showModal: showDeviceModal,
            edit: showDeviceModal,
            save: saveDevice,
            delete: deleteDevice,
            generateCode: generateDeviceCode,
            managePumps: managePumps,
            createPump: createPump,
            deletePump: deletePump,
            manageReports: manageReports,
            saveReportConfig: saveReportConfig,
            loadList: loadDeviceList
        };

        if (window.__DEVICES_INITIAL__ && window.__DEVICES_INITIAL__.success && Array.isArray(window.__DEVICES_INITIAL__.data)) {
            state.devicesList = window.__DEVICES_INITIAL__.data;
            renderDeviceList(state.devicesList);
        } else {
            loadDeviceList();
        }
    });

})();
JS;
    }
}
