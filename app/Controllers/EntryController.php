<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;
use App\Services\PumpsService;

class EntryController
{
    public function site(): string { return $this->render('现场资料录入','录入 / 现场'); }
    public function cb26(): string { return $this->render('CB26资料录入','录入 / CB26'); }
    public function ctrl(): string { return $this->render('中控室资料录入','录入 / 中控室'); }

    // 统一录入页分发器：根据报表ID和模板类型打开对应录入界面
    public function report(): string
    {
        Auth::requireLogin();
        $reportId = (int)($_GET['id'] ?? 0);
        $deviceId = (int)($_GET['device_id'] ?? 0);
        if (!$reportId) {
            http_response_code(400);
            return '参数错误：缺少报表ID';
        }

        // 获取报表信息和模板信息
        $pdo = DB::conn();
        $stmt = $pdo->prepare("
            SELECT r.id, r.name, r.template_code, t.report_type
            FROM reports r
            LEFT JOIN report_templates t ON r.template_code = t.code
            WHERE r.id=? AND r.enabled=1
        ");
        $stmt->execute([$reportId]);
        $report = $stmt->fetch();

        if (!$report) {
            http_response_code(404);
            return '报表不存在或已禁用';
        }

        $templateCode = $report['template_code'] ?: 'pumps';
        $reportName = $report['name'];
        // 确保报表类型默认为daily，避免NULL值导致的问题
        $reportType = (!empty($report['report_type'])) ? $report['report_type'] : 'daily';

        // 统一入口：无论模板类型为何，一律使用统一渲染方法
        return $this->renderOptimizedEntry($reportId, $reportName, $templateCode, $reportType, $deviceId);
    }

    // 根据模板类型渲染录入页面
    private function renderByTemplate(int $reportId, string $reportName, string $templateCode, string $reportType, ?int $deviceId = null): string
    {
        // 优化：统一使用优化版渲染，减少代码重复
        return $this->renderOptimizedEntry($reportId, $reportName, $templateCode, $reportType, $deviceId);
    }

    // 优化的统一录入页面渲染（支持查询模式）
    public function renderOptimizedEntry(int $reportId, string $reportName, string $templateCode, string $reportType, ?int $deviceId = null, bool $isQueryMode = false): string
    {
        Auth::requireLogin();
        ob_start();

        // 如果指定了设备ID，获取设备名称用于标题
        $deviceName = '';
        if ($deviceId) {
            $pdo = DB::conn();
            $stmt = $pdo->prepare("SELECT name FROM devices WHERE id=?");
            $stmt->execute([$deviceId]);
            $deviceName = $stmt->fetchColumn() ?: '';
        }

        $title = $reportName . ($deviceName ? ' - ' . $deviceName : '') . ($isQueryMode ? ' - 查询' : '');
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';

        // 面包屑导航
        echo '<nav aria-label="breadcrumb" class="mb-3">';
        echo '<ol class="breadcrumb">';
        echo '<li class="breadcrumb-item"><a href="'.BASE_URL.'">首页</a></li>';
        if ($isQueryMode) {
            echo '<li class="breadcrumb-item">查询</li>';
            echo '<li class="breadcrumb-item">报表查询</li>';
        } else {
            echo '<li class="breadcrumb-item">录入</li>';
            echo '<li class="breadcrumb-item">报表录入</li>';
        }
        echo '<li class="breadcrumb-item active" aria-current="page">'.$reportName.'</li>';
        echo '</ol>';
        echo '</nav>';

        $pageTitle = $title;
        include __DIR__ . '/../Views/layout/page_head.php';

        // 设备选择和日期选择区域
        echo '<div class="card mb-3"><div class="card-body">';
        echo '<div class="row g-3">';
        echo '  <div class="col-sm-3"><label class="form-label">设备选择</label><select id="deviceSelect" class="form-select">';
        echo '    <option value="">请选择设备...</option>';
        echo '  </select></div>';

        if ($reportType === 'continuous') {
            // 连续报表：显示月份选择和分页控制
            echo '  <div class="col-sm-2"><label class="form-label">查看月份</label><input id="viewMonth" type="month" class="form-control" value="' . date('Y-m') . '"></div>';
            echo '  <div class="col-sm-2"><label class="form-label">显示范围</label><select id="viewRange" class="form-select">';
            echo '    <option value="month">当月</option>';
            echo '    <option value="year">全年</option>';
            echo '  </select></div>';
            echo '  <div class="col-sm-2"><label class="form-label">排序方式</label><select id="sortOrder" class="form-select">';
            echo '    <option value="desc">降序（最新在前）</option>';
            echo '    <option value="asc">升序（最早在前）</option>';
            echo '  </select></div>';
            echo '  <div class="col-sm-3 d-flex align-items-end">';
            echo '    <button id="btnQuery" class="btn btn-primary">查询数据</button>';
            echo '  </div>';
        } else {
            // 日报表：显示日期选择
            echo '  <div class="col-sm-3"><label class="form-label">日期</label><input id="entryDate" type="date" class="form-control" value="' . date('Y-m-d') . '"></div>';
            echo '  <div class="col-sm-5 d-flex align-items-end"><button id="btnQuery" class="btn btn-primary">查询数据</button></div>';
        }

        echo '</div>';
        echo '</div></div>';

        // 数据录入表格容器
        echo '<div class="card"><div class="card-body">';
        echo '<div id="entryTableContainer">';
        echo '<div class="text-center text-muted py-4">';
        echo '<i class="fa fa-info-circle fa-2x mb-2"></i>';

        if ($reportType === 'continuous') {
            echo '<p>请先选择设备和查看月份，然后点击"查询数据"加载记录列表</p>';
            echo '<p class="small">连续报表：每次运行记录一行，支持自定义日期时间</p>';
        } else {
            echo '<p>请先选择设备和日期，然后点击"查询数据"加载录入表格</p>';
            echo '<p class="small">日报表：按固定时间段录入数据</p>';
        }

        echo '</div>';
        echo '</div>';

        // 值班人员信息区域（放在表格内部底部）
        echo '<div id="dutyStaffContainer" style="display:none;"></div>';

        echo '</div></div>';

        // 获取报表配置
        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT time_limit_days, time_limit_type, duty_staff_config FROM reports WHERE id=?");
        $stmt->execute([$reportId]);
        $reportConfig = $stmt->fetch() ?: [];

        // 获取当前用户角色
        $userRoles = $_SESSION['roles'] ?? [];

        // 获取系统配置（安全处理，如果表不存在则使用默认值）
        $systemConfigs = ['show_entry_info' => true, 'show_modify_info' => true];
        try {
            $configStmt = $pdo->prepare("SELECT cfg_key, cfg_value FROM system_config WHERE cfg_key IN ('show_entry_info', 'show_modify_info')");
            $configStmt->execute();
            $configRows = $configStmt->fetchAll();
            foreach ($configRows as $row) {
                $value = $row['cfg_value'];
                // 尝试解析JSON（ConfigService会将布尔值存储为JSON）
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $systemConfigs[$row['cfg_key']] = (bool)$decoded;
                } else {
                    // 兼容字符串格式
                    $systemConfigs[$row['cfg_key']] = $value === '1' || $value === 'true';
                }
            }
        } catch (\Exception $e) {
            // 如果系统配置表不存在，使用默认值
            error_log("Failed to load system configs: " . $e->getMessage());
        }

        // 调试日志
        error_log("System configs loaded: " . json_encode($systemConfigs));

        // JavaScript 配置
        echo '<script>';
        echo 'window.__REPORT_API__ = {';
        echo '  reportId: ' . $reportId . ',';
        echo '  templateCode: "' . $templateCode . '",';
        echo '  reportType: "' . $reportType . '",';
        echo '  deviceId: ' . ($deviceId ?: 0) . ',';
        echo '  isQueryMode: ' . ($isQueryMode ? 'true' : 'false') . ',';

        // 如果指定了设备ID，在API URL中包含device_id参数进行过滤
        $getDevicesUrl = BASE_URL . 'index.php?r=api/reports/devices&report_id=' . $reportId;
        if ($deviceId) {
            $getDevicesUrl .= '&device_id=' . $deviceId;
        }
        echo '  getDevices: "' . $getDevicesUrl . '",';

        // 查询模式和录入模式使用相同的API端点
        echo '  loadData: "'.BASE_URL.'index.php?r=api/entry/load",';
        if ($isQueryMode) {
            echo '  saveData: null'; // 查询模式不需要保存功能
        } else {
            echo '  saveData: "'.BASE_URL.'index.php?r=api/entry/save"';
        }
        echo '};';

        // 传递用户角色信息
        echo 'window.__USER_ROLES__ = ' . json_encode($userRoles) . ';';

        // 传递系统配置信息
        echo 'window.__SYSTEM_CONFIG__ = {';
        echo '  show_entry_info: ' . ($systemConfigs['show_entry_info'] ?? true ? 'true' : 'false') . ',';
        echo '  show_modify_info: ' . ($systemConfigs['show_modify_info'] ?? true ? 'true' : 'false');
        echo '};';

        // 传递报表配置信息
        echo 'window.__REPORT_CONFIG__ = {';
        echo '  time_limit_days: ' . (int)($reportConfig['time_limit_days'] ?? 2) . ',';
        echo '  time_limit_type: "' . ($reportConfig['time_limit_type'] ?? 'day') . '",';

        // 值班人配置
        $dutyConfig = [];
        if (!empty($reportConfig['duty_staff_config'])) {
            $dutyConfig = json_decode($reportConfig['duty_staff_config'], true) ?: [];
        }
        echo '  duty_staff: ' . json_encode($dutyConfig) . '';
        echo '};';

        echo '</script>';

        // 始终加载entry-generic.js，连续报表功能通过额外的JS文件实现
        echo '<script src="'.BASE_URL.'js/entry-generic.js"></script>';

        // 如果是连续报表，额外加载连续报表的JS
        if ($reportType === 'continuous') {
            echo '<script src="'.BASE_URL.'js/entry-continuous.js"></script>';
        }

        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }




    // 通用设备录入页面渲染器
    private function renderGenericEntry(int $reportId, string $reportName, string $templateCode, string $templateName, ?int $deviceId = null): string
    {
        Auth::requireLogin();
        ob_start();

        // 如果指定了设备ID，获取设备名称用于标题
        $deviceName = '';
        if ($deviceId) {
            $pdo = DB::conn();
            $stmt = $pdo->prepare("SELECT name FROM devices WHERE id=?");
            $stmt->execute([$deviceId]);
            $deviceName = $stmt->fetchColumn() ?: '';
        }

        $title = $reportName . ($deviceName ? ' - ' . $deviceName : ' - ' . $templateName);
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';

        // 面包屑导航
        echo '<nav aria-label="breadcrumb" class="mb-3">';
        echo '<ol class="breadcrumb">';
        echo '<li class="breadcrumb-item"><a href="'.BASE_URL.'">首页</a></li>';
        echo '<li class="breadcrumb-item">录入</li>';
        echo '<li class="breadcrumb-item">报表录入</li>';
        echo '<li class="breadcrumb-item active" aria-current="page">'.$reportName.'</li>';
        echo '</ol>';
        echo '</nav>';

        $pageTitle = $reportName . ($deviceName ? ' - ' . $deviceName : ' - ' . $templateName);
        include __DIR__ . '/../Views/layout/page_head.php';

        // 设备选择和日期选择区域
        echo '<div class="card mb-3"><div class="card-body">';
        echo '<div class="row g-3">';
        echo '  <div class="col-sm-4"><label class="form-label">设备选择</label><select id="deviceSelect" class="form-select"><option value="">请选择设备...</option></select></div>';
        echo '  <div class="col-sm-3"><label class="form-label">日期</label><input id="entryDate" type="date" class="form-control" value="' . date('Y-m-d') . '"></div>';
        echo '  <div class="col-sm-5 d-flex align-items-end"><button id="btnQuery" class="btn btn-primary">查询数据</button></div>';
        echo '</div>';
        echo '</div></div>';

        // 数据录入表格容器
        echo '<div class="card"><div class="card-body">';
        echo '<div id="entryTableContainer">';
        echo '<div class="text-center text-muted py-4">';
        echo '<i class="fa fa-info-circle fa-2x mb-2"></i>';
        echo '<p>请先选择设备和日期，然后点击"查询数据"加载录入表格</p>';
        echo '<p class="small">模板类型：' . $templateName . ' (' . $templateCode . ')</p>';
        echo '</div>';
        echo '</div>';

        // 值班人员信息区域（放在表格内部底部）
        echo '<div id="dutyStaffContainer" style="display:none;"></div>';

        echo '</div></div>';

        // 获取报表配置信息（包括值班人配置）
        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT time_limit_days, time_limit_type, duty_staff_config FROM reports WHERE id=?");
        $stmt->execute([$reportId]);
        $reportConfig = $stmt->fetch() ?: [];

        // 获取当前用户角色
        $userRoles = $_SESSION['roles'] ?? [];

        // JavaScript API 配置
        echo '<script>';
        echo 'window.__REPORT_API__ = {';
        echo '  reportId: ' . $reportId . ',';
        echo '  templateCode: "' . $templateCode . '",';

        // 传递报表配置
        echo '  config: {';
        echo '    time_limit_days: ' . (int)($reportConfig['time_limit_days'] ?? 2) . ',';
        echo '    time_limit_type: "' . ($reportConfig['time_limit_type'] ?? 'day') . '"';
        echo '  },';

        // 如果指定了设备ID，在API URL中包含device_id参数
        $getDevicesUrl = BASE_URL . 'index.php?r=api/reports/devices&report_id=' . $reportId;
        if ($deviceId) {
            $getDevicesUrl .= '&device_id=' . $deviceId;
        }
        echo '  getDevices: "' . $getDevicesUrl . '",';
        echo '  loadData: "'.BASE_URL.'index.php?r=api/entry/load",';
        echo '  saveData: "'.BASE_URL.'index.php?r=api/entry/save"';
        echo '};';

        // 传递用户角色信息
        echo 'window.__USER_ROLES__ = ' . json_encode($userRoles) . ';';

        // 传递报表配置信息（供前端时间限制和值班人使用）
        echo 'window.__REPORT_CONFIG__ = {';
        echo '  time_limit_days: ' . (int)($reportConfig['time_limit_days'] ?? 2) . ',';
        echo '  time_limit_type: "' . ($reportConfig['time_limit_type'] ?? 'day') . '",';

        // 值班人配置
        $dutyConfig = [];
        if (!empty($reportConfig['duty_staff_config'])) {
            $dutyConfig = json_decode($reportConfig['duty_staff_config'], true) ?: [];
        }
        echo '  duty_staff: ' . json_encode($dutyConfig) . '';
        echo '};';

        // 开发环境调试模式
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            echo 'window.__DEBUG_MODE__ = true;';
        }

        echo '</script>';
        echo '<script src="'.BASE_URL.'js/entry-generic.js"></script>';

        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    private function render(string $titleText, string $crumb): string
    {
        Auth::requireLogin();
        ob_start();
        $title = $titleText;
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = $titleText;
        include __DIR__ . '/../Views/layout/page_head.php';
        echo '<div class="card"><div class="card-body">';
        echo '<div class="text-muted">即将根据开发文档渲染表单表格（含最右列备注）。</div>';
        echo '</div></div>';
        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }







    // API: 通用录入数据加载
    public function entryLoad(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        $reportId = (int)($_GET['report_id'] ?? 0);
        $deviceId = (int)($_GET['device_id'] ?? 0);
        $objectId = (int)($_GET['object_id'] ?? 0);
        $date = $_GET['date'] ?? date('Y-m-d');
        $month = $_GET['month'] ?? '';
        $range = $_GET['range'] ?? 'month';

        if (!$reportId || !$deviceId) {
            echo json_encode(['success' => false, 'message' => '缺少必要参数：report_id 和 device_id']);
            return '';
        }

        // 检查是否为连续报表类型
        if ($month) {
            // 连续报表：按月份查询
            return $this->loadContinuousEntries($reportId, $deviceId, $month, $range);
        }

        try {
            // 优化：使用单个查询获取报表和模板信息
            $pdo = DB::conn();
            $sql = "SELECT r.template_code, rt.code, rt.name as template_name, rt.fields_config
                    FROM reports r
                    LEFT JOIN report_templates rt ON r.template_code = rt.code
                    WHERE r.id = ? AND r.enabled = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$reportId]);
            $reportInfo = $stmt->fetch();

            if (!$reportInfo || !$reportInfo['template_code']) {
                echo json_encode(['success' => false, 'message' => '报表不存在或已禁用']);
                return '';
            }

            $templateCode = $reportInfo['template_code'];
            $templateMeta = null;
            if ($reportInfo['fields_config']) {
                $templateMeta = [
                    'code' => $reportInfo['code'],
                    'name' => $reportInfo['template_name'],
                    'fields_config' => $reportInfo['fields_config']
                ];
            } else {
                error_log("Warning: Template '{$templateCode}' not found in report_templates table for report {$reportId}");
            }

            // 根据模板类型调用相应的数据加载逻辑
            switch ($templateCode) {
                case 'pumps':
                    // 泵类设备：使用 object_id 作为 pump_id
                    if (!$objectId) {
                        echo json_encode(['success' => false, 'message' => '泵类设备需要指定 object_id（泵ID）']);
                        return '';
                    }
                    $data = PumpsService::loadEntries($reportId, $objectId, $date);
                    break;

                case 'compressor':
                case 'fan':
                case 'motor':
                case 'custom':
                    // 其他设备类型：使用通用加载逻辑
                    $data = $this->loadGenericEntries($reportId, $deviceId, $objectId, $date, $templateCode);
                    break;

                default:
                    echo json_encode(['success' => false, 'message' => '不支持的模板类型：' . $templateCode]);
                    return '';
            }

            // 加载值班人员信息
            // 对于泵类设备，需要将pump_id转换为device_id
            $actualDeviceIdForDuty = $deviceId;
            if ($templateCode === 'pumps' && $objectId) {
                // 通过pump_id查询对应的device_id
                $stmt = $pdo->prepare("SELECT device_id FROM pumps WHERE id = ?");
                $stmt->execute([$objectId]);
                $pumpDeviceId = $stmt->fetchColumn();
                if ($pumpDeviceId) {
                    $actualDeviceIdForDuty = (int)$pumpDeviceId;
                }
            }
            $dutyStaff = $this->loadDutyStaff($reportId, $actualDeviceIdForDuty, $date);

            $resp = ['success' => true, 'data' => $data, 'duty_staff' => $dutyStaff];
            if ($templateMeta) {
                $resp['template'] = [
                    'code' => $templateMeta['code'] ?? '',
                    'name' => $templateMeta['name'] ?? '',
                    'fields_config' => $templateMeta['fields_config'] ?? ''
                ];
            }
            echo json_encode($resp, JSON_UNESCAPED_UNICODE);
        } catch (\Throwable $e) {
            error_log("通用数据加载失败: " . $e->getMessage() . " 在文件 " . $e->getFile() . " 第 " . $e->getLine() . " 行");
            echo json_encode(['success' => false, 'message' => '数据加载失败：' . $e->getMessage()]);
        }

        return '';
    }

    // API: 通用录入数据保存
    public function entrySave(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $reportId = (int)($input['report_id'] ?? 0);
        $deviceId = (int)($input['device_id'] ?? 0);
        $objectId = (int)($input['object_id'] ?? 0);
        $date = $input['date'] ?? date('Y-m-d');
        $rows = $input['rows'] ?? [];
        $dutyStaff = $input['duty_staff'] ?? null;
        $userId = (int)($_SESSION['user']['id'] ?? 0);

        // 调试日志
        error_log("EntryController::entrySave - Input data: " . json_encode($input));

        // 连续报表的单条记录保存
        $entryId = (int)($input['id'] ?? 0);
        $entryDate = $input['entry_date'] ?? '';
        $startTime = $input['start_time'] ?? '';
        $endTime = $input['end_time'] ?? '';
        $action = $input['action'] ?? '';

        if (!$reportId || !$deviceId) {
            echo json_encode(['success' => false, 'message' => '缺少必要参数：report_id 和 device_id']);
            return '';
        }

        // 检查是否为连续报表的操作
        if ($action === 'delete' && $entryId) {
            return $this->deleteContinuousEntry($reportId, $deviceId, $entryId, $userId);
        }

        if ($entryDate && $startTime) {
            return $this->saveContinuousEntry($input, $userId);
        }

        try {
            // 获取报表模板类型和报表类型
            $pdo = DB::conn();
            $stmt = $pdo->prepare("SELECT r.template_code, t.report_type FROM reports r LEFT JOIN report_templates t ON r.template_code = t.code WHERE r.id=? AND r.enabled=1");
            $stmt->execute([$reportId]);
            $reportInfo = $stmt->fetch();

            if (!$reportInfo) {
                echo json_encode(['success' => false, 'message' => '报表不存在或已禁用']);
                return '';
            }

            $templateCode = $reportInfo['template_code'];
            $reportType = $reportInfo['report_type'];

            // 过滤空白行
            $filtered = [];
            foreach ($rows as $slot => $data) {
                if (!is_array($data)) continue;
                $hasValue = false;
                foreach ($data as $v) {
                    if ($v !== '' && $v !== null) {
                        $hasValue = true;
                        break;
                    }
                }
                if ($hasValue) $filtered[$slot] = $data;
            }

            // 根据报表类型选择保存逻辑
            error_log("EntryController::entrySave - Template code: $templateCode, Report type: $reportType");
            if ($reportType === 'continuous') {
                // 连续报表：检查是否有entries数组（新格式）
                $entries = $input['entries'] ?? [];
                error_log("EntryController::entrySave - Continuous entries: " . json_encode($entries));
                if (!empty($entries)) {
                    $result = $this->saveContinuousEntries($reportId, $deviceId, $date, $entries, $userId, $templateCode);
                } else {
                    // 兼容旧格式
                    $result = $this->saveContinuousEntries($reportId, $deviceId, $date, $filtered, $userId, $templateCode);
                }
            } else {
                // 通用录入使用通用保存逻辑
                $result = $this->saveGenericEntries($reportId, $deviceId, $objectId, $date, $filtered, $userId, $templateCode);
            }

            // 保存值班人员信息
            if ($dutyStaff !== null) {
                // 对于泵类设备，需要将pump_id转换为device_id
                $actualDeviceId = $deviceId;
                if ($templateCode === 'pumps' && $objectId) {
                    // 通过pump_id查询对应的device_id
                    $stmt = $pdo->prepare("SELECT device_id FROM pumps WHERE id = ?");
                    $stmt->execute([$objectId]);
                    $pumpDeviceId = $stmt->fetchColumn();
                    if ($pumpDeviceId) {
                        $actualDeviceId = (int)$pumpDeviceId;
                    }
                }
                // 调试日志
                error_log("保存值班人员: reportId=$reportId, originalDeviceId=$deviceId, actualDeviceId=$actualDeviceId, objectId=$objectId, templateCode=$templateCode");
                $this->saveDutyStaff($reportId, $actualDeviceId, $date, $dutyStaff, $userId);
            }

            echo json_encode($result);
        } catch (\Throwable $e) {
            echo json_encode(['success' => false, 'message' => '数据保存失败：' . $e->getMessage()]);
        }

        return '';
    }

    // 通用数据加载逻辑（非泵类设备）
    private function loadGenericEntries(int $reportId, int $deviceId, int $objectId, string $date, string $templateCode): array
    {
        $pdo = DB::conn();

        // 对于非泵类设备，object_id 通常等于 device_id
        $actualObjectId = $objectId ?: $deviceId;

        try {
            $stmt = $pdo->prepare("SELECT e.time_slot_start, e.time_slot_end, e.data_json,
                                         e.created_by, e.created_at, e.updated_by, e.updated_at,
                                         u1.username as created_by_name, u2.username as updated_by_name
                                  FROM report_entries e
                                  LEFT JOIN users u1 ON e.created_by = u1.id
                                  LEFT JOIN users u2 ON e.updated_by = u2.id
                                  WHERE e.report_id=? AND e.object_id=? AND e.entry_date=?
                                  ORDER BY e.time_slot_start");
            $stmt->execute([$reportId, $actualObjectId, $date]);
            $rows = $stmt->fetchAll();


        } catch (\Exception $e) {
            // 如果查询失败，回退到简单查询
            error_log("用户信息查询失败，回退到简单查询: " . $e->getMessage());
            $stmt = $pdo->prepare("SELECT time_slot_start, time_slot_end, data_json,
                                         created_by, created_at, updated_by, updated_at
                                  FROM report_entries
                                  WHERE report_id=? AND object_id=? AND entry_date=?
                                  ORDER BY time_slot_start");
            $stmt->execute([$reportId, $actualObjectId, $date]);
            $rows = $stmt->fetchAll();
            // 为每行添加空的用户名
            foreach ($rows as &$row) {
                $row['created_by_name'] = null;
                $row['updated_by_name'] = null;
            }
        }

        $map = [];
        foreach ($rows as $r) {
            $key = substr($r['time_slot_start'], 0, 5) . '-' . substr($r['time_slot_end'], 0, 5);
            $data = json_decode($r['data_json'], true) ?: [];

            // 添加录入信息和修改信息
            $data['_meta'] = [
                'created_by' => $r['created_by'],
                'created_by_name' => $r['created_by_name'],
                'created_at' => $r['created_at'],
                'updated_by' => $r['updated_by'],
                'updated_by_name' => $r['updated_by_name'],
                'updated_at' => $r['updated_at']
            ];



            $map[$key] = $data;
        }

        return $map;
    }

    // 通用数据保存逻辑（连续运行设备）
    private function saveGenericEntries(int $reportId, int $deviceId, int $objectId, string $date, array $rows, int $userId, string $templateCode): array
    {
        $pdo = DB::conn();
        $errors = [];
        $saved = 0;

        // 处理设备ID和泵ID的关系
        $actualDeviceId = null;
        $pumpId = null;
        $actualObjectId = $objectId ?: $deviceId;

        // 检查传入的deviceId是否是pump_id
        $stmt = $pdo->prepare("SELECT id, device_id FROM pumps WHERE id = ?");
        $stmt->execute([$deviceId]);
        $pumpInfo = $stmt->fetch();

        if ($pumpInfo) {
            // 传入的是pump_id
            $pumpId = $deviceId;
            $actualDeviceId = (int)$pumpInfo['device_id'];
            $actualObjectId = $objectId ?: $deviceId;
        } else {
            // 传入的是device_id
            $actualDeviceId = $deviceId;
            $actualObjectId = $objectId ?: $deviceId;
        }

        // 获取报表时间限制
        $stmt = $pdo->prepare("SELECT time_limit_days FROM reports WHERE id=?");
        $stmt->execute([$reportId]);
        $timeLimitDays = (int)($stmt->fetchColumn() ?: 2);

        // 检查用户角色（管理员不受时间限制）
        $isAdmin = !empty($_SESSION['roles']) && in_array('admin', $_SESSION['roles']);

        $pdo->beginTransaction();
        try {
            foreach ($rows as $slot => $data) {
                // slot 形如 00:00-02:00
                [$s, $e] = explode('-', $slot);
                $slotStart = new \DateTime($date . ' ' . $s, new \DateTimeZone('Asia/Shanghai'));
                $slotEnd = new \DateTime($date . ' ' . $e, new \DateTimeZone('Asia/Shanghai'));
                $now = new \DateTime('now', new \DateTimeZone('Asia/Shanghai'));

                // 校验1：禁止未来时段
                if ($slotEnd > $now) {
                    $errors[] = "时段 {$slot} 为未来时间，无法保存";
                    continue;
                }

                // 校验2：时间限制（非管理员）
                if (!$isAdmin) {
                    $limitDate = (clone $now)->sub(new \DateInterval("P{$timeLimitDays}D"));
                    if ($slotStart < $limitDate) {
                        $errors[] = "时段 {$slot} 超出可录入时间限制（{$timeLimitDays}天）";
                        continue;
                    }
                }

                // upsert：先删后插，使用 object_id 字段
                $pdo->prepare("DELETE FROM report_entries
                              WHERE report_id=? AND object_id=? AND entry_date=? AND time_slot_start=? AND time_slot_end=?")
                    ->execute([$reportId, $actualObjectId, $date, $s . ':00', $e . ':00']);

                $pdo->prepare("INSERT INTO report_entries(report_id, device_id, pump_id, object_id, entry_date, time_slot_start, time_slot_end, data_json, created_by)
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)")
                    ->execute([$reportId, $actualDeviceId, $pumpId, $actualObjectId, $date, $s . ':00', $e . ':00', json_encode($data, JSON_UNESCAPED_UNICODE), $userId]);
                $saved++;
            }
            $pdo->commit();
            return ['success' => true, 'saved' => $saved, 'errors' => $errors];
        } catch (\Throwable $e) {
            $pdo->rollBack();
            throw $e;
        }
    }

    // 加载值班人员信息
    private function loadDutyStaff(int $reportId, int $deviceId, string $date): array
    {
        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT day_shift_staff, night_shift_staff
                              FROM report_duty_staff
                              WHERE report_id=? AND (device_id=? OR device_id IS NULL) AND entry_date=?
                              ORDER BY device_id DESC LIMIT 1");
        $stmt->execute([$reportId, $deviceId, $date]);
        $result = $stmt->fetch();

        return [
            'day_shift_staff' => $result['day_shift_staff'] ?? '',
            'night_shift_staff' => $result['night_shift_staff'] ?? ''
        ];
    }

    // 保存值班人员信息
    private function saveDutyStaff(int $reportId, int $deviceId, string $date, array $dutyStaff, int $userId): void
    {
        $pdo = DB::conn();

        $dayShift = trim($dutyStaff['day_shift_staff'] ?? '');
        $nightShift = trim($dutyStaff['night_shift_staff'] ?? '');

        // 如果两个值班人都为空，删除记录
        if (empty($dayShift) && empty($nightShift)) {
            $stmt = $pdo->prepare("DELETE FROM report_duty_staff
                                  WHERE report_id=? AND device_id=? AND entry_date=?");
            $stmt->execute([$reportId, $deviceId, $date]);
            return;
        }

        // 否则插入或更新记录
        $stmt = $pdo->prepare("INSERT INTO report_duty_staff
                              (report_id, device_id, entry_date, day_shift_staff, night_shift_staff, created_by)
                              VALUES (?, ?, ?, ?, ?, ?)
                              ON DUPLICATE KEY UPDATE
                              day_shift_staff=VALUES(day_shift_staff),
                              night_shift_staff=VALUES(night_shift_staff),
                              updated_by=VALUES(created_by)");
        $stmt->execute([$reportId, $deviceId, $date, $dayShift, $nightShift, $userId]);
    }

    // 加载连续报表数据
    private function loadContinuousEntries(int $reportId, int $deviceId, string $month, string $range): string
    {
        try {
            $pdo = DB::conn();

            // 获取排序参数
            $sort = $_GET['sort'] ?? 'desc';
            $orderBy = $sort === 'asc' ? 'ASC' : 'DESC';

            // 获取报表和模板信息
            $stmt = $pdo->prepare("
                SELECT r.template_code, t.fields_config, t.report_type
                FROM reports r
                LEFT JOIN report_templates t ON r.template_code = t.code
                WHERE r.id = ?
            ");
            $stmt->execute([$reportId]);
            $reportInfo = $stmt->fetch();

            if (!$reportInfo || $reportInfo['report_type'] !== 'continuous') {
                echo json_encode(['success' => false, 'message' => '报表类型不匹配']);
                return '';
            }

            // 解析模板字段
            $fieldsConfig = json_decode($reportInfo['fields_config'], true) ?: [];
            $templateFields = $fieldsConfig['fields'] ?? [];

            // 构建日期范围查询条件
            if ($range === 'year') {
                $year = substr($month, 0, 4);
                $startDate = $year . '-01-01';
                $endDate = $year . '-12-31';
            } else {
                $startDate = $month . '-01';
                $endDate = date('Y-m-t', strtotime($startDate)); // 月末日期
            }

            // 查询连续报表数据 - 修复泵类设备筛选问题
            // 对于泵类设备，需要同时检查device_id和pump_id字段
            $actualDeviceId = null;
            $pumpId = null;

            // 检查传入的deviceId是否是pump_id
            $stmt = $pdo->prepare("SELECT id, device_id FROM pumps WHERE id = ?");
            $stmt->execute([$deviceId]);
            $pumpInfo = $stmt->fetch();

            if ($pumpInfo) {
                // 传入的是pump_id
                $pumpId = $deviceId;
                $actualDeviceId = (int)$pumpInfo['device_id'];
            } else {
                // 传入的是device_id
                $actualDeviceId = $deviceId;
            }

            // 构建更精确的查询条件
            try {
                if ($pumpId) {
                    // 如果是泵ID，只查询该泵的记录
                    $stmt = $pdo->prepare("
                        SELECT e.*, u1.username as created_by_name, u2.username as updated_by_name
                        FROM report_entries e
                        LEFT JOIN users u1 ON e.created_by = u1.id
                        LEFT JOIN users u2 ON e.updated_by = u2.id
                        WHERE e.report_id = ?
                        AND (e.pump_id = ? OR e.object_id = ?)
                        AND e.entry_date BETWEEN ? AND ?
                        ORDER BY e.entry_date {$orderBy}, e.created_at {$orderBy}
                    ");
                    $stmt->execute([$reportId, $pumpId, $pumpId, $startDate, $endDate]);
                } else {
                    // 如果是设备ID，查询该设备下的所有记录
                    $stmt = $pdo->prepare("
                        SELECT e.*, u1.username as created_by_name, u2.username as updated_by_name
                        FROM report_entries e
                        LEFT JOIN users u1 ON e.created_by = u1.id
                        LEFT JOIN users u2 ON e.updated_by = u2.id
                        WHERE e.report_id = ?
                        AND e.device_id = ?
                        AND e.entry_date BETWEEN ? AND ?
                        ORDER BY e.entry_date {$orderBy}, e.created_at {$orderBy}
                    ");
                    $stmt->execute([$reportId, $actualDeviceId, $startDate, $endDate]);
                }
                $entries = $stmt->fetchAll();
            } catch (\Exception $e) {
                // 如果查询失败，回退到简单查询
                if ($pumpId) {
                    $stmt = $pdo->prepare("
                        SELECT e.*
                        FROM report_entries e
                        WHERE e.report_id = ?
                        AND (e.pump_id = ? OR e.object_id = ?)
                        AND e.entry_date BETWEEN ? AND ?
                        ORDER BY e.entry_date {$orderBy}, e.created_at {$orderBy}
                    ");
                    $stmt->execute([$reportId, $pumpId, $pumpId, $startDate, $endDate]);
                } else {
                    $stmt = $pdo->prepare("
                        SELECT e.*
                        FROM report_entries e
                        WHERE e.report_id = ?
                        AND e.device_id = ?
                        AND e.entry_date BETWEEN ? AND ?
                        ORDER BY e.entry_date {$orderBy}, e.created_at {$orderBy}
                    ");
                    $stmt->execute([$reportId, $actualDeviceId, $startDate, $endDate]);
                }
                $entries = $stmt->fetchAll();
                // 为每行添加空的用户名
                foreach ($entries as &$entry) {
                    $entry['created_by_name'] = null;
                    $entry['updated_by_name'] = null;
                }
            }

            // 解析field_data字段
            foreach ($entries as &$entry) {
                if (!empty($entry['field_data'])) {
                    $fieldData = json_decode($entry['field_data'], true) ?: [];
                    // 将字段数据合并到主记录中
                    foreach ($fieldData as $key => $value) {
                        $entry[$key] = $value;
                    }
                }
            }

            echo json_encode([
                'success' => true,
                'data' => $entries,
                'template_fields' => $templateFields,
                'date_range' => ['start' => $startDate, 'end' => $endDate]
            ], JSON_UNESCAPED_UNICODE);

        } catch (\Exception $e) {
            error_log("连续报表数据加载失败: " . $e->getMessage() . " 在文件 " . $e->getFile() . " 第 " . $e->getLine() . " 行");
            echo json_encode(['success' => false, 'message' => '加载数据失败：' . $e->getMessage()]);
        }

        return '';
    }

    // 保存连续报表单条记录
    private function saveContinuousEntry(array $input, int $userId): string
    {
        try {
            $pdo = DB::conn();

            $reportId = (int)($input['report_id'] ?? 0);
            $deviceId = (int)($input['device_id'] ?? 0);
            $entryId = (int)($input['id'] ?? 0);
            $entryDate = $input['entry_date'] ?? '';
            $startTime = $input['start_time'] ?? '';
            $endTime = $input['end_time'] ?? '';

            // 验证必填字段
            if (!$entryDate || !$startTime) {
                echo json_encode(['success' => false, 'message' => '日期和开始时间不能为空']);
                return '';
            }

            // 获取模板字段配置
            $stmt = $pdo->prepare("
                SELECT t.fields_config, t.report_type
                FROM reports r
                LEFT JOIN report_templates t ON r.template_code = t.code
                WHERE r.id = ?
            ");
            $stmt->execute([$reportId]);
            $templateInfo = $stmt->fetch();

            if (!$templateInfo || $templateInfo['report_type'] !== 'continuous') {
                echo json_encode(['success' => false, 'message' => '报表类型不匹配']);
                return '';
            }

            $fieldsConfig = json_decode($templateInfo['fields_config'], true) ?: [];
            $templateFields = $fieldsConfig['fields'] ?? [];

            // 构建字段数据
            $fieldData = [];
            foreach ($templateFields as $field) {
                $fieldKey = $field['key'];
                if (isset($input[$fieldKey])) {
                    $fieldData[$fieldKey] = $input[$fieldKey];
                }
            }

            // 处理设备ID和泵ID的关系
            $actualDeviceId = null;
            $pumpId = null;
            $objectId = null;

            // 检查传入的deviceId是否是pump_id
            $stmt = $pdo->prepare("SELECT id, device_id FROM pumps WHERE id = ?");
            $stmt->execute([$deviceId]);
            $pumpInfo = $stmt->fetch();

            if ($pumpInfo) {
                // 传入的是pump_id
                $pumpId = $deviceId;
                $objectId = $deviceId;
                $actualDeviceId = (int)$pumpInfo['device_id'];
            } else {
                // 传入的是device_id
                $actualDeviceId = $deviceId;
                $objectId = $deviceId; // 对于非泵设备，object_id等于device_id
            }

            $pdo->beginTransaction();

            if ($entryId) {
                // 更新现有记录
                $stmt = $pdo->prepare("
                    UPDATE report_entries
                    SET device_id=?, pump_id=?, object_id=?, entry_date=?,
                        time_slot_start=?, time_slot_end=?, field_data=?, data_json=?, updated_by=?, updated_at=NOW()
                    WHERE id=? AND report_id=?
                ");

                // 设置时间段
                $timeSlotStart = $startTime ?: '00:00:00';
                $timeSlotEnd = $endTime ?: '23:59:59';
                $fieldDataJson = json_encode($fieldData, JSON_UNESCAPED_UNICODE);
                $dataJson = '{}'; // 间歇运行设备的data_json设为空对象

                $stmt->execute([
                    $actualDeviceId, $pumpId, $objectId, $entryDate,
                    $timeSlotStart, $timeSlotEnd, $fieldDataJson, $dataJson,
                    $userId, $entryId, $reportId
                ]);
                $message = '记录更新成功';
            } else {
                // 插入新记录
                $stmt = $pdo->prepare("
                    INSERT INTO report_entries
                    (report_id, device_id, pump_id, object_id, entry_date,
                     time_slot_start, time_slot_end, field_data, data_json, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");

                // 设置时间段
                $timeSlotStart = $startTime ?: '00:00:00';
                $timeSlotEnd = $endTime ?: '23:59:59';
                $fieldDataJson = json_encode($fieldData, JSON_UNESCAPED_UNICODE);
                $dataJson = '{}'; // 间歇运行设备的data_json设为空对象

                $stmt->execute([
                    $reportId, $actualDeviceId, $pumpId, $objectId, $entryDate,
                    $timeSlotStart, $timeSlotEnd, $fieldDataJson, $dataJson, $userId
                ]);
                $entryId = $pdo->lastInsertId();
                $message = '记录保存成功';
            }

            $pdo->commit();

            echo json_encode([
                'success' => true,
                'message' => $message,
                'id' => $entryId
            ]);

        } catch (\Exception $e) {
            if (isset($pdo)) $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
        }

        return '';
    }

    // 删除连续报表记录
    private function deleteContinuousEntry(int $reportId, int $deviceId, int $entryId, int $userId): string
    {
        try {
            $pdo = DB::conn();

            // 验证记录是否存在且属于当前用户或管理员
            $stmt = $pdo->prepare("
                SELECT id, created_by
                FROM report_entries
                WHERE id=? AND report_id=? AND device_id=?
            ");
            $stmt->execute([$entryId, $reportId, $deviceId]);
            $entry = $stmt->fetch();

            if (!$entry) {
                echo json_encode(['success' => false, 'message' => '记录不存在']);
                return '';
            }

            // 检查权限：只有创建者或管理员可以删除
            $userRoles = $_SESSION['roles'] ?? [];
            $isAdmin = in_array('admin', $userRoles);

            if (!$isAdmin && $entry['created_by'] != $userId) {
                echo json_encode(['success' => false, 'message' => '权限不足：只能删除自己创建的记录']);
                return '';
            }

            // 删除记录
            $stmt = $pdo->prepare("DELETE FROM report_entries WHERE id=?");
            $stmt->execute([$entryId]);

            echo json_encode([
                'success' => true,
                'message' => '记录删除成功'
            ]);

        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => '删除失败：' . $e->getMessage()]);
        }

        return '';
    }

    /**
     * 保存连续报表记录（用于间歇运行设备）
     */
    private function saveContinuousEntries($reportId, $deviceId, $date, $entries, $userId, $templateCode) {
        try {
            $pdo = DB::conn();

            // 开始事务
            $pdo->beginTransaction();

            $successCount = 0;
            $errorMessages = [];

            foreach ($entries as $entry) {
                try {
                    // 检查是否有action字段（编辑操作）
                    if (isset($entry['action'])) {
                        if ($entry['action'] === 'add') {
                            // 新增记录
                            $this->insertContinuousEntry($pdo, $reportId, $deviceId, $entry, $userId, $templateCode);
                            $successCount++;
                        } elseif ($entry['action'] === 'update') {
                            // 更新记录
                            $this->updateContinuousEntry($pdo, $entry, $userId);
                            $successCount++;
                        } elseif ($entry['action'] === 'delete') {
                            // 删除记录
                            $result = $this->deleteContinuousEntry($reportId, $deviceId, $entry['id'], $userId);
                            if (strpos($result, '"success":false') !== false) {
                                throw new \Exception('删除记录失败');
                            }
                            $successCount++;
                        }
                    } else {
                        // 新增记录（没有action字段，默认为新增）
                        $this->insertContinuousEntry($pdo, $reportId, $deviceId, $entry, $userId, $templateCode);
                        $successCount++;
                    }
                } catch (\Exception $e) {
                    $errorMessages[] = $e->getMessage();
                }
            }

            if (empty($errorMessages)) {
                $pdo->commit();
                return [
                    'success' => true,
                    'message' => "成功处理 {$successCount} 条记录"
                ];
            } else {
                $pdo->rollback();
                return [
                    'success' => false,
                    'message' => '保存失败：' . implode('; ', $errorMessages)
                ];
            }

        } catch (\Exception $e) {
            if (isset($pdo)) {
                $pdo->rollback();
            }
            return [
                'success' => false,
                'message' => '保存失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 插入连续报表记录
     */
    private function insertContinuousEntry($pdo, $reportId, $deviceId, $entry, $userId, $templateCode) {
        // 准备字段数据
        $fieldData = $entry['field_data'] ?? [];
        $entryDate = $entry['entry_date'] ?? date('Y-m-d');
        $startTime = $entry['start_time'] ?? '';
        $endTime = $entry['end_time'] ?? '';

        // 处理设备ID和泵ID的关系
        $actualDeviceId = null;
        $pumpId = null;
        $objectId = null;

        // 检查传入的deviceId是否是pump_id
        $stmt = $pdo->prepare("SELECT id, device_id FROM pumps WHERE id = ?");
        $stmt->execute([$deviceId]);
        $pumpInfo = $stmt->fetch();

        if ($pumpInfo) {
            // 传入的是pump_id
            $pumpId = $deviceId;
            $objectId = $deviceId;
            $actualDeviceId = (int)$pumpInfo['device_id'];
        } else {
            // 传入的是device_id
            $actualDeviceId = $deviceId;
        }

        // 插入主记录 - 连续报表需要同时设置所有必需字段
        $stmt = $pdo->prepare("
            INSERT INTO report_entries (
                report_id, device_id, pump_id, object_id, entry_date,
                time_slot_start, time_slot_end, field_data, data_json,
                created_by, created_at, updated_by, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW())
        ");

        // 将字段数据作为JSON存储
        $fieldDataJson = json_encode($fieldData, JSON_UNESCAPED_UNICODE);

        // 为连续报表设置默认的时间段（如果没有具体时间，使用00:00-23:59）
        $timeSlotStart = $startTime ?: '00:00:00';
        $timeSlotEnd = $endTime ?: '23:59:59';

        // data_json字段也设置相同的数据（兼容性）
        $dataJson = $fieldDataJson;

        $stmt->execute([
            $reportId, $actualDeviceId, $pumpId, $objectId, $entryDate,
            $timeSlotStart, $timeSlotEnd, $fieldDataJson, $dataJson, $userId, $userId
        ]);

        $entryId = $pdo->lastInsertId();
        return $entryId;
    }

    /**
     * 更新连续报表记录
     */
    private function updateContinuousEntry($pdo, $entry, $userId) {
        $entryId = $entry['id'] ?? 0;
        if (!$entryId) {
            throw new \Exception('缺少记录ID');
        }

        // 检查记录是否存在
        $stmt = $pdo->prepare("SELECT id FROM report_entries WHERE id = ?");
        $stmt->execute([$entryId]);
        if (!$stmt->fetch()) {
            throw new \Exception('记录不存在');
        }

        // 更新主记录
        $fieldData = $entry['field_data'] ?? [];
        $entryDate = $entry['entry_date'] ?? date('Y-m-d');
        $startTime = $entry['start_time'] ?? '';
        $endTime = $entry['end_time'] ?? '';

        // 将字段数据作为JSON存储
        $fieldDataJson = json_encode($fieldData, JSON_UNESCAPED_UNICODE);

        // 为连续报表设置默认的时间段
        $timeSlotStart = $startTime ?: '00:00:00';
        $timeSlotEnd = $endTime ?: '23:59:59';

        $stmt = $pdo->prepare("
            UPDATE report_entries
            SET entry_date = ?, time_slot_start = ?, time_slot_end = ?,
                field_data = ?, data_json = ?, updated_by = ?, updated_at = NOW()
            WHERE id = ?
        ");

        $stmt->execute([$entryDate, $timeSlotStart, $timeSlotEnd, $fieldDataJson, $fieldDataJson, $userId, $entryId]);

        return $entryId;
    }

}

