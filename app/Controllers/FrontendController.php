<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

/**
 * 前端用户界面控制器
 * 专门为普通用户提供简化的录入和查询界面
 */
class FrontendController
{
    /**
     * 前端主页 - 用户端入口
     */
    public function index(): string
    {
        // 检查是否已登录，未登录显示登录页面
        if (!Auth::isLoggedIn()) {
            return $this->loginPage();
        }

        // 已登录用户显示主界面
        return $this->dashboard();
    }

    /**
     * 前端登录页面
     */
    public function loginPage(): string
    {
        ob_start();
        include __DIR__ . '/../Views/frontend/login.php';
        return ob_get_clean();
    }

    /**
     * 前端主界面（仪表板）
     */
    public function dashboard(): string
    {
        ob_start();
        include __DIR__ . '/../Views/frontend/dashboard.php';
        return ob_get_clean();
    }

    /**
     * 处理前端登录请求
     */
    public function login(): string
    {
        header('Content-Type: application/json; charset=UTF-8');

        try {
            $username = trim($_POST['username'] ?? '');
            $password = trim($_POST['password'] ?? '');

            if (empty($username) || empty($password)) {
                http_response_code(400);
                return json_encode(['success' => false, 'message' => '用户名和密码不能为空']);
            }

            // 使用现有的Auth类进行认证
            if (Auth::login($username, $password)) {
                return json_encode(['success' => true, 'message' => '登录成功']);
            } else {
                http_response_code(401);
                return json_encode(['success' => false, 'message' => '用户名或密码错误']);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '登录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理前端登出请求
     */
    public function logout(): string
    {
        header('Content-Type: application/json; charset=UTF-8');

        Auth::logout();
        return json_encode(['success' => true, 'message' => '已退出登录']);
    }

    /**
     * 设备类型页面 - 显示该类型的所有报表
     */
    public function device(): string
    {
        Auth::requireLogin();

        $deviceType = trim($_GET['type'] ?? '');
        $mode = trim($_GET['mode'] ?? 'entry'); // entry 或 query

        if (!$deviceType) {
            header('Location: ' . BASE_URL . 'index.php?r=frontend');
            return '';
        }

        ob_start();
        $title = $deviceType . ' - ' . ($mode === 'query' ? '数据查询' : '数据录入');

        include __DIR__ . '/../Views/frontend/header.php';

        echo '<main class="container-fluid py-4">';
        echo '<div class="row justify-content-center">';
        echo '<div class="col-lg-8">';

        // 页面标题
        echo '<div class="text-center mb-4">';
        echo '<h3 class="fw-bold text-primary">' . htmlspecialchars($deviceType) . '</h3>';
        echo '<p class="text-muted">' . ($mode === 'query' ? '数据查询' : '数据录入') . '</p>';
        echo '</div>';

        // 获取该设备类型的报表
        $reports = $this->getReportsForDeviceType($deviceType);
        $staticPages = $this->getStaticPagesInternal();
        $deviceTypeId = $this->getDeviceTypeId($deviceType);

        if (empty($reports)) {
            echo '<div class="alert alert-warning text-center">';
            echo '<h5>该设备类型暂无可用报表</h5>';
            echo '<p>请联系管理员配置报表</p>';
            echo '<a href="' . BASE_URL . 'index.php?r=frontend" class="btn btn-primary">返回首页</a>';
            echo '</div>';
        } else {
            echo '<div class="row g-3">';

            foreach ($reports as $report) {
                $reportId = $report['id'];
                $filePrefix = ($mode === 'query' ? 'query' : 'entry') . "_report_{$reportId}_devicetype_{$deviceTypeId}";
                $staticFile = $filePrefix . '.html';

                echo '<div class="col-md-6">';
                echo '<div class="card">';
                echo '<div class="card-body">';
                echo '<h6 class="card-title">' . htmlspecialchars($report['name']) . '</h6>';
                echo '<p class="card-text small text-muted">模板：' . htmlspecialchars($report['template_code']) . '</p>';

                if (isset($staticPages[$staticFile])) {
                    echo '<a href="' . BASE_URL . 'static_pages/' . $staticFile . '" class="btn btn-primary btn-sm" target="_blank">';
                    echo '<i class="fa fa-external-link me-1"></i>打开页面';
                    echo '</a>';
                    echo '<span class="badge bg-success ms-2">静态页面</span>';
                } else {
                    $dynamicUrl = BASE_URL . 'index.php?r=' . ($mode === 'query' ? 'query' : 'entry') . '/report&id=' . $reportId;
                    echo '<a href="' . $dynamicUrl . '" class="btn btn-outline-primary btn-sm">';
                    echo '<i class="fa fa-link me-1"></i>打开页面';
                    echo '</a>';
                    echo '<span class="badge bg-warning ms-2">动态页面</span>';
                }

                echo '</div>';
                echo '</div>';
                echo '</div>';
            }

            echo '</div>';
        }

        // 返回按钮
        echo '<div class="text-center mt-4">';
        echo '<a href="' . BASE_URL . 'index.php?r=frontend" class="btn btn-secondary">';
        echo '<i class="fa fa-arrow-left me-2"></i>返回首页';
        echo '</a>';
        echo '</div>';

        echo '</div>';
        echo '</div>';
        echo '</main>';

        include __DIR__ . '/../Views/frontend/footer.php';

        return ob_get_clean();
    }

    /**
     * 获取可用报表列表
     */
    private function getAvailableReports(): array
    {
        $pdo = DB::conn();
        $stmt = $pdo->query("
            SELECT r.id, r.name, r.template_code, t.report_type 
            FROM reports r 
            LEFT JOIN report_templates t ON r.template_code = t.code 
            WHERE r.enabled = 1 
            ORDER BY r.name
        ");
        return $stmt->fetchAll();
    }
    
    /**
     * 获取已生成的静态页面列表（内部使用）
     */
    private function getStaticPagesInternal(): array
    {
        $staticDir = __DIR__ . '/../../static_pages/';
        $staticPages = [];
        
        if (is_dir($staticDir)) {
            $files = glob($staticDir . '*.html');
            foreach ($files as $file) {
                $filename = basename($file);
                $staticPages[$filename] = [
                    'file' => $filename,
                    'size' => filesize($file),
                    'modified' => filemtime($file)
                ];
            }
        }
        
        return $staticPages;
    }

    /**
     * 获取所有设备类型
     */
    private function getDeviceTypes(): array
    {
        $pdo = DB::conn();
        $stmt = $pdo->query("SELECT DISTINCT name FROM devices ORDER BY name");
        $devices = $stmt->fetchAll();

        $deviceTypes = [];
        foreach ($devices as $device) {
            $deviceType = $this->extractDeviceType($device['name']);
            if ($deviceType && !in_array($deviceType, $deviceTypes)) {
                $deviceTypes[] = $deviceType;
            }
        }

        return $deviceTypes;
    }

    /**
     * 获取指定设备类型的报表
     */
    private function getReportsForDeviceType(string $deviceType): array
    {
        $pdo = DB::conn();
        $stmt = $pdo->prepare("
            SELECT DISTINCT r.id, r.name, r.template_code, t.report_type
            FROM reports r
            LEFT JOIN report_templates t ON r.template_code = t.code
            JOIN report_devices rd ON r.id = rd.report_id
            JOIN devices d ON rd.device_id = d.id
            WHERE r.enabled = 1 AND d.name LIKE ?
            ORDER BY r.name
        ");
        $stmt->execute(['%' . $deviceType . '%']);
        return $stmt->fetchAll();
    }

    /**
     * 从设备名称中提取设备类型
     */
    private function extractDeviceType(string $deviceName): string
    {
        // 移除数字前缀（如"1#"、"2#"等）
        $name = preg_replace('/^\d+#/', '', $deviceName);

        // 移除数字后缀
        $name = preg_replace('/\d+$/', '', $name);

        return trim($name);
    }

    /**
     * 获取前端菜单数据
     */
    public function getMenuData(): string
    {
        header('Content-Type: application/json; charset=UTF-8');

        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }

        try {
            $menuData = $this->buildMenuData();
            return json_encode(['success' => true, 'data' => $menuData]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取菜单失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取静态页面列表
     */
    public function getStaticPages(): string
    {
        header('Content-Type: application/json; charset=UTF-8');

        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }

        try {
            $staticPages = $this->scanStaticPages();
            return json_encode(['success' => true, 'data' => $staticPages]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取静态页面失败：' . $e->getMessage()]);
        }
    }

    /**
     * 构建菜单数据 - 只显示已生成静态页面的报表
     */
    public function buildMenuData(): array
    {
        $pdo = DB::conn();
        $userRoles = $_SESSION['roles'] ?? [];

        $menuData = [
            'entry' => [],
            'query' => [],
            'admin' => []
        ];

        // 扫描静态页面，获取已生成的报表和设备
        $staticPages = $this->scanStaticPages();
        $entryReportDevices = $this->parseStaticPages($staticPages, 'entry');
        $queryReportDevices = $this->parseStaticPages($staticPages, 'query');

        // 合并录入和查询的报表ID（用于获取报表信息）
        $allReportIds = array_unique(array_merge(array_keys($entryReportDevices), array_keys($queryReportDevices)));

        if (empty($allReportIds)) {
            return $menuData; // 没有静态页面，返回空菜单
        }

        // 获取报表数据（只查询有静态页面的报表）
        $placeholders = str_repeat('?,', count($allReportIds) - 1) . '?';

        $sql = "SELECT r.id, r.name, r.enabled, rc.name AS category_name
                FROM reports r
                LEFT JOIN report_categories rc ON r.category_id=rc.id
                WHERE r.enabled=1 AND r.id IN ($placeholders)
                ORDER BY r.id";
        $rows = $pdo->prepare($sql);
        $rows->execute($allReportIds);
        $reports = $rows->fetchAll() ?: [];

        // 按分组整理报表
        $reportsByGroup = ['site' => [], 'cb26' => [], 'ctrl' => []];
        foreach ($reports as $r) {
            $cat = (string)($r['category_name'] ?? '');
            $key = 'site';
            if (stripos($cat, 'cb26') !== false) {
                $key = 'cb26';
            } elseif (mb_strpos($cat, '中控') !== false || stripos($cat, 'ctrl') !== false) {
                $key = 'ctrl';
            }
            $reportsByGroup[$key][] = $r;
        }

        // 获取设备信息（分别处理录入和查询的设备）
        $entryDevicesByReport = [];
        $queryDevicesByReport = [];

        // 处理录入设备
        foreach ($entryReportDevices as $reportId => $deviceIds) {
            if (empty($deviceIds)) continue;

            $placeholders = str_repeat('?,', count($deviceIds) - 1) . '?';
            $sql = "SELECT d.id, d.name FROM devices d WHERE d.id IN ($placeholders) ORDER BY d.name";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($deviceIds);
            $devices = $stmt->fetchAll() ?: [];

            $entryDevicesByReport[$reportId] = $devices;
        }

        // 处理查询设备
        foreach ($queryReportDevices as $reportId => $deviceIds) {
            if (empty($deviceIds)) continue;

            $placeholders = str_repeat('?,', count($deviceIds) - 1) . '?';
            $sql = "SELECT d.id, d.name FROM devices d WHERE d.id IN ($placeholders) ORDER BY d.name";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($deviceIds);
            $devices = $stmt->fetchAll() ?: [];

            $queryDevicesByReport[$reportId] = $devices;
        }

        // 构建录入菜单（只显示有entry_静态页面的报表，并检查权限）
        foreach (['site', 'cb26', 'ctrl'] as $group) {
            // 检查用户是否有该分组的权限
            if (!$this->hasRoleAccess($userRoles, $group)) continue;
            if (empty($reportsByGroup[$group])) continue;

            $groupData = [
                'name' => $this->getGroupName($group) . '资料录入',
                'type' => 'entry',
                'group' => $group,
                'reports' => []
            ];

            foreach ($reportsByGroup[$group] as $report) {
                $reportId = $report['id'];
                $devices = $entryDevicesByReport[$reportId] ?? [];

                if (!empty($devices)) {
                    $reportData = [
                        'id' => $reportId,
                        'name' => $report['name'],
                        'devices' => $devices
                    ];
                    $groupData['reports'][] = $reportData;
                }
            }

            if (!empty($groupData['reports'])) {
                $menuData['entry'][] = $groupData;
            }
        }

        // 构建查询菜单（只显示有query_静态页面的报表，并检查权限）
        foreach (['site', 'cb26', 'ctrl'] as $group) {
            // 检查用户是否有该分组的权限
            if (!$this->hasRoleAccess($userRoles, $group)) continue;
            if (empty($reportsByGroup[$group])) continue;

            $groupData = [
                'name' => $this->getGroupName($group) . '资料查询',
                'type' => 'query',
                'group' => $group,
                'reports' => []
            ];

            foreach ($reportsByGroup[$group] as $report) {
                $reportId = $report['id'];
                $devices = $queryDevicesByReport[$reportId] ?? [];

                if (!empty($devices)) {
                    $reportData = [
                        'id' => $reportId,
                        'name' => $report['name'],
                        'devices' => $devices
                    ];
                    $groupData['reports'][] = $reportData;
                }
            }

            if (!empty($groupData['reports'])) {
                $menuData['query'][] = $groupData;
            }
        }

        // 不再在左侧菜单显示管理功能，统一通过右上角按钮进入后台管理

        return $menuData;
    }

    /**
     * 扫描静态页面
     */
    private function scanStaticPages(): array
    {
        $staticDir = __DIR__ . '/../../static_pages';
        $pages = [];

        if (!is_dir($staticDir)) {
            return $pages;
        }

        $files = glob($staticDir . '/*.html');
        foreach ($files as $file) {
            $filename = basename($file);
            $pages[] = [
                'filename' => $filename,
                'path' => 'static_pages/' . $filename,
                'title' => $this->extractPageTitle($file),
                'type' => $this->getPageType($filename)
            ];
        }

        return $pages;
    }

    /**
     * 提取页面标题
     */
    private function extractPageTitle(string $filePath): string
    {
        $content = file_get_contents($filePath);
        if (preg_match('/<title>(.*?)<\/title>/i', $content, $matches)) {
            return trim($matches[1]);
        }
        return basename($filePath, '.html');
    }

    /**
     * 获取页面类型
     */
    private function getPageType(string $filename): string
    {
        if (strpos($filename, 'entry_') === 0) {
            return 'entry';
        } elseif (strpos($filename, 'query_') === 0) {
            return 'query';
        }
        return 'unknown';
    }

    /**
     * 解析静态页面文件名，提取报表ID和设备ID
     * 文件名格式：entry_report_1001_device_10.html 或 query_report_1001_device_10.html
     */
    private function parseStaticPages(array $staticPages, string $type = 'query'): array
    {
        $reportDevices = [];

        foreach ($staticPages as $page) {
            $filename = $page['filename'];

            // 解析文件名：entry_report_1001_device_10.html 或 query_report_1001_device_10.html
            $pattern = "/{$type}_report_(\d+)_device_(\d+)\.html/";
            if (preg_match($pattern, $filename, $matches)) {
                $reportId = (int)$matches[1];
                $deviceId = (int)$matches[2];

                if (!isset($reportDevices[$reportId])) {
                    $reportDevices[$reportId] = [];
                }

                if (!in_array($deviceId, $reportDevices[$reportId])) {
                    $reportDevices[$reportId][] = $deviceId;
                }
            }
        }

        return $reportDevices;
    }

    /**
     * 检查角色访问权限
     */
    private function hasRoleAccess(array $userRoles, string $group): bool
    {
        return in_array('admin', $userRoles) ||
               in_array('mod', $userRoles) ||
               in_array($group, $userRoles);
    }

    /**
     * 获取分组名称
     */
    private function getGroupName(string $group): string
    {
        $names = [
            'site' => '现场',
            'cb26' => 'CB26',
            'ctrl' => '中控室'
        ];
        return $names[$group] ?? $group;
    }

    /**
     * 获取系统运行时间
     */
    public function getSystemUptime(): string
    {
        header('Content-Type: application/json; charset=UTF-8');

        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }

        try {
            // 获取服务器启动时间（使用系统命令）
            $uptime = '';

            if (function_exists('shell_exec') && !in_array('shell_exec', explode(',', ini_get('disable_functions')))) {
                // Linux/Unix 系统
                if (PHP_OS_FAMILY === 'Linux' || PHP_OS_FAMILY === 'Darwin') {
                    $output = shell_exec('uptime -p 2>/dev/null');
                    if ($output) {
                        $uptime = trim($output);
                        // 转换英文输出为中文
                        $uptime = str_replace(['up ', 'days', 'day', 'hours', 'hour', 'minutes', 'minute'],
                                            ['', '天', '天', '小时', '小时', '分钟', '分钟'], $uptime);
                    }
                }
                // Windows 系统
                elseif (PHP_OS_FAMILY === 'Windows') {
                    $output = shell_exec('wmic os get lastbootuptime /value 2>nul');
                    if ($output && preg_match('/LastBootUpTime=(\d{14})/', $output, $matches)) {
                        $bootTime = \DateTime::createFromFormat('YmdHis', $matches[1]);
                        $now = new \DateTime();
                        $diff = $now->diff($bootTime);

                        if ($diff->days > 0) {
                            $uptime = $diff->days . '天' . $diff->h . '小时';
                        } elseif ($diff->h > 0) {
                            $uptime = $diff->h . '小时' . $diff->i . '分钟';
                        } else {
                            $uptime = $diff->i . '分钟';
                        }
                    }
                }
            }

            // 如果无法获取系统运行时间，使用PHP进程运行时间作为替代
            if (empty($uptime)) {
                $startTime = $_SERVER['REQUEST_TIME'] ?? time();
                $currentTime = time();
                $uptimeSeconds = $currentTime - $startTime;

                $days = floor($uptimeSeconds / 86400);
                $hours = floor(($uptimeSeconds % 86400) / 3600);
                $minutes = floor(($uptimeSeconds % 3600) / 60);

                if ($days > 0) {
                    $uptime = $days . '天' . $hours . '小时';
                } elseif ($hours > 0) {
                    $uptime = $hours . '小时' . $minutes . '分钟';
                } else {
                    $uptime = $minutes . '分钟';
                }
            }

            return json_encode(['success' => true, 'uptime' => $uptime]);
        } catch (\Exception $e) {
            return json_encode(['success' => false, 'message' => '获取系统运行时间失败：' . $e->getMessage()]);
        }
    }
}
?>
