<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;

class HomeController
{
    public function index(): string
    {
        // 要求登录，未登录自动跳转
        Auth::requireLogin();

        // 检查是否为管理后台请求
        if (class_exists('\App\Core\RouteDispatcher') && \App\Core\RouteDispatcher::isAdmin()) {
            // 管理后台首页
            ob_start();
            include __DIR__ . '/../Views/admin/index.php';
            return ob_get_clean();
        } else {
            // 原有的首页（兼容性）
            ob_start();
            $title = '设备资料录入管理系统';
            include __DIR__ . '/../Views/layout/header.php';
            include __DIR__ . '/../Views/layout/sidebar.php';
            echo '<main class="container-fluid py-3">';
            $pageTitle = '首页';
            include __DIR__ . '/../Views/layout/page_head.php';
            echo '<div class="card shadow-sm"><div class="card-body">';
            echo '<h5 class="mb-3">欢迎使用设备资料录入管理系统</h5>';
            echo '<p class="text-muted mb-0">请从左侧菜单进入功能模块。</p>';
            echo '</div></div>';
            echo '</main>';
            include __DIR__ . '/../Views/layout/footer.php';
            return ob_get_clean();
        }
    }
}

