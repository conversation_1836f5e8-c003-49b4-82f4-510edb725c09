<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

class QueryController
{
    // 间歇运行设备查询（连续报表）
    public function continuous(): string
    {
        Auth::requireLogin();

        $reportId = (int)($_GET['report_id'] ?? $_GET['id'] ?? 0);
        $deviceId = (int)($_GET['device_id'] ?? 0);

        if (!$reportId) {
            return $this->showError('缺少报表ID参数');
        }

        // 获取报表信息
        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT * FROM reports WHERE id = ? AND enabled = 1");
        $stmt->execute([$reportId]);
        $report = $stmt->fetch();

        if (!$report) {
            return $this->showError('报表不存在或已禁用');
        }

        ob_start();
        $title = $report['name'] . ' - 数据查询';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = $report['name'] . ' - 数据查询';
        include __DIR__ . '/../Views/layout/page_head.php';

        // 复用EntryController的渲染方法，设置为查询模式
        $entryController = new \App\Controllers\EntryController();

        // 获取报表详细信息
        $stmt = $pdo->prepare("
            SELECT r.id, r.name, r.template_code, t.report_type
            FROM reports r
            LEFT JOIN report_templates t ON r.template_code = t.code
            WHERE r.id=? AND r.enabled=1
        ");
        $stmt->execute([$reportId]);
        $reportDetail = $stmt->fetch();

        $templateCode = $reportDetail['template_code'] ?: 'pumps';
        $reportType = (!empty($reportDetail['report_type'])) ? $reportDetail['report_type'] : 'continuous';

        // 关闭当前的输出缓冲，让EntryController处理
        ob_end_clean();
        return $entryController->renderOptimizedEntry($reportId, $report['name'], $templateCode, $reportType, $deviceId, true);

    }

    // 通用设备查询（复用录入页面代码）
    public function generic(): string
    {
        Auth::requireLogin();

        $reportId = (int)($_GET['report_id'] ?? $_GET['id'] ?? 0);
        $deviceId = (int)($_GET['device_id'] ?? 0);

        if (!$reportId) {
            return $this->showError('缺少报表ID参数');
        }

        // 获取报表信息和模板信息（与EntryController相同的逻辑）
        $pdo = DB::conn();
        $stmt = $pdo->prepare("
            SELECT r.id, r.name, r.template_code, t.report_type
            FROM reports r
            LEFT JOIN report_templates t ON r.template_code = t.code
            WHERE r.id=? AND r.enabled=1
        ");
        $stmt->execute([$reportId]);
        $report = $stmt->fetch();

        if (!$report) {
            return $this->showError('报表不存在或已禁用');
        }

        $templateCode = $report['template_code'] ?: 'pumps';
        $reportName = $report['name'];
        $reportType = (!empty($report['report_type'])) ? $report['report_type'] : 'daily';

        // 复用EntryController的渲染方法，但设置为查询模式
        $entryController = new \App\Controllers\EntryController();
        return $entryController->renderOptimizedEntry($reportId, $reportName, $templateCode, $reportType, $deviceId, true); // 最后一个参数设置为查询模式
    }

    private function showError(string $message): string
    {
        ob_start();
        $title = '错误';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        echo '<div class="alert alert-danger"><i class="fa fa-exclamation-triangle me-2"></i>' . htmlspecialchars($message) . '</div>';
        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }
}
?>

