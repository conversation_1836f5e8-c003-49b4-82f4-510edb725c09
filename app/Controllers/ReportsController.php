<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

class ReportsController
{
    public function index(): string
    {
        Auth::requireLogin();
        // 检查管理员权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            http_response_code(403);
            return '权限不足：需要管理员权限';
        }

        ob_start();
        $title = '报表配置';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = '报表配置';
        include __DIR__ . '/../Views/layout/page_head.php';

        // 获取报表列表
        $pdo = DB::conn();
        $reports = $pdo->query("SELECT r.*, rc.name as category_name FROM reports r LEFT JOIN report_categories rc ON r.category_id=rc.id ORDER BY r.id")->fetchAll();

        // 获取报表分类
        $categories = $pdo->query("SELECT * FROM report_categories ORDER BY name")->fetchAll();
        // 获取可用模板（来自数据库）
        $templates = $pdo->query("SELECT code, name, report_type FROM report_templates ORDER BY code")->fetchAll();

        // 构建模板映射表，用于显示模板名称
        $templateMap = [];
        foreach ($templates as $tpl) {
            $templateMap[$tpl['code']] = [
                'name' => $tpl['name'],
                'report_type' => $tpl['report_type'] ?? 'daily'
            ];
        }

        echo '<div class="card mb-3"><div class="card-body">';
        echo '<div class="d-flex justify-content-between align-items-center mb-3">';
        echo '<h5 class="mb-0">报表定义列表</h5>';
        echo '<button class="btn btn-primary btn-sm" onclick="showReportModal()">新增报表</button>';
        echo '</div>';

        echo '<div class="table-responsive">';
        echo '<table class="table table-hover table-sm">';
        echo '<thead><tr><th style="width:50px">ID</th><th style="width:150px">报表名称</th><th style="width:120px">编码</th><th style="width:80px">分类</th><th style="width:100px">录入模板</th><th style="width:80px">周期类型</th><th style="width:120px">时间限制</th><th style="width:60px">状态</th><th style="width:140px">操作</th></tr></thead>';
        echo '<tbody>';
        foreach ($reports as $r) {
            $status = $r['enabled'] ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>';

            // 模板类型显示
            $templateCode = $r['template_code'] ?? '';

            // 优先从数据库模板中查找
            if (isset($templateMap[$templateCode])) {
                $templateInfo = $templateMap[$templateCode];
                $templateName = htmlspecialchars($templateInfo['name']);
                $reportType = $templateInfo['report_type'];

                // 根据报表类型设置不同的徽章颜色
                if ($reportType === 'continuous') {
                    $templateText = '<span class="badge bg-info" title="连续报表（间歇运行）">' . $templateName . '</span>';
                } else {
                    $templateText = '<span class="badge bg-success" title="日报表（固定时间段）">' . $templateName . '</span>';
                }
            } else {
                // 兜底：内置模板类型
                $templateTypes = [
                    'pumps' => '<span class="badge bg-primary">泵类</span>',
                    'compressor' => '<span class="badge bg-info">空压机</span>',
                    'fan' => '<span class="badge bg-warning">风机</span>',
                    'motor' => '<span class="badge bg-success">电机</span>',
                    'custom' => '<span class="badge bg-secondary">自定义</span>'
                ];
                $templateText = $templateTypes[$templateCode] ?? '<span class="badge bg-light text-dark">未配置</span>';
            }

            // 周期类型显示
            $cycleTypes = [
                'daily' => '日报表',
                'weekly' => '周报表',
                'shift' => '班次报表',
                'monthly' => '月度报表',
                'irregular' => '不定时报表'
            ];
            $cycleText = $cycleTypes[$r['cycle_type']] ?? $r['cycle_type'];

            // 时间限制显示
            $limitTypes = [
                'hour' => '小时',
                'day' => '日',
                'week' => '周',
                'month' => '月',
                'unlimited' => '无时效限制'
            ];
            $limitText = $r['time_limit_type'] === 'unlimited' ? '无时效限制' : $r['time_limit_days'] . $limitTypes[$r['time_limit_type']] . '级时效控制';

            echo '<tr>';
            echo '<td>'.$r['id'].'</td>';
            echo '<td>'.$r['name'].'</td>';
            echo '<td><code class="small">'.htmlspecialchars($r['code']).'</code></td>';
            echo '<td class="small">'.($r['category_name'] ?: '-').'</td>';
            echo '<td>'.$templateText.'</td>';
            echo '<td class="small">'.$cycleText.'</td>';
            echo '<td class="small">'.$limitText.'</td>';
            echo '<td>'.$status.'</td>';
            echo '<td>';
            echo '<button class="btn btn-sm btn-outline-primary me-1" onclick="editReport('.$r['id'].')">编辑</button>';
            echo '<button class="btn btn-sm btn-outline-info me-1" onclick="configDevices('.$r['id'].')">设备配置</button>';
            echo '<button class="btn btn-sm btn-outline-danger" onclick="deleteReport('.$r['id'].')">删除</button>';
            echo '</td>';
            echo '</tr>';
        }
        echo '</tbody></table>';
        echo '</div>';
        echo '</div></div>';

        // 设备配置区域
        echo '<div class="card"><div class="card-body">';
        echo '<div class="d-flex justify-content-between align-items-center mb-3">';
        echo '<h5 class="mb-0">设备与报表关联配置</h5>';
        echo '<a href="'.BASE_URL.'index.php?r=sys/devices" class="btn btn-outline-primary"><i class="fa fa-cog me-1"></i>设备管理</a>';
        echo '</div>';
        echo '<div id="deviceConfig" class="mt-3">';
        echo '<p class="text-muted">请从上方表格点击"设备配置"来配置报表关联的设备。设备管理请前往 <a href="'.BASE_URL.'index.php?r=sys/devices">设备管理页面</a>。</p>';
        echo '</div>';
        echo '</div></div>';

        // 报表编辑模态框
        $this->renderReportModal($categories, $templates);

        echo '<script>';
        echo 'window.__REPORTS_API__ = {';
        echo '  getDevices: "'.BASE_URL.'index.php?r=api/reports/devices",';
        echo '  getAllDevices: "'.BASE_URL.'index.php?r=api/reports/all-devices",';
        echo '  saveDevices: "'.BASE_URL.'index.php?r=api/reports/save-devices",';
        echo '  getReport: "'.BASE_URL.'index.php?r=api/reports/get",';
        echo '  saveReport: "'.BASE_URL.'index.php?r=api/reports/save",';
        echo '  deleteReport: "'.BASE_URL.'index.php?r=api/reports/delete",';
        echo '  listDevices: "'.BASE_URL.'index.php?r=api/devices",';
        echo '  createDevice: "'.BASE_URL.'index.php?r=api/devices/create",';
        echo '  updateDevice: "'.BASE_URL.'index.php?r=api/devices/update",';
        echo '  deleteDevice: "'.BASE_URL.'index.php?r=api/devices/delete",';
        echo '  listPumps: "'.BASE_URL.'index.php?r=api/devices/pumps",';
        echo '  createPump: "'.BASE_URL.'index.php?r=api/devices/pumps/create",';
        echo '  deletePump: "'.BASE_URL.'index.php?r=api/devices/pumps/delete"';
        echo '};';
        echo '</script>';

        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    private function renderReportModal(array $categories, array $templates): void
    {
        echo '<div class="modal fade" id="reportModal" tabindex="-1">';
        echo '<div class="modal-dialog modal-lg">';
        echo '<div class="modal-content">';
        echo '<div class="modal-header">';
        echo '<h5 class="modal-title" id="reportModalTitle">新增报表</h5>';
        echo '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>';
        echo '</div>';
        echo '<div class="modal-body">';
        echo '<form id="reportForm">';
        echo '<input type="hidden" id="reportId" name="id">';

        echo '<div class="row g-3">';
        echo '<div class="col-md-6">';
        echo '<label class="form-label">报表名称 *</label>';
        echo '<input type="text" class="form-control" id="reportName" name="name" required>';
        echo '</div>';
        echo '<div class="col-md-6">';
        echo '<label class="form-label">报表编码 *</label>';
        echo '<input type="text" class="form-control" id="reportCode" name="code" required readonly placeholder="将根据报表名称自动生成">';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<label class="form-label">报表分类</label>';
        echo '<select class="form-select" id="reportCategory" name="category_id">';
        echo '<option value="">请选择分类</option>';
        foreach ($categories as $cat) {
            echo '<option value="'.$cat['id'].'">'.$cat['name'].'</option>';
        }
        echo '</select>';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<label class="form-label">录入模板 *</label>';
        echo '<select class="form-select" id="reportTemplate" name="template_code" required>';
        // 优先使用数据库中的模板；若没有任何模板，提供内置选项兜底
        if (!empty($templates)) {
            foreach ($templates as $tpl) {
                $reportType = $tpl['report_type'] ?? 'daily';
                $typeLabel = $reportType === 'continuous' ? '连续报表' : '日报表';
                $optionText = htmlspecialchars($tpl['name']) . ' (' . $typeLabel . ')';
                echo '<option value="'.htmlspecialchars($tpl['code']).'" data-report-type="'.$reportType.'">'.$optionText.'</option>';
            }
        } else {
            echo '<option value="pumps">泵类设备</option>';
            echo '<option value="compressor">空压机设备</option>';
            echo '<option value="fan">风机设备</option>';
            echo '<option value="motor">电机设备</option>';
            echo '<option value="custom">自定义模板</option>';
        }
        echo '</select>';
        echo '<div class="form-text">选择录入模板决定数据录入界面的字段和布局</div>';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<label class="form-label">周期类型 *</label>';
        echo '<select class="form-select" id="reportCycleType" name="cycle_type" required onchange="updateTimeLimitOptions()">';
        echo '<option value="daily">日报表</option>';
        echo '<option value="weekly">周报表</option>';
        echo '<option value="shift">班次报表</option>';
        echo '<option value="monthly">月度报表</option>';
        echo '<option value="irregular">不定时报表</option>';
        echo '</select>';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<label class="form-label">时效控制类型 *</label>';
        echo '<select class="form-select" id="reportTimeLimitType" name="time_limit_type" required onchange="updateTimeLimitDays()">';
        echo '<option value="unlimited">无时效限制</option>';
        echo '</select>';
        echo '</div>';

        echo '<div class="col-md-6" id="timeLimitDaysGroup">';
        echo '<label class="form-label" id="timeLimitDaysLabel">时效控制数值</label>';
        echo '<input type="number" class="form-control" id="reportTimeLimitDays" name="time_limit_days" min="1" max="365">';
        echo '</div>';

        echo '<div class="col-12">';
        echo '<div class="form-check">';
        echo '<input class="form-check-input" type="checkbox" id="reportEnabled" name="enabled" checked>';
        echo '<label class="form-check-label" for="reportEnabled">启用报表</label>';
        echo '</div>';
        echo '</div>';

        // 值班人员配置区域
        echo '<div class="col-12">';
        echo '<hr>';
        echo '<h6 class="mb-3"><i class="fa fa-users me-2"></i>值班人员配置</h6>';
        echo '</div>';

        echo '<div class="col-12">';
        echo '<div class="form-check">';
        echo '<input class="form-check-input" type="checkbox" id="dutyStaffEnabled" name="duty_staff_enabled" onchange="toggleDutyStaffConfig()">';
        echo '<label class="form-check-label" for="dutyStaffEnabled">启用值班人员录入</label>';
        echo '</div>';
        echo '</div>';

        echo '<div id="dutyStaffConfigGroup" style="display:none;">';
        echo '<div class="col-md-6">';
        echo '<label class="form-label">白班标签</label>';
        echo '<input type="text" class="form-control" id="dutyDayLabel" name="duty_day_label" value="白班值班人" placeholder="白班值班人">';
        echo '</div>';
        echo '<div class="col-md-6">';
        echo '<label class="form-label">夜班标签</label>';
        echo '<input type="text" class="form-control" id="dutyNightLabel" name="duty_night_label" value="夜班值班人" placeholder="夜班值班人">';
        echo '</div>';
        echo '<div class="col-12">';
        echo '<div class="form-check">';
        echo '<input class="form-check-input" type="checkbox" id="dutyStaffRequired" name="duty_staff_required">';
        echo '<label class="form-check-label" for="dutyStaffRequired">值班人员为必填项</label>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '</div>';

        echo '</form>';
        echo '</div>';
        echo '<div class="modal-footer">';
        echo '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>';
        echo '<button type="button" class="btn btn-primary" onclick="saveReport()">保存</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
    // API: 获取报表关联的设备
    public function getDevices(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');
        $reportId = (int)($_GET['report_id'] ?? 0);
        $deviceId = (int)($_GET['device_id'] ?? 0); // 新增：支持按设备ID过滤
        $deviceType = trim($_GET['device_type'] ?? ''); // 新增：支持按设备类型过滤

        if (!$reportId) {
            echo json_encode(['success' => false, 'message' => '缺少报表ID参数']);
            return '';
        }

        try {
            $pdo = DB::conn();

            // 如果指定了设备ID，只返回该设备的泵号
            $whereDevice = $deviceId ? ' AND rd.device_id = ' . $deviceId : '';

            // 如果指定了设备类型，添加设备类型过滤
            $whereDeviceType = '';
            if ($deviceType) {
                $whereDeviceType = ' AND d.name LIKE ' . $pdo->quote('%' . $deviceType . '%');
            }

            // 修改查询以支持object_id字段，并确保返回正确的数据结构
            $sql = "SELECT rd.device_id, rd.pump_id, COALESCE(rd.object_id, rd.pump_id) as object_id,
                           d.name as device_name, p.pump_no
                    FROM report_devices rd
                    JOIN devices d ON rd.device_id=d.id
                    LEFT JOIN pumps p ON (rd.object_id=p.id OR (rd.object_id IS NULL AND rd.pump_id=p.id))
                    WHERE rd.report_id=? {$whereDevice} {$whereDeviceType} ORDER BY d.name, p.pump_no";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$reportId]);
            $devices = $stmt->fetchAll();

            // 确保数据结构正确：device_id保持为设备ID，pump_id为泵ID
            foreach ($devices as &$device) {
                $pumpId = $device['object_id'] ?: $device['pump_id'];
                if ($pumpId) {
                    // 保持device_id为原始设备ID，不要修改
                    // pump_id设置为实际的泵ID
                    $device['pump_id'] = $pumpId;
                }
            }
            unset($device); // 清除引用

            // 如果URL中指定了device_id，添加默认选中信息
            $response = ['success' => true, 'data' => $devices];
            if ($deviceId && !empty($devices)) {
                // 统一返回device_id作为auto_select，简化前端逻辑
                $response['auto_select'] = $deviceId;
            }

            echo json_encode($response, JSON_UNESCAPED_UNICODE);
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => '获取设备列表失败: ' . $e->getMessage()]);
        }
        return '';
    }

    // API: 获取所有可用设备
    public function getAllDevices(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        $pdo = DB::conn();
        $devices = $pdo->query("SELECT d.*, GROUP_CONCAT(CONCAT(p.id,':',p.pump_no) ORDER BY p.pump_no SEPARATOR ',') as pumps
                               FROM devices d LEFT JOIN pumps p ON d.id=p.device_id
                               GROUP BY d.id ORDER BY d.name")->fetchAll();

        // 解析泵数据
        foreach ($devices as &$device) {
            $device['pump_list'] = [];
            if ($device['pumps']) {
                foreach (explode(',', $device['pumps']) as $pump) {
                    $parts = explode(':', $pump);
                    $device['pump_list'][] = ['id' => (int)$parts[0], 'pump_no' => $parts[1]];
                }
            }
            unset($device['pumps']);
        }

        echo json_encode($devices, JSON_UNESCAPED_UNICODE);
        return '';
    }

    // API: 保存报表设备关联
    public function saveDevices(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $reportId = (int)($input['report_id'] ?? 0);
        $devices = $input['devices'] ?? [];

        $pdo = DB::conn();
        $pdo->beginTransaction();
        try {
            // 清除旧关联
            $pdo->prepare("DELETE FROM report_devices WHERE report_id=?")->execute([$reportId]);

            // 插入新关联
            $stmt = $pdo->prepare("INSERT INTO report_devices (report_id, device_id, pump_id) VALUES (?, ?, ?)");
            foreach ($devices as $device) {
                $deviceId = (int)$device['device_id'];
                $pumpId = !empty($device['pump_id']) ? (int)$device['pump_id'] : null;
                $stmt->execute([$reportId, $deviceId, $pumpId]);
            }

            $pdo->commit();
            echo json_encode(['success' => true]);
        } catch (\Throwable $e) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        return '';
    }

    // API: 获取单个报表信息
    public function getReport(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        $reportId = (int)($_GET['id'] ?? 0);
        if (!$reportId) {
            echo json_encode(['success' => false, 'message' => '报表ID不能为空']);
            return '';
        }

        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT r.*, rc.name as category_name FROM reports r LEFT JOIN report_categories rc ON r.category_id=rc.id WHERE r.id=?");
        $stmt->execute([$reportId]);
        $report = $stmt->fetch();

        if (!$report) {
            echo json_encode(['success' => false, 'message' => '报表不存在']);
            return '';
        }

        echo json_encode(['success' => true, 'data' => $report], JSON_UNESCAPED_UNICODE);
        return '';
    }

    // API: 保存报表
    public function saveReport(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $reportId = (int)($input['id'] ?? 0);
        $name = trim($input['name'] ?? '');
        $code = trim($input['code'] ?? '');
        $categoryId = !empty($input['category_id']) ? (int)$input['category_id'] : null;
        $templateCode = trim($input['template_code'] ?? 'pumps');
        $cycleType = $input['cycle_type'] ?? 'daily';
        $timeLimitType = $input['time_limit_type'] ?? 'day';
        $timeLimitDays = (int)($input['time_limit_days'] ?? 2);
        $enabled = !empty($input['enabled']);
        $dutyStaffConfig = $input['duty_staff_config'] ?? [];
        $userId = $_SESSION['user']['id'] ?? 1;

        // 验证必填字段
        if (!$name || !$code || !$templateCode) {
            echo json_encode(['success' => false, 'message' => '报表名称、编码和录入模板不能为空']);
            return '';
        }

        // 验证模板代码：允许数据库中存在的任意模板代码；兼容内置白名单
        $pdo = DB::conn();
        $stmtTpl = $pdo->prepare("SELECT COUNT(*) FROM report_templates WHERE code=?");
        $stmtTpl->execute([$templateCode]);
        $existsTpl = (int)$stmtTpl->fetchColumn() > 0;
        $builtins = ['pumps', 'compressor', 'fan', 'motor', 'custom'];
        if (!$existsTpl && !in_array($templateCode, $builtins)) {
            echo json_encode(['success' => false, 'message' => '无效的录入模板']);
            return '';
        }

        // 验证周期类型和时间限制类型的组合
        $validCombinations = [
            'daily' => ['hour', 'day', 'week', 'month', 'unlimited'],
            'weekly' => ['week', 'month', 'unlimited'],
            'shift' => ['month', 'unlimited'],
            'monthly' => ['month', 'unlimited'],
            'irregular' => ['day', 'week', 'month', 'unlimited']
        ];

        if (!isset($validCombinations[$cycleType]) || !in_array($timeLimitType, $validCombinations[$cycleType])) {
            echo json_encode(['success' => false, 'message' => '周期类型和时间限制类型组合无效']);
            return '';
        }

        $pdo = DB::conn();

        try {
            // 处理值班人员配置
            $dutyStaffConfigJson = json_encode($dutyStaffConfig, JSON_UNESCAPED_UNICODE);

            if ($reportId) {
                // 更新
                $stmt = $pdo->prepare("UPDATE reports SET name=?, code=?, category_id=?, template_code=?, cycle_type=?, time_limit_type=?, time_limit_days=?, enabled=?, duty_staff_config=? WHERE id=?");
                $stmt->execute([$name, $code, $categoryId, $templateCode, $cycleType, $timeLimitType, $timeLimitDays, $enabled ? 1 : 0, $dutyStaffConfigJson, $reportId]);
                $message = '报表更新成功';
            } else {
                // 新增
                $stmt = $pdo->prepare("INSERT INTO reports (name, code, category_id, template_code, cycle_type, fields_json, time_limit_type, time_limit_days, enabled, duty_staff_config, created_by) VALUES (?, ?, ?, ?, ?, '[]', ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $code, $categoryId, $templateCode, $cycleType, $timeLimitType, $timeLimitDays, $enabled ? 1 : 0, $dutyStaffConfigJson, $userId]);
                $reportId = $pdo->lastInsertId();
                $message = '报表创建成功';
            }

            echo json_encode(['success' => true, 'message' => $message, 'id' => $reportId]);
        } catch (\Throwable $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo json_encode(['success' => false, 'message' => '报表编码已存在']);
            } else {
                echo json_encode(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
            }
        }

        return '';
    }

    // API: 删除报表
    public function deleteReport(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }
        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $reportId = (int)($input['id'] ?? 0);
        if (!$reportId) { echo json_encode(['success'=>false,'message'=>'报表ID不能为空']); return ''; }
        $pdo = null;
        try {
            $pdo = DB::conn();
            // 为避免不一致，先删关联与条目，再删报表
            $pdo->beginTransaction();
            $pdo->prepare('DELETE FROM report_devices WHERE report_id=?')->execute([$reportId]);
            $pdo->prepare('DELETE FROM report_entries WHERE report_id=?')->execute([$reportId]);
            $pdo->prepare('DELETE FROM report_duty_staff WHERE report_id=?')->execute([$reportId]);
            $pdo->prepare('DELETE FROM reports WHERE id=?')->execute([$reportId]);
            $pdo->commit();
            echo json_encode(['success'=>true,'message'=>'报表已删除']);
        } catch (\Throwable $e) {
            if ($pdo && $pdo->inTransaction()) { $pdo->rollBack(); }
            echo json_encode(['success'=>false,'message'=>'删除失败：'.$e->getMessage()]);
        }
        return '';
    }
}

