<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

class StaticController
{
    // 静态页面管理主页
    public function index(): string
    {
        Auth::requireLogin();
        
        // 检查管理员权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            http_response_code(403);
            return '权限不足：需要管理员权限';
        }

        ob_start();
        $title = '静态页面管理';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = '静态页面管理';
        include __DIR__ . '/../Views/layout/page_head.php';

        // 获取所有报表
        $pdo = DB::conn();
        $reports = $pdo->query("
            SELECT r.*, rt.name as template_name, rt.report_type 
            FROM reports r 
            LEFT JOIN report_templates rt ON r.template_code = rt.code 
            WHERE r.enabled = 1 
            ORDER BY r.id
        ")->fetchAll();

        // 检查静态页面目录
        $staticDir = __DIR__ . '/../../static_pages/';
        $staticDirExists = is_dir($staticDir);
        $staticFiles = [];
        
        if ($staticDirExists) {
            $files = glob($staticDir . '*.html');
            foreach ($files as $file) {
                $filename = basename($file);
                $staticFiles[$filename] = [
                    'file' => $filename,
                    'size' => filesize($file),
                    'modified' => filemtime($file)
                ];
            }
        }

        echo '<div class="row">';
        
        // 左侧：报表列表和生成控制
        echo '<div class="col-md-8">';
        echo '<div class="card mb-4">';
        echo '<div class="card-header d-flex justify-content-between align-items-center">';
        echo '<h5 class="mb-0">报表静态化管理</h5>';
        echo '<div class="btn-group btn-group-sm">';
        echo '<button class="btn btn-success" onclick="generateAllStatic()">批量生成</button>';
        echo '<button class="btn btn-outline-secondary" onclick="refreshStaticList()">刷新列表</button>';
        echo '</div>';
        echo '</div>';
        echo '<div class="card-body">';

        if (empty($reports)) {
            echo '<div class="alert alert-info">暂无可用报表</div>';
        } else {
            echo '<div class="table-responsive">';
            echo '<table class="table table-hover table-sm">';
            echo '<thead>';
            echo '<tr>';
            echo '<th style="width: 80px;">ID</th>';
            echo '<th style="width: 250px;">报表名称</th>';
            echo '<th style="width: 120px;">模板类型</th>';
            echo '<th style="width: 100px;">报表类型</th>';
            echo '<th style="width: 180px;">静态页面状态</th>';
            echo '<th style="width: 150px;">操作</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';

            foreach ($reports as $report) {
                $reportId = $report['id'];
                $entryFile = "entry_report_{$reportId}.html";
                $queryFile = "query_report_{$reportId}.html";
                
                // 统计设备专用页面数量
                $devicePageCount = 0;
                foreach ($staticFiles as $filename => $info) {
                    if (preg_match("/^(entry|query)_report_{$reportId}_device_\d+\.html$/", $filename)) {
                        $devicePageCount++;
                    }
                }

                echo '<tr>';
                echo '<td>' . $reportId . '</td>';
                echo '<td>' . htmlspecialchars($report['name']) . '</td>';
                echo '<td><span class="badge bg-secondary">' . htmlspecialchars($report['template_code']) . '</span></td>';
                echo '<td><span class="badge bg-info">' . htmlspecialchars($report['report_type'] ?: 'daily') . '</span></td>';
                echo '<td>';

                if ($devicePageCount > 0) {
                    echo '<span class="badge bg-success">' . $devicePageCount . ' 个设备页面</span>';
                } else {
                    echo '<span class="badge bg-secondary">未生成</span>';
                }
                
                echo '</td>';
                echo '<td>';
                echo '<div class="btn-group btn-group-sm" role="group">';
                echo '<button class="btn btn-primary btn-sm" onclick="generateDeviceStatic(' . $reportId . ')" title="生成设备专用页面">';
                echo '<i class="fa fa-cogs me-1"></i>生成设备页面';
                echo '</button>';

                // 只有当有静态页面时才显示清理按钮
                if ($devicePageCount > 0) {
                    echo '<button class="btn btn-outline-danger btn-sm" onclick="cleanupReportStatic(' . $reportId . ')" title="清理该报表的所有静态页面">';
                    echo '<i class="fa fa-trash"></i>';
                    echo '</button>';
                }

                echo '</div>';
                echo '</td>';
                echo '</tr>';
            }

            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';

        // 右侧：静态页面文件列表和系统状态
        echo '<div class="col-md-4">';
        
        // 系统状态
        echo '<div class="card mb-4">';
        echo '<div class="card-header">';
        echo '<h6 class="mb-0">系统状态</h6>';
        echo '</div>';
        echo '<div class="card-body">';
        echo '<div class="row text-center">';
        echo '<div class="col-6">';
        echo '<div class="h4 text-primary">' . count($reports) . '</div>';
        echo '<small class="text-muted">总报表数</small>';
        echo '</div>';
        echo '<div class="col-6">';
        echo '<div class="h4 text-success">' . count($staticFiles) . '</div>';
        echo '<small class="text-muted">静态页面数</small>';
        echo '</div>';
        echo '</div>';
        
        if (!$staticDirExists) {
            echo '<div class="alert alert-warning mt-3 small">';
            echo '<i class="fa fa-exclamation-triangle"></i> 静态页面目录不存在，将自动创建';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';

        // 静态文件列表（优化版）
        echo '<div class="card">';
        echo '<div class="card-header d-flex justify-content-between align-items-center">';
        echo '<h6 class="mb-0">最近文件</h6>';
        echo '<div class="btn-group btn-group-sm">';
        echo '<button class="btn btn-outline-secondary" onclick="toggleFileList()" id="toggleFileBtn">';
        echo '<i class="fa fa-eye"></i> 查看全部';
        echo '</button>';
        echo '<button class="btn btn-outline-danger" onclick="cleanupStaticFiles()">';
        echo '<i class="fa fa-trash"></i>';
        echo '</button>';
        echo '</div>';
        echo '</div>';
        echo '<div class="card-body p-2">';

        if (empty($staticFiles)) {
            echo '<p class="text-muted small mb-0">暂无静态文件</p>';
        } else {
            // 计算总大小
            $totalSize = array_sum(array_column($staticFiles, 'size'));
            $totalCount = count($staticFiles);

            // 显示统计信息
            echo '<div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">';
            echo '<small class="text-muted">共 ' . $totalCount . ' 个文件</small>';
            echo '<small class="text-muted">总计 ' . $this->formatFileSize($totalSize) . '</small>';
            echo '</div>';

            // 按修改时间排序，只显示最近的10个
            usort($staticFiles, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });

            $displayFiles = array_slice($staticFiles, 0, 10);
            $hasMore = count($staticFiles) > 10;

            echo '<div class="file-list-container" style="max-height: 300px; overflow-y: auto;">';
            echo '<div class="list-group list-group-flush" id="fileListShort">';
            foreach ($displayFiles as $file) {
                echo '<div class="list-group-item border-0 px-2 py-1">';
                echo '<div class="d-flex justify-content-between align-items-start">';
                echo '<div class="flex-grow-1 me-2">';
                echo '<div class="fw-bold small text-truncate" style="max-width: 200px;" title="' . htmlspecialchars($file['file']) . '">';
                echo htmlspecialchars($this->getShortFileName($file['file']));
                echo '</div>';
                echo '<small class="text-muted">' . $this->formatFileSize($file['size']) . '</small>';
                echo '</div>';
                echo '<small class="text-muted text-nowrap">' . date('m-d H:i', $file['modified']) . '</small>';
                echo '</div>';
                echo '</div>';
            }
            echo '</div>';

            // 隐藏的完整列表
            if ($hasMore) {
                echo '<div class="list-group list-group-flush d-none" id="fileListFull">';
                foreach ($staticFiles as $file) {
                    echo '<div class="list-group-item border-0 px-2 py-1">';
                    echo '<div class="d-flex justify-content-between align-items-start">';
                    echo '<div class="flex-grow-1 me-2">';
                    echo '<div class="fw-bold small text-truncate" style="max-width: 200px;" title="' . htmlspecialchars($file['file']) . '">';
                    echo htmlspecialchars($this->getShortFileName($file['file']));
                    echo '</div>';
                    echo '<small class="text-muted">' . $this->formatFileSize($file['size']) . '</small>';
                    echo '</div>';
                    echo '<small class="text-muted text-nowrap">' . date('m-d H:i', $file['modified']) . '</small>';
                    echo '</div>';
                    echo '</div>';
                }
                echo '</div>';

                echo '<div class="text-center mt-2">';
                echo '<small class="text-muted">还有 ' . ($totalCount - 10) . ' 个文件未显示</small>';
                echo '</div>';
            }
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
        
        echo '</div>';
        echo '</div>';

        // 显示已生成的静态页面列表
        echo '<div class="row mt-4">';
        echo '<div class="col-12">';
        echo '<div class="card">';
        echo '<div class="card-header d-flex justify-content-between align-items-center">';
        echo '<h5 class="mb-0">已生成的静态页面</h5>';
        echo '<button class="btn btn-outline-secondary btn-sm" onclick="refreshStaticList()">刷新列表</button>';
        echo '</div>';
        echo '<div class="card-body">';

        // 按报表分组显示静态页面
        $this->displayStaticPagesList($staticFiles);

        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        // 添加自定义CSS优化表格显示
        echo '<style>';
        echo '.table th, .table td { vertical-align: middle; }';
        echo '.btn-group-sm .btn { padding: 0.25rem 0.4rem; font-size: 0.75rem; }';
        echo '.table-sm th, .table-sm td { padding: 0.3rem; }';
        echo '</style>';

        // JavaScript API配置
        echo '<script>';
        echo 'window.__STATIC_API__ = {';
        echo '  generate: "' . BASE_URL . 'index.php?r=static/generate",';
        echo '  generateDevices: "' . BASE_URL . 'index.php?r=static/generate-devices",';
        echo '  cleanup: "' . BASE_URL . 'index.php?r=static/cleanup",';
        echo '  cleanupReport: "' . BASE_URL . 'index.php?r=static/cleanup-report",';
        echo '  status: "' . BASE_URL . 'index.php?r=static/status"';
        echo '};';

        // 文件列表切换功能
        echo 'function toggleFileList() {';
        echo '  const shortList = document.getElementById("fileListShort");';
        echo '  const fullList = document.getElementById("fileListFull");';
        echo '  const toggleBtn = document.getElementById("toggleFileBtn");';
        echo '  ';
        echo '  if (fullList && fullList.classList.contains("d-none")) {';
        echo '    shortList.classList.add("d-none");';
        echo '    fullList.classList.remove("d-none");';
        echo '    toggleBtn.innerHTML = \'<i class="fa fa-eye-slash"></i> 收起\';';
        echo '  } else if (fullList) {';
        echo '    shortList.classList.remove("d-none");';
        echo '    fullList.classList.add("d-none");';
        echo '    toggleBtn.innerHTML = \'<i class="fa fa-eye"></i> 查看全部\';';
        echo '  }';
        echo '}';

        // 清理单个报表的静态页面
        echo 'function cleanupReportStatic(reportId) {';
        echo '  if (!confirm("确定要清理报表 " + reportId + " 的所有静态页面吗？")) return;';
        echo '  ';
        echo '  fetch(window.__STATIC_API__.cleanupReport, {';
        echo '    method: "POST",';
        echo '    headers: { "Content-Type": "application/json" },';
        echo '    body: JSON.stringify({ report_id: reportId })';
        echo '  })';
        echo '  .then(response => response.json())';
        echo '  .then(data => {';
        echo '    if (data.success) {';
        echo '      alert(data.message);';
        echo '      location.reload();';
        echo '    } else {';
        echo '      alert("清理失败: " + data.message);';
        echo '    }';
        echo '  })';
        echo '  .catch(error => {';
        echo '    console.error("Error:", error);';
        echo '    alert("清理失败，请重试");';
        echo '  });';
        echo '}';
        echo '</script>';

        // 静态页面管理JavaScript
        echo '<script src="' . BASE_URL . 'js/static-manager.js"></script>';

        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    // API: 生成静态页面
    public function generate(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $reportId = (int)($input['report_id'] ?? $_GET['report_id'] ?? 0);
        $type = $input['type'] ?? $_GET['type'] ?? 'both'; // entry, query, both

        if (!$reportId) {
            echo json_encode(['success' => false, 'message' => '报表ID不能为空']);
            return '';
        }

        try {
            // 获取报表信息（包含完整的配置信息）
            $pdo = DB::conn();
            $stmt = $pdo->prepare("
                SELECT r.*, rt.fields_config, rt.report_type
                FROM reports r
                LEFT JOIN report_templates rt ON r.template_code = rt.code
                WHERE r.id = ? AND r.enabled = 1
            ");
            $stmt->execute([$reportId]);
            $report = $stmt->fetch();

            if (!$report) {
                echo json_encode(['success' => false, 'message' => '报表不存在或已禁用']);
                return '';
            }

            // 解析模板配置
            $templateConfig = [
                'report_id' => $reportId,
                'name' => $report['name'],
                'template_code' => $report['template_code'],
                'report_type' => $report['report_type'] ?: 'daily',
                'fields' => [],
                // 添加报表配置信息
                'time_limit_days' => (int)($report['time_limit_days'] ?? 2),
                'time_limit_type' => $report['time_limit_type'] ?? 'day',
                'duty_staff_config' => []
            ];

            // 解析字段配置
            if ($report['fields_config']) {
                $fieldsConfig = json_decode($report['fields_config'], true);
                if ($fieldsConfig && isset($fieldsConfig['fields'])) {
                    $templateConfig['fields'] = $fieldsConfig['fields'];
                }
            }

            // 解析值班人员配置
            if (!empty($report['duty_staff_config'])) {
                $dutyConfig = json_decode($report['duty_staff_config'], true);
                if ($dutyConfig) {
                    $templateConfig['duty_staff_config'] = $dutyConfig;
                }
            }

            // 引入静态页面生成器
            require_once __DIR__ . '/../../tools/static-generator.php';
            $generator = new \StaticPageGenerator([
                'output_dir' => __DIR__ . '/../../static_pages/',
                'minify_html' => true
            ]);

            // 使用与批量生成相同的逻辑：按设备生成
            $results = $generator->generateByDevices($reportId, $templateConfig);

            $successCount = 0;
            $errorCount = 0;
            $generatedFiles = [];

            foreach ($results as $result) {
                if ($result['result']['success']) {
                    $successCount++;
                    $generatedFiles[] = [
                        'type' => $result['type'],
                        'device_name' => $result['device_name'],
                        'file' => $result['result']['file'],
                        'size' => $result['result']['size']
                    ];
                } else {
                    $errorCount++;
                }
            }

            echo json_encode([
                'success' => true,
                'message' => "成功生成 {$successCount} 个设备专用页面" . ($errorCount > 0 ? "，{$errorCount} 个失败" : ""),
                'results' => $generatedFiles,
                'total' => count($results),
                'success_count' => $successCount,
                'error_count' => $errorCount
            ], JSON_UNESCAPED_UNICODE);

        } catch (\Throwable $e) {
            echo json_encode([
                'success' => false,
                'message' => '生成失败: ' . $e->getMessage()
            ]);
        }

        return '';
    }

    // API: 清理静态文件
    public function cleanup(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        try {
            $staticDir = __DIR__ . '/../../static_pages/';
            $deletedCount = 0;

            if (is_dir($staticDir)) {
                $files = glob($staticDir . '*.html');
                foreach ($files as $file) {
                    if (unlink($file)) {
                        $deletedCount++;
                    }
                }
            }

            echo json_encode([
                'success' => true,
                'message' => "已清理 {$deletedCount} 个静态文件"
            ]);

        } catch (\Throwable $e) {
            echo json_encode([
                'success' => false,
                'message' => '清理失败: ' . $e->getMessage()
            ]);
        }

        return '';
    }

    // API: 生成设备专用静态页面
    public function generateDevices(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        // 检查管理员权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足：需要管理员权限']);
            return '';
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            $reportId = (int)($input['report_id'] ?? 0);

            if (!$reportId) {
                echo json_encode(['success' => false, 'message' => '报表ID不能为空']);
                return '';
            }

            // 获取报表信息
            $pdo = DB::conn();
            $stmt = $pdo->prepare("
                SELECT r.*, rt.fields_config, rt.report_type
                FROM reports r
                LEFT JOIN report_templates rt ON r.template_code = rt.code
                WHERE r.id = ? AND r.enabled = 1
            ");
            $stmt->execute([$reportId]);
            $report = $stmt->fetch();

            if (!$report) {
                echo json_encode(['success' => false, 'message' => '报表不存在或已禁用']);
                return '';
            }

            // 构建模板配置
            $templateConfig = [
                'report_id' => (int)$report['id'],
                'name' => $report['name'],
                'template_code' => $report['template_code'],
                'report_type' => $report['report_type'] ?: 'daily',
                'fields' => [],
                'time_limit_days' => (int)($report['time_limit_days'] ?? 2),
                'time_limit_type' => $report['time_limit_type'] ?? 'day',
                'duty_staff_config' => []
            ];

            // 解析字段配置
            if ($report['fields_config']) {
                $fieldsConfig = json_decode($report['fields_config'], true);
                if ($fieldsConfig && isset($fieldsConfig['fields'])) {
                    $templateConfig['fields'] = $fieldsConfig['fields'];
                }
            }

            // 解析值班人员配置
            if (!empty($report['duty_staff_config'])) {
                $dutyConfig = json_decode($report['duty_staff_config'], true);
                if ($dutyConfig) {
                    $templateConfig['duty_staff_config'] = $dutyConfig;
                }
            }

            // 使用静态页面生成器
            require_once __DIR__ . '/../../tools/static-generator.php';
            $generator = new \StaticPageGenerator([
                'output_dir' => __DIR__ . '/../../static_pages/',
                'minify_html' => false
            ]);

            // 生成按设备分组的页面
            $results = $generator->generateByDevices($reportId, $templateConfig);

            $successCount = 0;
            $errorCount = 0;
            $generatedFiles = [];

            foreach ($results as $result) {
                if ($result['result']['success']) {
                    $successCount++;
                    $generatedFiles[] = [
                        'type' => $result['type'],
                        'device_name' => $result['device_name'],
                        'file' => $result['result']['file'],
                        'size' => $result['result']['size']
                    ];
                } else {
                    $errorCount++;
                }
            }

            echo json_encode([
                'success' => true,
                'message' => "成功生成 {$successCount} 个设备专用页面" . ($errorCount > 0 ? "，{$errorCount} 个失败" : ""),
                'results' => $generatedFiles,
                'total' => count($results),
                'success_count' => $successCount,
                'error_count' => $errorCount
            ]);

        } catch (\Throwable $e) {
            echo json_encode([
                'success' => false,
                'message' => '生成失败: ' . $e->getMessage()
            ]);
        }

        return '';
    }

    // API: 获取静态页面状态
    public function status(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        try {
            $staticDir = __DIR__ . '/../../static_pages/';
            $files = [];

            if (is_dir($staticDir)) {
                $htmlFiles = glob($staticDir . '*.html');
                foreach ($htmlFiles as $file) {
                    $filename = basename($file);
                    $files[] = [
                        'file' => $filename,
                        'size' => filesize($file),
                        'modified' => filemtime($file)
                    ];
                }
            }

            echo json_encode([
                'success' => true,
                'files' => $files,
                'total' => count($files)
            ]);

        } catch (\Throwable $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取状态失败: ' . $e->getMessage()
            ]);
        }

        return '';
    }

    /**
     * 显示静态页面列表
     */
    private function displayStaticPagesList($staticFiles)
    {
        if (empty($staticFiles)) {
            echo '<p class="text-muted">暂无静态页面</p>';
            return;
        }

        // 按报表分组
        $pagesByReport = [];
        foreach ($staticFiles as $filename => $info) {
            // 确保 $filename 是字符串
            if (is_array($info) && isset($info['file'])) {
                $filename = $info['file'];
            }

            if (preg_match('/^(entry|query)_report_(\d+)(?:_device_(\d+))?\.html$/', $filename, $matches)) {
                $type = $matches[1];
                $reportId = $matches[2];
                $deviceId = $matches[3] ?? null;

                if (!isset($pagesByReport[$reportId])) {
                    $pagesByReport[$reportId] = [];
                }

                $pagesByReport[$reportId][] = [
                    'filename' => $filename,
                    'type' => $type,
                    'device_id' => $deviceId,
                    'size' => $info['size'],
                    'modified' => $info['modified']
                ];
            }
        }

        // 获取报表名称
        $pdo = DB::conn();
        $reportNames = [];
        if (!empty($pagesByReport)) {
            $reportIds = array_keys($pagesByReport);
            $placeholders = str_repeat('?,', count($reportIds) - 1) . '?';
            $stmt = $pdo->prepare("SELECT id, name FROM reports WHERE id IN ($placeholders)");
            $stmt->execute($reportIds);
            while ($row = $stmt->fetch()) {
                $reportNames[$row['id']] = $row['name'];
            }
        }

        // 获取设备名称
        $deviceNames = [];
        $allDeviceIds = [];
        foreach ($pagesByReport as $pages) {
            foreach ($pages as $page) {
                if ($page['device_id']) {
                    $allDeviceIds[] = $page['device_id'];
                }
            }
        }
        if (!empty($allDeviceIds)) {
            $uniqueDeviceIds = array_unique($allDeviceIds);
            if (!empty($uniqueDeviceIds)) {
                $placeholders = str_repeat('?,', count($uniqueDeviceIds) - 1) . '?';
                $stmt = $pdo->prepare("SELECT id, name FROM devices WHERE id IN ($placeholders)");
                $stmt->execute(array_values($uniqueDeviceIds));
                while ($row = $stmt->fetch()) {
                    $deviceNames[$row['id']] = $row['name'];
                }
            }
        }

        // 显示页面列表
        foreach ($pagesByReport as $reportId => $pages) {
            $reportName = $reportNames[$reportId] ?? "报表 $reportId";

            echo '<div class="mb-4">';
            echo '<h6 class="text-primary">' . htmlspecialchars($reportName) . ' (ID: ' . $reportId . ')</h6>';

            // 按设备分组
            $pagesByDevice = [];
            foreach ($pages as $page) {
                $deviceKey = $page['device_id'] ?? 'general';
                if (!isset($pagesByDevice[$deviceKey])) {
                    $pagesByDevice[$deviceKey] = [];
                }
                $pagesByDevice[$deviceKey][] = $page;
            }

            echo '<div class="row g-2">';
            foreach ($pagesByDevice as $deviceKey => $devicePages) {
                if ($deviceKey === 'general') {
                    continue; // 跳过通用页面
                }

                $deviceName = $deviceNames[$deviceKey] ?? "设备 $deviceKey";

                echo '<div class="col-md-6 col-lg-4">';
                echo '<div class="card card-sm">';
                echo '<div class="card-body p-2">';
                echo '<h6 class="card-title mb-2">' . htmlspecialchars($deviceName) . '</h6>';

                $entryPage = null;
                $queryPage = null;
                foreach ($devicePages as $page) {
                    if ($page['type'] === 'entry') {
                        $entryPage = $page;
                    } else {
                        $queryPage = $page;
                    }
                }

                echo '<div class="btn-group w-100 mb-2" role="group">';
                if ($entryPage) {
                    echo '<a href="' . BASE_URL . 'static_pages/' . $entryPage['filename'] . '" target="_blank" class="btn btn-sm btn-primary" title="预览录入页面">';
                    echo '<i class="fa fa-edit me-1"></i>录入';
                    echo '</a>';
                }
                if ($queryPage) {
                    echo '<a href="' . BASE_URL . 'static_pages/' . $queryPage['filename'] . '" target="_blank" class="btn btn-sm btn-success" title="预览查询页面">';
                    echo '<i class="fa fa-search me-1"></i>查询';
                    echo '</a>';
                }
                echo '</div>';

                // 显示文件信息
                if ($entryPage || $queryPage) {
                    $latestPage = $entryPage ?: $queryPage;
                    echo '<small class="text-muted">';
                    echo '大小: ' . $this->formatFileSize($latestPage['size']) . '<br>';
                    echo '更新: ' . date('m-d H:i', $latestPage['modified']);
                    echo '</small>';
                }

                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
            echo '</div>';
            echo '</div>';
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 1) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 1) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }

    /**
     * 获取简短的文件名（用于显示）
     */
    private function getShortFileName($filename)
    {
        // 移除扩展名
        $name = pathinfo($filename, PATHINFO_FILENAME);

        // 解析文件名格式：entry_report_1001_device_10 或 query_report_1001_device_10
        if (preg_match('/^(entry|query)_report_(\d+)_device_(\d+)$/', $name, $matches)) {
            $type = $matches[1] === 'entry' ? '录入' : '查询';
            $reportId = $matches[2];
            $deviceId = $matches[3];
            return "{$type}-{$reportId}-{$deviceId}";
        }

        // 如果不匹配标准格式，返回原文件名（截断）
        return strlen($name) > 20 ? substr($name, 0, 17) . '...' : $name;
    }

    // API: 清理指定报表的静态页面
    public function cleanupReport(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $reportId = $input['report_id'] ?? null;

            if (!$reportId) {
                echo json_encode(['success' => false, 'message' => '缺少报表ID']);
                return '';
            }

            $staticDir = __DIR__ . '/../../static_pages/';
            $deletedCount = 0;

            if (is_dir($staticDir)) {
                // 查找该报表相关的所有文件
                $pattern = $staticDir . "*_report_{$reportId}_*.html";
                $files = glob($pattern);

                foreach ($files as $file) {
                    if (unlink($file)) {
                        $deletedCount++;
                    }
                }
            }

            echo json_encode([
                'success' => true,
                'message' => "已清理报表 {$reportId} 的 {$deletedCount} 个静态文件"
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '清理失败: ' . $e->getMessage()
            ]);
        }

        return '';
    }

    /**
     * 嵌入模式显示静态页面（用于iframe）
     */
    public function embed(): string
    {
        $filename = $_GET['file'] ?? '';

        if (empty($filename)) {
            http_response_code(400);
            return '缺少文件参数';
        }

        // 安全检查：只允许HTML文件，防止路径遍历
        if (!preg_match('/^[a-zA-Z0-9_\-]+\.html$/', $filename)) {
            http_response_code(400);
            return '无效的文件名';
        }

        $staticDir = __DIR__ . '/../../static_pages/';
        $filePath = $staticDir . $filename;

        // 检查文件是否存在
        if (!file_exists($filePath)) {
            http_response_code(404);
            return '文件不存在';
        }

        // 设置正确的Content-Type
        header('Content-Type: text/html; charset=UTF-8');

        // 直接输出文件内容
        return file_get_contents($filePath);
    }
}
