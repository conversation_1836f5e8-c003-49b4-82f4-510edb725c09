<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

/**
 * 静态页面菜单控制器
 * 生成与原系统菜单结构一致的静态页面菜单
 */
class StaticMenuController
{
    /**
     * 静态页面菜单首页
     */
    public function index(): string
    {
        Auth::requireLogin();
        
        ob_start();
        $title = '静态页面菜单';
        
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        
        echo '<main class="container-fluid py-3">';
        $pageTitle = '静态页面菜单';
        include __DIR__ . '/../Views/layout/page_head.php';
        
        echo '<div class="row">';
        echo '<div class="col-12">';
        
        echo '<div class="card">';
        echo '<div class="card-header">';
        echo '<h5 class="mb-0">静态页面快速访问</h5>';
        echo '</div>';
        echo '<div class="card-body">';
        
        // 获取报表和设备数据
        $reportsByGroup = $this->getReportsByGroup();
        $devicesByReport = $this->getDevicesByReport();
        $staticPages = $this->getStaticPages();
        
        foreach (['site', 'cb26', 'ctrl'] as $group) {
            $groupName = $this->getGroupName($group);
            $reports = $reportsByGroup[$group] ?? [];
            
            if (empty($reports)) continue;
            
            echo '<h6 class="text-primary mt-4 mb-3">' . $groupName . '</h6>';
            
            foreach ($reports as $report) {
                $reportId = $report['id'];
                $devices = $devicesByReport[$reportId] ?? [];
                
                echo '<div class="mb-3">';
                echo '<h6>' . htmlspecialchars($report['name']) . '</h6>';
                
                if (empty($devices)) {
                    // 没有设备，显示通用页面
                    $entryFile = "entry_report_{$reportId}.html";
                    $queryFile = "query_report_{$reportId}.html";
                    
                    echo '<div class="btn-group me-2" role="group">';
                    if (isset($staticPages[$entryFile])) {
                        echo '<a href="' . BASE_URL . 'static_pages/' . $entryFile . '" class="btn btn-sm btn-primary" target="_blank">录入</a>';
                    } else {
                        echo '<a href="' . BASE_URL . 'index.php?r=entry/report&id=' . $reportId . '" class="btn btn-sm btn-outline-primary">录入</a>';
                    }
                    
                    if (isset($staticPages[$queryFile])) {
                        echo '<a href="' . BASE_URL . 'static_pages/' . $queryFile . '" class="btn btn-sm btn-success" target="_blank">查询</a>';
                    } else {
                        echo '<a href="' . BASE_URL . 'index.php?r=query/generic&report_id=' . $reportId . '" class="btn btn-sm btn-outline-success">查询</a>';
                    }
                    echo '</div>';
                } else {
                    // 有设备，按设备显示
                    echo '<div class="row g-2">';
                    foreach ($devices as $device) {
                        $deviceId = $device['id'];
                        $deviceName = $device['name'];
                        
                        $entryFile = "entry_report_{$reportId}_device_{$deviceId}.html";
                        $queryFile = "query_report_{$reportId}_device_{$deviceId}.html";
                        
                        echo '<div class="col-md-6 col-lg-4">';
                        echo '<div class="card card-sm">';
                        echo '<div class="card-body p-2">';
                        echo '<h6 class="card-title mb-2">' . htmlspecialchars($deviceName) . '</h6>';
                        
                        echo '<div class="btn-group w-100" role="group">';
                        if (isset($staticPages[$entryFile])) {
                            echo '<a href="' . BASE_URL . 'static_pages/' . $entryFile . '" class="btn btn-sm btn-primary" target="_blank">录入</a>';
                        } else {
                            echo '<a href="' . BASE_URL . 'index.php?r=entry/report&id=' . $reportId . '&device_id=' . $deviceId . '" class="btn btn-sm btn-outline-primary">录入</a>';
                        }
                        
                        if (isset($staticPages[$queryFile])) {
                            echo '<a href="' . BASE_URL . 'static_pages/' . $queryFile . '" class="btn btn-sm btn-success" target="_blank">查询</a>';
                        } else {
                            echo '<a href="' . BASE_URL . 'index.php?r=query/generic&report_id=' . $reportId . '&device_id=' . $deviceId . '" class="btn btn-sm btn-outline-success">查询</a>';
                        }
                        echo '</div>';
                        
                        // 状态指示
                        if (isset($staticPages[$entryFile]) && isset($staticPages[$queryFile])) {
                            echo '<div class="text-center mt-1">';
                            echo '<span class="badge bg-success">静态</span>';
                            echo '</div>';
                        } else {
                            echo '<div class="text-center mt-1">';
                            echo '<span class="badge bg-warning">动态</span>';
                            echo '</div>';
                        }
                        
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                    }
                    echo '</div>';
                }
                
                echo '</div>';
            }
        }
        
        echo '</div>';
        echo '</div>';
        
        echo '</div>';
        echo '</div>';
        echo '</main>';
        
        include __DIR__ . '/../Views/layout/footer.php';
        
        return ob_get_clean();
    }
    
    /**
     * 获取按分组的报表
     */
    private function getReportsByGroup(): array
    {
        $reportsByGroup = ['site' => [], 'cb26' => [], 'ctrl' => []];
        
        try {
            $pdo = DB::conn();
            $sql = "SELECT r.id, r.name, r.enabled, rc.name AS category_name 
                    FROM reports r 
                    LEFT JOIN report_categories rc ON r.category_id = rc.id 
                    WHERE r.enabled = 1 
                    ORDER BY r.id";
            $rows = $pdo->query($sql)->fetchAll() ?: [];
            
            foreach ($rows as $r) {
                $cat = (string)($r['category_name'] ?? '');
                $key = 'site';
                if (stripos($cat, 'cb26') !== false) {
                    $key = 'cb26';
                } elseif (mb_strpos($cat, '中控') !== false || stripos($cat, 'ctrl') !== false) {
                    $key = 'ctrl';
                }
                $reportsByGroup[$key][] = $r;
            }
        } catch (\Throwable $e) {
            // 忽略错误
        }
        
        return $reportsByGroup;
    }
    
    /**
     * 获取报表关联的设备
     */
    private function getDevicesByReport(): array
    {
        $devicesByReport = [];
        
        try {
            $pdo = DB::conn();
            $sql = "SELECT DISTINCT rd.report_id, d.id, d.name 
                    FROM report_devices rd 
                    JOIN devices d ON rd.device_id = d.id 
                    ORDER BY rd.report_id, d.name";
            $deviceRows = $pdo->query($sql)->fetchAll() ?: [];
            
            foreach ($deviceRows as $device) {
                $devicesByReport[$device['report_id']][] = $device;
            }
        } catch (\Throwable $e) {
            // 忽略错误
        }
        
        return $devicesByReport;
    }
    
    /**
     * 获取静态页面列表
     */
    private function getStaticPages(): array
    {
        $staticPages = [];
        $staticDir = __DIR__ . '/../../static_pages/';
        
        if (is_dir($staticDir)) {
            $files = glob($staticDir . '*.html');
            foreach ($files as $file) {
                $filename = basename($file);
                $staticPages[$filename] = true;
            }
        }
        
        return $staticPages;
    }
    
    /**
     * 获取分组名称
     */
    private function getGroupName(string $group): string
    {
        $names = [
            'site' => '现场资料',
            'cb26' => 'CB26资料',
            'ctrl' => '中控室资料'
        ];
        
        return $names[$group] ?? $group;
    }
}
?>
