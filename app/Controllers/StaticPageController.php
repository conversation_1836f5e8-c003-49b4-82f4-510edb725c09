<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;

/**
 * 静态页面控制器
 * 负责处理静态页面的代理和路径修正
 */
class StaticPageController
{
    /**
     * 显示静态页面
     */
    public function show(): string
    {
        Auth::requireLogin();
        
        $filename = $_GET['file'] ?? '';
        if (empty($filename)) {
            http_response_code(400);
            return $this->errorPage('缺少文件参数');
        }
        
        // 安全检查：防止路径遍历攻击
        if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
            http_response_code(400);
            return $this->errorPage('无效的文件名');
        }
        
        $filePath = __DIR__ . '/../../static_pages/' . $filename;
        
        if (!file_exists($filePath) || !is_file($filePath)) {
            http_response_code(404);
            return $this->errorPage('静态页面不存在');
        }
        
        // 读取静态页面内容
        $content = file_get_contents($filePath);
        if ($content === false) {
            http_response_code(500);
            return $this->errorPage('无法读取静态页面');
        }
        
        // 修正路径
        $content = $this->fixPaths($content);
        
        // 注入用户信息
        $content = $this->injectUserInfo($content);
        
        // 添加前端集成脚本
        $content = $this->addIntegrationScript($content);
        
        return $content;
    }
    
    /**
     * 修正静态页面中的路径
     */
    private function fixPaths(string $content): string
    {
        $baseUrl = BASE_URL;
        
        // 修正CSS和JS文件路径
        $content = preg_replace('/href="\.\.\/css\//', 'href="' . $baseUrl . 'css/', $content);
        $content = preg_replace('/src="\.\.\/js\//', 'src="' . $baseUrl . 'js/', $content);
        
        // 修正图片路径
        $content = preg_replace('/src="\.\.\/images\//', 'src="' . $baseUrl . 'images/', $content);
        $content = preg_replace('/src="\.\.\/img\//', 'src="' . $baseUrl . 'img/', $content);
        
        // 修正链接路径
        $content = preg_replace('/href="\.\.\//', 'href="' . $baseUrl, $content);
        
        // 修正表单action路径
        $content = preg_replace('/action="\.\.\//', 'action="' . $baseUrl, $content);
        
        return $content;
    }
    
    /**
     * 注入用户信息
     */
    private function injectUserInfo(string $content): string
    {
        $username = $_SESSION['real_name'] ?? $_SESSION['username'] ?? '用户';
        
        // 替换静态页面中的用户信息
        $content = str_replace('静态页面用户', htmlspecialchars($username), $content);
        $content = str_replace('id="currentUser">静态页面用户', 'id="currentUser">' . htmlspecialchars($username), $content);
        
        return $content;
    }
    
    /**
     * 添加前端集成脚本
     */
    private function addIntegrationScript(string $content): string
    {
        $script = '
<script>
// 前端集成脚本
(function() {
    // 检查是否在iframe中
    if (window.parent !== window) {
        // 在iframe中，可以与父页面通信
        try {
            window.parent.postMessage({
                type: "static_page_loaded",
                title: document.title,
                url: window.location.href
            }, "*");
        } catch (e) {
            console.warn("无法与父页面通信:", e);
        }
    }
    
    // 修正返回按钮
    $(document).ready(function() {
        $("button[onclick*=\"history.back\"]").off("click").on("click", function(e) {
            e.preventDefault();
            if (window.parent !== window) {
                // 在iframe中，通知父页面关闭
                window.parent.postMessage({type: "close_static_page"}, "*");
            } else {
                // 直接页面，返回前端首页
                window.location.href = "' . BASE_URL . '";
            }
        });
        
        $("button[onclick*=\"window.close\"]").off("click").on("click", function(e) {
            e.preventDefault();
            if (window.parent !== window) {
                window.parent.postMessage({type: "close_static_page"}, "*");
            } else {
                window.location.href = "' . BASE_URL . '";
            }
        });
        
        // 修正首页链接
        $("a[href=\"../\"]").attr("href", "' . BASE_URL . '");
    });
})();
</script>';
        
        // 在</body>标签前插入脚本
        $content = str_replace('</body>', $script . '</body>', $content);
        
        return $content;
    }
    
    /**
     * 错误页面
     */
    private function errorPage(string $message): string
    {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>错误 - 设备资料录入管理系统</title>
            <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
            <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
        </head>
        <body>
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h4>页面错误</h4>
                                <p class="text-muted"><?php echo htmlspecialchars($message); ?></p>
                                <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                                    <i class="fa fa-home me-2"></i>返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
    
    /**
     * 获取静态页面列表（API）
     */
    public function list(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        if (!Auth::isLoggedIn()) {
            http_response_code(401);
            return json_encode(['success' => false, 'message' => '未登录']);
        }
        
        try {
            $staticDir = __DIR__ . '/../../static_pages';
            $pages = [];
            
            if (is_dir($staticDir)) {
                $files = glob($staticDir . '/*.html');
                foreach ($files as $file) {
                    $filename = basename($file);
                    $pages[] = [
                        'filename' => $filename,
                        'title' => $this->extractTitle($file),
                        'type' => $this->getPageType($filename),
                        'url' => BASE_URL . 'index.php?r=static/show&file=' . urlencode($filename),
                        'size' => filesize($file),
                        'modified' => filemtime($file)
                    ];
                }
            }
            
            return json_encode(['success' => true, 'data' => $pages]);
        } catch (\Exception $e) {
            http_response_code(500);
            return json_encode(['success' => false, 'message' => '获取页面列表失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 提取页面标题
     */
    private function extractTitle(string $filePath): string
    {
        $content = file_get_contents($filePath);
        if (preg_match('/<title>(.*?)<\/title>/i', $content, $matches)) {
            return trim($matches[1]);
        }
        return basename($filePath, '.html');
    }
    
    /**
     * 获取页面类型
     */
    private function getPageType(string $filename): string
    {
        if (strpos($filename, 'entry_') === 0) {
            return 'entry';
        } elseif (strpos($filename, 'query_') === 0) {
            return 'query';
        }
        return 'unknown';
    }
}
