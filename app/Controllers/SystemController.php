<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;
use App\Services\ConfigService;
use App\Services\AuditService;

class SystemController
{
    public function config(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        // 获取当前配置
        $configs = ConfigService::getAll();

        ob_start();
        $title = '系统配置';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = '系统配置';
        include __DIR__ . '/../Views/layout/page_head.php';
        include __DIR__ . '/../Views/system/config.php';
        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    public function saveConfig(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $configs = [
                'system_name' => [
                    'value' => $_POST['system_name'] ?? '设备资料录入管理系统',
                    'remark' => '系统名称'
                ],
                'system_version' => [
                    'value' => $_POST['system_version'] ?? '1.0.0',
                    'remark' => '系统版本'
                ],
                'session_ttl_days' => [
                    'value' => (int)($_POST['session_ttl_days'] ?? 7),
                    'remark' => '会话超时天数'
                ],
                'show_entry_info' => [
                    'value' => !empty($_POST['show_entry_info']),
                    'remark' => '是否显示录入信息（录入人、录入时间）'
                ],
                'show_modify_info' => [
                    'value' => !empty($_POST['show_modify_info']),
                    'remark' => '是否显示修改信息（修改人、修改时间）'
                ],
                'ip_acl_mode' => [
                    'value' => $_POST['ip_acl_mode'] ?? 'disabled',
                    'remark' => 'IP访问控制模式'
                ]
            ];

            if (ConfigService::setMultiple($configs)) {
                // 记录审计日志
                AuditService::log('config.save', 'system_config', [
                    'configs' => array_keys($configs)
                ]);

                header('Content-Type: application/json');
                return json_encode(['success' => true, 'message' => '配置保存成功']);
            } else {
                throw new \Exception('配置保存失败');
            }
        } catch (\Exception $e) {
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function ipAcl(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        $pdo = DB::conn();
        $stmt = $pdo->query('SELECT * FROM ip_acl ORDER BY type, id');
        $ipAcls = $stmt->fetchAll();

        ob_start();
        $title = 'IP访问控制';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = 'IP访问控制';
        include __DIR__ . '/../Views/layout/page_head.php';
        include __DIR__ . '/../Views/system/ip_acl.php';
        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    public function createIpAcl(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $type = trim($_POST['type'] ?? '');
            $cidr = trim($_POST['cidr'] ?? '');
            $remark = trim($_POST['remark'] ?? '');

            if (empty($type) || empty($cidr)) {
                throw new \Exception('类型和IP地址不能为空');
            }

            if (!in_array($type, ['whitelist', 'blacklist'])) {
                throw new \Exception('无效的类型');
            }

            // 验证IP地址或CIDR格式
            if (!$this->validateCidr($cidr)) {
                throw new \Exception('无效的IP地址或CIDR格式');
            }

            $pdo = DB::conn();

            // 检查是否已存在相同的规则
            $checkStmt = $pdo->prepare('SELECT id FROM ip_acl WHERE type = ? AND cidr = ?');
            $checkStmt->execute([$type, $cidr]);
            if ($checkStmt->fetch()) {
                throw new \Exception('相同的IP访问控制规则已存在');
            }

            $stmt = $pdo->prepare('INSERT INTO ip_acl (type, cidr, remark) VALUES (?, ?, ?)');
            $stmt->execute([$type, $cidr, $remark ?: null]);

            // 记录审计日志
            AuditService::log('ip_acl.create', 'ip_acl:' . $pdo->lastInsertId(), [
                'type' => $type,
                'cidr' => $cidr,
                'remark' => $remark
            ]);

            header('Content-Type: application/json');
            return json_encode(['success' => true, 'message' => 'IP访问控制规则创建成功']);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function updateIpAcl(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $aclId = (int)($_POST['acl_id'] ?? 0);
            $type = trim($_POST['type'] ?? '');
            $cidr = trim($_POST['cidr'] ?? '');
            $remark = trim($_POST['remark'] ?? '');

            if (!$aclId || empty($type) || empty($cidr)) {
                throw new \Exception('ID、类型和IP地址不能为空');
            }

            if (!in_array($type, ['whitelist', 'blacklist'])) {
                throw new \Exception('无效的类型');
            }

            // 验证IP地址或CIDR格式
            if (!$this->validateCidr($cidr)) {
                throw new \Exception('无效的IP地址或CIDR格式');
            }

            $pdo = DB::conn();

            // 检查规则是否存在
            $checkStmt = $pdo->prepare('SELECT type, cidr FROM ip_acl WHERE id = ?');
            $checkStmt->execute([$aclId]);
            $existing = $checkStmt->fetch();
            if (!$existing) {
                throw new \Exception('IP访问控制规则不存在');
            }

            // 检查是否与其他规则冲突
            $conflictStmt = $pdo->prepare('SELECT id FROM ip_acl WHERE type = ? AND cidr = ? AND id != ?');
            $conflictStmt->execute([$type, $cidr, $aclId]);
            if ($conflictStmt->fetch()) {
                throw new \Exception('相同的IP访问控制规则已存在');
            }

            $stmt = $pdo->prepare('UPDATE ip_acl SET type = ?, cidr = ?, remark = ? WHERE id = ?');
            $stmt->execute([$type, $cidr, $remark ?: null, $aclId]);

            // 记录审计日志
            AuditService::log('ip_acl.update', 'ip_acl:' . $aclId, [
                'old' => $existing,
                'new' => ['type' => $type, 'cidr' => $cidr, 'remark' => $remark]
            ]);

            header('Content-Type: application/json');
            return json_encode(['success' => true, 'message' => 'IP访问控制规则更新成功']);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function deleteIpAcl(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $aclId = (int)($_POST['acl_id'] ?? 0);

            if (!$aclId) {
                throw new \Exception('ID不能为空');
            }

            $pdo = DB::conn();

            // 获取要删除的规则信息
            $checkStmt = $pdo->prepare('SELECT type, cidr, remark FROM ip_acl WHERE id = ?');
            $checkStmt->execute([$aclId]);
            $acl = $checkStmt->fetch();
            if (!$acl) {
                throw new \Exception('IP访问控制规则不存在');
            }

            $stmt = $pdo->prepare('DELETE FROM ip_acl WHERE id = ?');
            $stmt->execute([$aclId]);

            // 记录审计日志
            AuditService::log('ip_acl.delete', 'ip_acl:' . $aclId, [
                'type' => $acl['type'],
                'cidr' => $acl['cidr'],
                'remark' => $acl['remark']
            ]);

            header('Content-Type: application/json');
            return json_encode(['success' => true, 'message' => 'IP访问控制规则删除成功']);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 验证CIDR格式
     */
    private function validateCidr(string $cidr): bool
    {
        // 检查是否是单个IP地址
        if (filter_var($cidr, FILTER_VALIDATE_IP)) {
            return true;
        }

        // 检查是否是CIDR格式
        if (strpos($cidr, '/') !== false) {
            [$ip, $mask] = explode('/', $cidr, 2);

            // 验证IP地址
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                return false;
            }

            // 验证子网掩码
            $mask = (int)$mask;
            if ($mask < 0 || $mask > 32) {
                return false;
            }

            return true;
        }

        return false;
    }
}

