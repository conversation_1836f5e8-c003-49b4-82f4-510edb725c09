<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;

class TemplatesController
{
    public function index(): string
    {
        Auth::requireLogin();
        // 检查管理员权限
        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            http_response_code(403);
            return '权限不足：需要管理员权限';
        }

        ob_start();
        $title = '模板管理';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = '模板管理';
        include __DIR__ . '/../Views/layout/page_head.php';

        // 获取模板列表
        $pdo = DB::conn();
        $templates = $pdo->query("SELECT * FROM report_templates ORDER BY code")->fetchAll();

        echo '<div class="card mb-3"><div class="card-body">';
        echo '<div class="d-flex justify-content-between align-items-center mb-3">';
        echo '<h5 class="mb-0">录入模板列表</h5>';
        echo '<button class="btn btn-primary btn-sm" onclick="showTemplateModal()">新增模板</button>';
        echo '</div>';

        echo '<div class="table-responsive">';
        echo '<table class="table table-hover table-sm">';
        echo '<thead><tr><th style="width:120px">模板代码</th><th style="width:180px">模板名称</th><th style="width:100px">报表类型</th><th style="width:80px">字段数量</th><th style="width:120px">创建时间</th><th style="width:160px">操作</th></tr></thead>';
        echo '<tbody>';
        foreach ($templates as $template) {
            $fieldsConfig = json_decode($template['fields_config'], true);
            $fieldCount = isset($fieldsConfig['fields']) ? count($fieldsConfig['fields']) : 0;
            $reportType = $template['report_type'] ?? 'daily';
            $reportTypeLabel = $reportType === 'continuous' ? '连续报表' : '日报表';
            $reportTypeBadge = $reportType === 'continuous' ? 'bg-info' : 'bg-success';

            echo '<tr>';
            echo '<td><code class="small">' . htmlspecialchars($template['code']) . '</code></td>';
            echo '<td>' . htmlspecialchars($template['name']) . '</td>';
            echo '<td><span class="badge ' . $reportTypeBadge . '">' . $reportTypeLabel . '</span></td>';
            echo '<td class="text-center">' . $fieldCount . ' 个</td>';
            echo '<td class="small">' . date('m-d H:i', strtotime($template['created_at'])) . '</td>';
            echo '<td>';
            echo '<button class="btn btn-sm btn-outline-primary me-1" onclick="editTemplate(' . $template['id'] . ')">编辑</button>';
            echo '<button class="btn btn-sm btn-outline-info me-1" onclick="viewTemplate(' . $template['id'] . ')">查看</button>';
            echo '<button class="btn btn-sm btn-outline-danger" onclick="deleteTemplate(' . $template['id'] . ')">删除</button>';
            echo '</td>';
            echo '</tr>';
        }
        echo '</tbody></table>';
        echo '</div>';
        echo '</div></div>';

        // 模板详情区域
        echo '<div class="card"><div class="card-body">';
        echo '<h5 class="mb-3">模板详情</h5>';
        echo '<div id="templateDetail">';
        echo '<p class="text-muted">请从上方表格选择一个模板查看详情。</p>';
        echo '</div>';
        echo '</div></div>';

        // 模板编辑模态框
        $this->renderTemplateModal();

        echo '<script>';
        echo 'window.__TEMPLATES_API__ = {';
        echo '  getTemplate: "'.BASE_URL.'index.php?r=templates/get",';
        echo '  saveTemplate: "'.BASE_URL.'index.php?r=templates/save",';
        echo '  deleteTemplate: "'.BASE_URL.'index.php?r=templates/delete"';
        echo '};';

        // 添加JavaScript功能
        echo '/*
        function showTemplateModal(templateId = null) {
            const modal = new bootstrap.Modal(document.getElementById("templateModal"));
            const form = document.getElementById("templateForm");
            const title = document.getElementById("templateModalTitle");

            if (templateId) {
                title.textContent = "编辑模板";
                loadTemplate(templateId);
            } else {
                title.textContent = "新增模板";
                form.reset();
                document.getElementById("templateId").value = "";
            }

            modal.show();
        }

        function editTemplate(templateId) {
            showTemplateModal(templateId);
        }

        function viewTemplate(templateId) {
            fetch(buildUrl(window.__TEMPLATES_API__.getTemplate, {id: templateId}))
                .then(function(response){ return response.json(); })
                .then(function(result){
                    if (!result.success) { showMessage(result.message || "加载模板详情失败", "error"); return; }
                    var template = result.data;
                    var fieldsConfig = {};
                    try { fieldsConfig = JSON.parse(template.fields_config || "{\"fields\":[]}"); } catch(e){ fieldsConfig = {"fields":[]}; }

                    var detailHtml = ""
                        + "<div class=\"row g-3\">"
                        +   "<div class=\"col-md-6\"><strong>模板代码：</strong><code>" + escapeHtml(template.code) + "</code></div>"
                        +   "<div class=\"col-md-6\"><strong>模板名称：</strong>" + escapeHtml(template.name) + "</div>"
                        +   "<div class=\"col-12\"><strong>字段配置：</strong>"
                        +     "<div class=\"table-responsive mt-2\">"
                        +       "<table class=\"table table-sm table-bordered\">"
                        +         "<thead><tr><th>字段键</th><th>字段名</th><th>类型</th><th>其他属性</th></tr></thead>"
                        +         "<tbody>";

                    if (fieldsConfig.fields) {
                        fieldsConfig.fields.forEach(function(field){
                            var otherAttrs = Object.keys(field)
                                .filter(function(key){ return ["key","label","type"].indexOf(key) === -1; })
                                .map(function(key){ return key + ": " + field[key]; })
                                .join(", ");
                            detailHtml += "<tr>"
                                + "<td><code>" + escapeHtml(field.key) + "</code></td>"
                                + "<td>" + escapeHtml(field.label) + "</td>"
                                + "<td><span class=\"badge bg-info\">" + escapeHtml(field.type) + "</span></td>"
                                + "<td>" + escapeHtml(otherAttrs) + "</td>"
                                + "</tr>";
                        });
                    }

                    detailHtml += "</tbody></table></div></div>"
                        + "<div class=\"col-12\"><strong>创建时间：</strong>" + template.created_at + "</div>"
                        + "<div class=\"col-12\"><strong>更新时间：</strong>" + template.updated_at + "</div>"
                        + "</div>";

                    document.getElementById("templateDetail").innerHTML = detailHtml;
                })
                .catch(function(error){ console.error("加载模板详情失败:", error); showMessage("网络错误，加载失败", "error"); });
        }

        function loadTemplate(templateId) {
            fetch(buildUrl(window.__TEMPLATES_API__.getTemplate, {id: templateId}))
                .then(function(r){ return r.json(); })
                .then(function(result){
                    if (!result.success) { showMessage(result.message||"加载模板失败","error"); return; }
                    const template = result.data;
                    document.getElementById("templateId").value = template.id;
                    document.getElementById("templateCode").value = template.code;
                    document.getElementById("templateName").value = template.name;
                    const fieldsJson = template.fields_config || "{\"fields\":[]}";
                    document.getElementById("templateFields").value = fieldsJson;
                    renderFieldEditor(fieldsJson);
                })
                .catch(function(error){ console.error("加载模板失败:", error); showMessage("网络错误，加载失败","error"); });
        }

        function saveTemplate() {
            // 从可视化表格收集，生成JSON
            const json = collectFieldsToJson();
            if (!json) return;
            document.getElementById("templateFields").value = json;

            const form = document.getElementById("templateForm");
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            try { JSON.parse(data.fields_config); }
            catch(e){ showMessage("字段配置JSON格式错误","error"); return; }

            fetch(window.__TEMPLATES_API__.saveTemplate, {
                method: "POST", headers: {"Content-Type": "application/json"},
                body: JSON.stringify(data)
            })
            .then(function(r){ return r.json(); })
            .then(result => {
                if (result.success) {
                    showMessage(result.message, "success");
                    bootstrap.Modal.getInstance(document.getElementById("templateModal")).hide();
                    setTimeout(function(){ location.reload(); }, 800);
                } else {
                    showMessage(result.message || "保存失败", "error");
                }
            })
            .catch(function(err){ console.error("保存失败:", err); showMessage("网络错误，保存失败", "error"); });
        }

        function deleteTemplate(templateId) {
            if (!confirm("确定要删除这个模板吗？此操作不可撤销。")) {
                return;
            }

            fetch(window.__TEMPLATES_API__.deleteTemplate, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({id: templateId})
            })
            .then(function(response){ return response.json(); })
            .then(result => {
                if (result.success) {
                    showMessage(result.message, "success");
                    setTimeout(function(){ location.reload(); }, 1000);
                } else {
                    showMessage(result.message || "删除失败", "error");
                }
            })
            .catch(error => {
                console.error("删除失败:", error);
                showMessage("网络错误，删除失败", "error");
            });
        }

        function showMessage(message, type = "info") {
            const cls = {success:"alert-success", error:"alert-danger", warning:"alert-warning", info:"alert-info"}[type]||"alert-info";
            const html = "<div class=\"alert "+cls+" alert-dismissible fade show\" role=\"alert\">"
              + String(message) + "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>";
            let c = document.getElementById("messageContainer");
            if (!c){ c=document.createElement("div"); c.id="messageContainer"; c.style.position="fixed"; c.style.top="80px"; c.style.right="20px"; c.style.zIndex="9999"; c.style.maxWidth="400px"; document.body.appendChild(c);}
            c.innerHTML = html;
            if (type!=="error") setTimeout(function(){ const a=c.querySelector(".alert"); if(a){a.classList.remove("show"); setTimeout(function(){a.remove();},150);} }, 3000);
        }

        // --- 可视化编辑器脚本 ---
        function renderFieldEditor(fieldsJson){
            let config; try{ config = JSON.parse(fieldsJson||"{\"fields\":[]}"); }catch(e){ config={fields:[]}; }
            const tbody = document.querySelector("#fieldTable tbody");
            tbody.innerHTML = "";
            (config.fields||[]).forEach(addFieldRow);
        }

        function addFieldRow(field){
            const tbody = document.querySelector("#fieldTable tbody");
            const tr = document.createElement("tr");
            const f = Object.assign({key:"",label:"",type:"number",step:"0.01",min:"",max:"",width:"",unit:"",group:"",required:false}, field||{});
            tr.innerHTML =
              "<td><input class=\"form-control form-control-sm\" data-k=\"key\" value=\""+escapeHtml(f.key)+"\" placeholder=\"voltage_v\"/></td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"label\" value=\""+escapeHtml(f.label)+"\" placeholder=\"电压(V)\"/></td>"+
              "<td><select class=\"form-select form-select-sm\" data-k=\"type\" onchange=\"toggleSelectOptions(this)\">"
                    + ["number","text","time","date","select"].map(function(t){
                        const typeNames = {number:"数值",text:"文本",time:"时间",date:"日期",select:"下拉选择"};
                        return "<option value=\""+t+"\" "+(f.type===t?"selected":"")+">"+(typeNames[t]||t)+"</option>";
                    }).join("") +
                 "</select>"+(f.type==="select"?"<div class=\"mt-1\"><button type=\"button\" class=\"btn btn-xs btn-outline-info\" onclick=\"editSelectOptions(this)\" style=\"font-size:9px;padding:1px 3px;\">选项</button></div>":"")+"</td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"step\" type=\"text\" value=\""+escapeHtml(String(f.step||""))+"\" placeholder=\"0.01\"/></td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"min\" type=\"text\" value=\""+escapeHtml(String(f.min||""))+"\" placeholder=\"0\"/></td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"max\" type=\"text\" value=\""+escapeHtml(String(f.max||""))+"\" placeholder=\"100\"/></td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"width\" type=\"text\" value=\""+escapeHtml(String(f.width||""))+"\" placeholder=\"80px\"/></td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"unit\" type=\"text\" value=\""+escapeHtml(String(f.unit||""))+"\" placeholder=\"V\"/></td>"+
              "<td><input class=\"form-control form-control-sm\" data-k=\"group\" type=\"text\" value=\""+escapeHtml(String(f.group||""))+"\" placeholder=\"基础参数\"/></td>"+
              "<td><div class=\"form-check form-switch d-flex justify-content-center\"><input class=\"form-check-input\" data-k=\"required\" type=\"checkbox\" "+(f.required?"checked":"")+" /></div></td>"+
              "<td><div class=\"d-flex gap-1 justify-content-center\">"
                 +"<button type=\"button\" class=\"btn btn-outline-danger btn-sm\" onclick=\"this.closest(\\\"tr\\\").remove()\" title=\"删除\" style=\"padding:1px 4px;font-size:10px;\"><i class=\"fa fa-trash\"></i></button>"
                 +"<button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" onclick=\"moveRow(this,-1)\" title=\"上移\" style=\"padding:1px 4px;font-size:10px;\"><i class=\"fa fa-arrow-up\"></i></button>"
                 +"<button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" onclick=\"moveRow(this,1)\" title=\"下移\" style=\"padding:1px 4px;font-size:10px;\"><i class=\"fa fa-arrow-down\"></i></button>"
                 +"</div></td>";
            if (f.options) tr.dataset.options = JSON.stringify(f.options);
            tbody.appendChild(tr);

            // 确保必填项开关状态正确显示
            const requiredCheckbox = tr.querySelector("[data-k=\"required\"]");
            if (requiredCheckbox && f.required) {
                requiredCheckbox.checked = true;
            }
        }

        function moveRow(btn, delta){
            const tr = btn.closest("tr");
            const tbody = tr.parentNode;
            const rows = Array.from(tbody.children);
            const i = rows.indexOf(tr);
            const j = i + delta; if (j<0 || j>=rows.length) return;
            if (delta<0) tbody.insertBefore(tr, rows[j]); else tbody.insertBefore(rows[j], tr);
        }

        function collectFieldsToJson(){
            const rows = document.querySelectorAll("#fieldTable tbody tr");
            const fields = [];
            for (const tr of rows){
                const get = function(sel){ return tr.querySelector(sel); };
                const key = get("[data-k=\"key\"]").value.trim();
                const label = get("[data-k=\"label\"]").value.trim();
                if (!key || !label){ showMessage("字段键和字段名不能为空","error"); return null; }
                const type = get("[data-k=\"type\"]").value;
                const step = get("[data-k=\"step\"]").value.trim();
                const min = get("[data-k=\"min\"]").value.trim();
                const max = get("[data-k=\"max\"]").value.trim();
                const width = get("[data-k=\"width\"]").value.trim();
                const unit = get("[data-k=\"unit\"]").value.trim();
                const group = get("[data-k=\"group\"]").value.trim();
                const required = get("[data-k=\"required\"]").checked;
                const field = {key, label, type};
                if (step) field.step = step;
                if (min!=="") field.min = isNaN(Number(min)) ? min : Number(min);
                if (max!=="") field.max = isNaN(Number(max)) ? max : Number(max);
                if (width) field.width = width;
                if (unit) field.unit = unit;
                if (group) field.group = group;
                field.required = required; // 明确保存必填项状态，无论是 true 还是 false
                if (type === "select" && tr.dataset.options) {
                    try { field.options = JSON.parse(tr.dataset.options); } catch(e) {}
                }
                fields.push(field);
            }
            return JSON.stringify({fields});
        }

        function escapeHtml(s){ const d=document.createElement("div"); d.textContent=String(s||""); return d.innerHTML; }

        // select类型选项管理
        function toggleSelectOptions(selectEl){
            const td = selectEl.parentNode;
            const isSelect = selectEl.value === "select";
            let btn = td.querySelector(".btn-outline-info");
            if (isSelect && !btn){
                btn = document.createElement("button");
                btn.type = "button";
                btn.className = "btn btn-sm btn-outline-info mt-1";
                btn.textContent = "管理选项";
                btn.onclick = function(){ editSelectOptions(btn); };
                td.appendChild(btn);
            } else if (!isSelect && btn){
                btn.remove();
            }
        }

        function editSelectOptions(btn){
            const tr = btn.closest("tr");
            const field = collectFieldFromRow(tr);
            const options = field.options || [];

            const modal = document.createElement("div");
            modal.className = "modal fade";
            modal.innerHTML = "<div class=\"modal-dialog\"><div class=\"modal-content\">"
                + "<div class=\"modal-header\"><h5 class=\"modal-title\">编辑选项 - "+escapeHtml(field.label||field.key)+"</h5>"
                + "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button></div>"
                + "<div class=\"modal-body\">"
                + "<div class=\"mb-2\"><button type=\"button\" class=\"btn btn-sm btn-primary\" onclick=\"addOption()\">新增选项</button></div>"
                + "<div id=\"optionsList\"></div></div>"
                + "<div class=\"modal-footer\">"
                + "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">取消</button>"
                + "<button type=\"button\" class=\"btn btn-primary\" onclick=\"saveOptions()\">保存</button>"
                + "</div></div></div>";
            document.body.appendChild(modal);

            window.currentEditingRow = tr;
            window.currentOptions = options.slice();
            renderOptions();

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            modal.addEventListener("hidden.bs.modal", function(){ modal.remove(); });
        }

        function collectFieldFromRow(tr){
            const get = function(sel){ return tr.querySelector(sel); };
            return {
                key: get("[data-k=\"key\"]").value.trim(),
                label: get("[data-k=\"label\"]").value.trim(),
                type: get("[data-k=\"type\"]").value,
                step: get("[data-k=\"step\"]").value.trim(),
                min: get("[data-k=\"min\"]").value.trim(),
                max: get("[data-k=\"max\"]").value.trim(),
                required: get("[data-k=\"required\"]").checked,
                options: JSON.parse(tr.dataset.options || "[]")
            };
        }

        function renderOptions(){
            const container = document.getElementById("optionsList");
            container.innerHTML = "";
            window.currentOptions.forEach(function(opt, i){
                const div = document.createElement("div");
                div.className = "input-group mb-2";
                div.innerHTML = "<input type=\"text\" class=\"form-control\" value=\""+escapeHtml(opt.value||"")+"\" placeholder=\"选项值\" data-i=\""+i+"\" data-k=\"value\">"
                    + "<input type=\"text\" class=\"form-control\" value=\""+escapeHtml(opt.label||"")+"\" placeholder=\"显示文本\" data-i=\""+i+"\" data-k=\"label\">"
                    + "<button type=\"button\" class=\"btn btn-outline-danger\" onclick=\"removeOption("+i+")\">删除</button>";
                container.appendChild(div);
            });
        }

        function addOption(){
            window.currentOptions.push({value:"", label:""});
            renderOptions();
        }

        function removeOption(index){
            window.currentOptions.splice(index, 1);
            renderOptions();
        }

        function saveOptions(){
            const inputs = document.querySelectorAll("#optionsList input");
            const options = [];
            const optMap = {};
            inputs.forEach(function(inp){
                const i = parseInt(inp.dataset.i);
                const k = inp.dataset.k;
                if (!optMap[i]) optMap[i] = {};
                optMap[i][k] = inp.value.trim();
            });
            Object.values(optMap).forEach(function(opt){
                if (opt.value) options.push(opt);
            });

            window.currentEditingRow.dataset.options = JSON.stringify(options);
            bootstrap.Modal.getInstance(document.querySelector(".modal.show")).hide();
        }

        function buildUrl(base, params) {
            const hasQuery = base.includes("?");
            const sep = hasQuery ? "&" : "?";
            const query = new URLSearchParams(params).toString();
            return base + sep + query;
        }
        */';
        echo '</script>';
        echo '<script src="'.BASE_URL.'js/templates-page.js?v='.time().'"></script>';

        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    private function renderTemplateModal(): void
    {
        echo '<div class="modal fade" id="templateModal" tabindex="-1">';
        echo '<div class="modal-dialog modal-xl" style="max-width: 1200px;">';
        echo '<div class="modal-content">';
        echo '<div class="modal-header">';
        echo '<h5 class="modal-title" id="templateModalTitle">新增模板</h5>';
        echo '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>';
        echo '</div>';
        echo '<div class="modal-body">';
        echo '<form id="templateForm">';
        echo '<input type="hidden" id="templateId" name="id">';

        echo '<div class="row g-3">';
        echo '<div class="col-md-6">';
        echo '<label class="form-label">模板代码 *</label>';
        echo '<input type="text" class="form-control" id="templateCode" name="code" required>';
        echo '<div class="form-text">英文字母和下划线，如：pumps、compressor</div>';
        echo '</div>';
        echo '<div class="col-md-6">';
        echo '<label class="form-label">模板名称 *</label>';
        echo '<input type="text" class="form-control" id="templateName" name="name" required>';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<label class="form-label">报表类型 *</label>';
        echo '<select class="form-select" id="templateReportType" name="report_type" required>';
        echo '<option value="daily">日报表（固定时间段）</option>';
        echo '<option value="continuous">连续报表（间歇运行）</option>';
        echo '</select>';
        echo '<div class="form-text">日报表：按固定时间段录入；连续报表：每次运行记录一行</div>';
        echo '</div>';

        echo '<div class="col-12">';
        echo '<label class="form-label">字段配置 *</label>';
        // 可视化字段编辑器容器
        echo '<div id="fieldEditor" class="border rounded p-2 bg-light">';
        echo '  <style>';
        echo '    #fieldTable { table-layout: fixed; width: 100%; }';
        echo '    #fieldTable th, #fieldTable td { vertical-align: middle; padding: 6px 3px; text-align: center; font-size: 12px; }';
        echo '    #fieldTable input, #fieldTable select { font-size: 11px; text-align: center; padding: 2px 4px; }';
        echo '    #fieldTable input[data-k="key"] { text-align: left; font-family: monospace; font-size: 10px; }';
        echo '    #fieldTable input[data-k="label"] { text-align: left; font-size: 11px; }';
        echo '    #fieldTable input[data-k="group"] { text-align: left; font-size: 11px; }';
        echo '    #fieldTable .btn-sm { font-size: 9px; padding: 1px 4px; }';
        echo '    #fieldTable .btn-group-sm .btn { margin: 0.5px; }';
        echo '    .form-switch { display: inline-block; }';
        echo '    .form-switch .form-check-input { width: 1.8em; height: 0.9em; }';
        echo '    #fieldTable th { background-color: #f8f9fa; font-weight: 600; font-size: 11px; position: sticky; top: 0; z-index: 10; }';
        echo '    .table-responsive { max-height: 400px; overflow-y: auto; overflow-x: auto; }';
        echo '    @media (max-width: 1200px) {';
        echo '      #fieldTable th, #fieldTable td { font-size: 10px; padding: 4px 2px; }';
        echo '      #fieldTable input, #fieldTable select { font-size: 10px; padding: 1px 3px; }';
        echo '      #fieldTable .btn-sm { font-size: 8px; padding: 1px 3px; }';
        echo '    }';
        echo '  </style>';
        echo '  <div class="d-flex justify-content-between align-items-center mb-2">';
        echo '    <div class="small text-muted">直观编辑字段：字段键、字段名、类型、小数位、范围、必填等</div>';
        echo '    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addFieldRow()"><i class="fa fa-plus me-1"></i>新增字段</button>';
        echo '  </div>';
        echo '  <div class="table-responsive">';
        echo '    <table class="table table-sm table-bordered align-middle" id="fieldTable">';
        echo '      <thead class="table-light"><tr>';
        echo '        <th style="width:12%;">字段键</th>';
        echo '        <th style="width:12%;">字段名</th>';
        echo '        <th style="width:10%;">类型</th>';
        echo '        <th style="width:8%;">小数位</th>';
        echo '        <th style="width:8%;">最小值</th>';
        echo '        <th style="width:8%;">最大值</th>';
        echo '        <th style="width:8%;">列宽</th>';
        echo '        <th style="width:9%;">单位</th>';
        echo '        <th style="width:10%;">分组</th>';
        echo '        <th style="width:6%;" class="text-center">必填</th>';
        echo '        <th style="width:9%;">操作</th>';
        echo '      </tr></thead>';
        echo '      <tbody></tbody>';
        echo '    </table>';
        echo '  </div>';
        echo '</div>';
        // 隐藏域：与后端兼容
        echo '<textarea class="form-control d-none" id="templateFields" name="fields_config" rows="10" required></textarea>';
        echo '<div class="form-text">可视化编辑自动生成JSON，无需手写</div>';
        echo '</div>';
        echo '</div>';

        echo '</form>';
        echo '</div>';
        echo '<div class="modal-footer">';
        echo '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>';
        echo '<button type="button" class="btn btn-primary" onclick="saveTemplate()">保存</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    // API: 获取模板详情
    public function getTemplate(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        $templateId = (int)($_GET['id'] ?? 0);
        if (!$templateId) {
            echo json_encode(['success' => false, 'message' => '模板ID不能为空']);
            return '';
        }

        $pdo = DB::conn();
        $stmt = $pdo->prepare("SELECT * FROM report_templates WHERE id=?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch();

        if (!$template) {
            echo json_encode(['success' => false, 'message' => '模板不存在']);
            return '';
        }

        echo json_encode(['success' => true, 'data' => $template], JSON_UNESCAPED_UNICODE);
        return '';
    }

    // API: 保存模板
    public function saveTemplate(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $templateId = (int)($input['id'] ?? 0);
        $code = trim($input['code'] ?? '');
        $name = trim($input['name'] ?? '');
        $reportType = trim($input['report_type'] ?? 'daily');
        $fieldsConfig = trim($input['fields_config'] ?? '');

        // 验证必填字段
        if (!$code || !$name || !$fieldsConfig) {
            echo json_encode(['success' => false, 'message' => '模板代码、名称和字段配置不能为空']);
            return '';
        }

        // 验证报表类型
        if (!in_array($reportType, ['daily', 'continuous'])) {
            echo json_encode(['success' => false, 'message' => '报表类型无效']);
            return '';
        }

        // 验证JSON格式
        $fieldsData = json_decode($fieldsConfig, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode(['success' => false, 'message' => '字段配置JSON格式错误']);
            return '';
        }

        $pdo = DB::conn();

        try {
            if ($templateId) {
                // 更新
                $stmt = $pdo->prepare("UPDATE report_templates SET code=?, name=?, report_type=?, fields_config=? WHERE id=?");
                $stmt->execute([$code, $name, $reportType, $fieldsConfig, $templateId]);
                $message = '模板更新成功';
            } else {
                // 新增
                $stmt = $pdo->prepare("INSERT INTO report_templates (code, name, report_type, fields_config) VALUES (?, ?, ?, ?)");
                $stmt->execute([$code, $name, $reportType, $fieldsConfig]);
                $templateId = $pdo->lastInsertId();
                $message = '模板创建成功';
            }

            echo json_encode(['success' => true, 'message' => $message, 'id' => $templateId]);
        } catch (\Throwable $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo json_encode(['success' => false, 'message' => '模板代码已存在']);
            } else {
                echo json_encode(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
            }
        }

        return '';
    }

    // API: 删除模板
    public function deleteTemplate(): string
    {
        Auth::requireLogin();
        header('Content-Type: application/json; charset=UTF-8');

        if (empty($_SESSION['roles']) || !in_array('admin', $_SESSION['roles'])) {
            echo json_encode(['success' => false, 'message' => '权限不足']);
            return '';
        }

        $input = json_decode(file_get_contents('php://input'), true) ?: [];
        $templateId = (int)($input['id'] ?? 0);

        if (!$templateId) {
            echo json_encode(['success' => false, 'message' => '模板ID不能为空']);
            return '';
        }

        $pdo = DB::conn();

        try {
            // 检查是否有报表在使用此模板
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM reports WHERE template_code = (SELECT code FROM report_templates WHERE id=?)");
            $stmt->execute([$templateId]);
            $count = $stmt->fetchColumn();

            if ($count > 0) {
                echo json_encode(['success' => false, 'message' => "无法删除：有 {$count} 个报表正在使用此模板"]);
                return '';
            }

            $stmt = $pdo->prepare("DELETE FROM report_templates WHERE id=?");
            $stmt->execute([$templateId]);

            echo json_encode(['success' => true, 'message' => '模板删除成功']);
        } catch (\Throwable $e) {
            echo json_encode(['success' => false, 'message' => '删除失败：' . $e->getMessage()]);
        }

        return '';
    }
}
