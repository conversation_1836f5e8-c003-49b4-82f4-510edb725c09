<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;

class UserController
{
    /**
     * API: 获取当前用户信息
     */
    public function current(): string
    {
        header('Content-Type: application/json; charset=UTF-8');
        
        try {
            // 检查是否已登录
            if (empty($_SESSION['user'])) {
                echo json_encode([
                    'success' => false, 
                    'message' => '未登录',
                    'user' => null
                ]);
                return '';
            }
            
            $user = $_SESSION['user'];
            
            // 返回安全的用户信息（不包含敏感数据）
            $safeUser = [
                'id' => $user['id'] ?? null,
                'username' => $user['username'] ?? '',
                'real_name' => $user['real_name'] ?? '',
                'roles' => $_SESSION['roles'] ?? []
            ];
            
            echo json_encode([
                'success' => true,
                'user' => $safeUser
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取用户信息失败: ' . $e->getMessage(),
                'user' => null
            ]);
        }
        
        return '';
    }
}
?>
