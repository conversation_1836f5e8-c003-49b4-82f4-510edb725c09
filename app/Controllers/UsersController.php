<?php
declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\DB;
use App\Services\AuditService;

class UsersController
{
    public function index(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        // 获取用户列表
        $pdo = DB::conn();
        $stmt = $pdo->query('
            SELECT u.*, GROUP_CONCAT(r.name) as role_names, GROUP_CONCAT(r.code) as role_codes
            FROM users u
            LEFT JOIN user_role ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            GROUP BY u.id
            ORDER BY u.created_at DESC
        ');
        $users = $stmt->fetchAll();

        // 获取所有角色
        $roleStmt = $pdo->query('SELECT * FROM roles ORDER BY id');
        $roles = $roleStmt->fetchAll();

        ob_start();
        $title = '用户管理';
        include __DIR__ . '/../Views/layout/header.php';
        include __DIR__ . '/../Views/layout/sidebar.php';
        echo '<main class="container-fluid py-3">';
        $pageTitle = '用户管理';
        include __DIR__ . '/../Views/layout/page_head.php';
        include __DIR__ . '/../Views/users/index.php';
        echo '</main>';
        include __DIR__ . '/../Views/layout/footer.php';
        return ob_get_clean();
    }

    public function create(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $username = trim($_POST['username'] ?? '');
            $password = trim($_POST['password'] ?? '');
            $realName = trim($_POST['real_name'] ?? '');
            $permissionRole = (int)($_POST['permission_role'] ?? 0);
            $businessRoles = $_POST['business_roles'] ?? [];

            // 验证必填字段
            if (empty($username) || empty($password) || empty($realName)) {
                throw new \Exception('用户名、密码和姓名为必填项');
            }

            if (!$permissionRole) {
                throw new \Exception('必须选择一个权限角色');
            }

            // 验证用户名唯一性
            $pdo = DB::conn();
            $checkStmt = $pdo->prepare('SELECT id FROM users WHERE username = ?');
            $checkStmt->execute([$username]);
            if ($checkStmt->fetch()) {
                throw new \Exception('用户名已存在');
            }

            $pdo->beginTransaction();

            // 创建用户
            $userStmt = $pdo->prepare('
                INSERT INTO users (username, password_hash, real_name)
                VALUES (?, ?, ?)
            ');
            $userStmt->execute([
                $username,
                password_hash($password, PASSWORD_DEFAULT),
                $realName
            ]);

            $userId = $pdo->lastInsertId();

            // 分配权限角色（必须有一个）
            $roleStmt = $pdo->prepare('INSERT INTO user_role (user_id, role_id) VALUES (?, ?)');
            $roleStmt->execute([$userId, $permissionRole]);

            // 分配业务角色（可选）
            if (!empty($businessRoles)) {
                foreach ($businessRoles as $roleId) {
                    $roleStmt->execute([$userId, (int)$roleId]);
                }
            }

            $pdo->commit();

            // 记录审计日志
            AuditService::log('users.create', 'user:' . $userId, [
                'username' => $username,
                'real_name' => $realName,
                'permission_role' => $permissionRole,
                'business_roles' => $businessRoles
            ]);

            header('Content-Type: application/json');
            return json_encode(['success' => true, 'message' => '用户创建成功']);

        } catch (\Exception $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function update(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $userId = (int)($_POST['user_id'] ?? 0);
            $username = trim($_POST['username'] ?? '');
            $realName = trim($_POST['real_name'] ?? '');
            $password = trim($_POST['password'] ?? '');
            $status = (int)($_POST['status'] ?? 1);
            $permissionRole = (int)($_POST['permission_role'] ?? 0);
            $businessRoles = $_POST['business_roles'] ?? [];

            if (!$userId || empty($username) || empty($realName)) {
                throw new \Exception('用户ID、用户名和姓名为必填项');
            }

            if (!$permissionRole) {
                throw new \Exception('必须选择一个权限角色');
            }

            $pdo = DB::conn();

            // 检查用户是否存在
            $checkStmt = $pdo->prepare('SELECT username FROM users WHERE id = ?');
            $checkStmt->execute([$userId]);
            $existingUser = $checkStmt->fetch();
            if (!$existingUser) {
                throw new \Exception('用户不存在');
            }

            // 检查用户名唯一性（排除当前用户）
            $uniqueStmt = $pdo->prepare('SELECT id FROM users WHERE username = ? AND id != ?');
            $uniqueStmt->execute([$username, $userId]);
            if ($uniqueStmt->fetch()) {
                throw new \Exception('用户名已被其他用户使用');
            }

            $pdo->beginTransaction();

            // 更新用户信息
            if (!empty($password)) {
                // 如果提供了新密码，则更新密码
                $updateStmt = $pdo->prepare('
                    UPDATE users
                    SET username = ?, password_hash = ?, real_name = ?, status = ?
                    WHERE id = ?
                ');
                $updateStmt->execute([
                    $username,
                    password_hash($password, PASSWORD_DEFAULT),
                    $realName,
                    $status,
                    $userId
                ]);
            } else {
                // 不更新密码
                $updateStmt = $pdo->prepare('
                    UPDATE users
                    SET username = ?, real_name = ?, status = ?
                    WHERE id = ?
                ');
                $updateStmt->execute([
                    $username,
                    $realName,
                    $status,
                    $userId
                ]);
            }

            // 更新角色分配
            $pdo->prepare('DELETE FROM user_role WHERE user_id = ?')->execute([$userId]);

            // 分配权限角色（必须有一个）
            $roleStmt = $pdo->prepare('INSERT INTO user_role (user_id, role_id) VALUES (?, ?)');
            $roleStmt->execute([$userId, $permissionRole]);

            // 分配业务角色（可选）
            if (!empty($businessRoles)) {
                foreach ($businessRoles as $roleId) {
                    $roleStmt->execute([$userId, (int)$roleId]);
                }
            }

            $pdo->commit();

            // 记录审计日志
            AuditService::log('users.update', 'user:' . $userId, [
                'username' => $username,
                'real_name' => $realName,
                'status' => $status,
                'permission_role' => $permissionRole,
                'business_roles' => $businessRoles,
                'password_changed' => !empty($password)
            ]);

            header('Content-Type: application/json');
            return json_encode(['success' => true, 'message' => '用户更新成功']);

        } catch (\Exception $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function delete(): string
    {
        Auth::requireLogin();
        Auth::requireRole('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return json_encode(['success' => false, 'message' => '方法不允许']);
        }

        try {
            $userId = (int)($_POST['user_id'] ?? 0);

            if (!$userId) {
                throw new \Exception('用户ID不能为空');
            }

            // 不能删除自己
            $currentUser = Auth::user();
            if ($currentUser && $currentUser['id'] == $userId) {
                throw new \Exception('不能删除当前登录用户');
            }

            $pdo = DB::conn();

            // 检查用户是否存在
            $checkStmt = $pdo->prepare('SELECT username, real_name FROM users WHERE id = ?');
            $checkStmt->execute([$userId]);
            $user = $checkStmt->fetch();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            $pdo->beginTransaction();

            // 删除用户角色关联
            $pdo->prepare('DELETE FROM user_role WHERE user_id = ?')->execute([$userId]);

            // 删除用户
            $deleteStmt = $pdo->prepare('DELETE FROM users WHERE id = ?');
            $deleteStmt->execute([$userId]);

            $pdo->commit();

            // 记录审计日志
            AuditService::log('users.delete', 'user:' . $userId, [
                'username' => $user['username'],
                'real_name' => $user['real_name']
            ]);

            header('Content-Type: application/json');
            return json_encode(['success' => true, 'message' => '用户删除成功']);

        } catch (\Exception $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            header('Content-Type: application/json');
            return json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}

