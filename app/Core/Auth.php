<?php
declare(strict_types=1);

namespace App\Core;

class Auth
{
    /**
     * 检查是否已登录
     */
    public static function check(): bool
    {
        return !empty($_SESSION['user']);
    }

    /**
     * 检查是否已登录（别名方法）
     */
    public static function isLoggedIn(): bool
    {
        return self::check();
    }

    /**
     * 获取当前用户信息
     */
    public static function user(): ?array
    {
        return $_SESSION['user'] ?? null;
    }

    /**
     * 获取当前用户角色
     */
    public static function roles(): array
    {
        return $_SESSION['roles'] ?? [];
    }

    /**
     * 检查是否有指定角色
     */
    public static function hasRole(string $role): bool
    {
        return in_array($role, self::roles());
    }

    /**
     * 检查是否有任一指定角色
     */
    public static function hasAnyRole(array $roles): bool
    {
        return !empty(array_intersect($roles, self::roles()));
    }

    /**
     * 要求登录，未登录则跳转
     */
    public static function requireLogin(): void
    {
        if (!self::check()) {
            // 根据请求类型跳转到不同的登录页面
            if (class_exists('\App\Core\RouteDispatcher') && \App\Core\RouteDispatcher::isAdmin()) {
                // 管理员登录页面
                $loginUrl = self::buildLoginUrl('auth/login');
                header('Location: ' . $loginUrl);
            } else {
                // 前端用户登录页面
                $loginUrl = self::buildLoginUrl('frontend/login');
                header('Location: ' . $loginUrl);
            }
            exit;
        }
    }

    /**
     * 构建登录URL，自动适应任何部署环境
     */
    private static function buildLoginUrl(string $route): string
    {
        // 获取当前请求的基础信息
        $scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // 动态获取基础路径
        $basePath = self::getBasePath();

        return $scheme . '://' . $host . $basePath . 'index.php?r=' . $route;
    }

    /**
     * 动态获取应用的基础路径，适应任何部署环境
     */
    private static function getBasePath(): string
    {
        // 获取当前脚本的路径
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '/index.php';

        // 提取目录路径（去掉文件名）
        $basePath = dirname($scriptName);

        // 规范化路径
        $basePath = str_replace('\\', '/', $basePath); // Windows路径兼容
        $basePath = rtrim($basePath, '/'); // 移除末尾斜杠

        // 确保以斜杠结尾
        if ($basePath === '' || $basePath === '.') {
            return '/'; // 根目录部署
        }

        return $basePath . '/';
    }

    /**
     * 要求指定角色，无权限则返回403
     */
    public static function requireRole(string $role): void
    {
        self::requireLogin();
        if (!self::hasRole($role)) {
            http_response_code(403);
            echo '权限不足';
            exit;
        }
    }

    /**
     * 用户登录
     */
    public static function login(string $username, string $password): bool
    {
        $pdo = DB::conn();
        $stmt = $pdo->prepare('SELECT id,username,password_hash,real_name FROM users WHERE username=? LIMIT 1');
        $stmt->execute([$username]);
        $row = $stmt->fetch();

        if (!$row || !password_verify($password, $row['password_hash'])) {
            return false;
        }

        $_SESSION['user'] = [
            'id' => (int)$row['id'],
            'username' => $row['username'],
            'real_name' => $row['real_name']
        ];

        // 加载角色
        $roles = $pdo->prepare('SELECT r.code FROM roles r JOIN user_role ur ON ur.role_id=r.id WHERE ur.user_id=?');
        $roles->execute([$row['id']]);
        $_SESSION['roles'] = array_map(fn($x) => $x['code'], $roles->fetchAll());

        return true;
    }

    /**
     * 用户登出
     */
    public static function logout(): void
    {
        $_SESSION = [];
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params['path'], $params['domain'], $params['secure'], $params['httponly']
            );
        }
        session_destroy();
    }
}
