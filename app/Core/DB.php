<?php
declare(strict_types=1);

namespace App\Core;

class DB
{
    private static ?\PDO $pdo = null;

    public static function conn(): \PDO
    {
        if (self::$pdo === null) {
            $cfg = require __DIR__ . '/../../config/db.php';
            $dsn = sprintf('mysql:host=%s;port=%d;dbname=%s;charset=%s', $cfg['host'], $cfg['port'], $cfg['dbname'], $cfg['charset']);
            $opt = [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ];
            self::$pdo = new \PDO($dsn, $cfg['username'], $cfg['password'], $opt);
        }
        return self::$pdo;
    }
}

