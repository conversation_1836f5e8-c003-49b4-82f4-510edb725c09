<?php
declare(strict_types=1);

namespace App\Core;

/**
 * 路由分发器
 * 负责处理前后端分离的路由逻辑
 */
class RouteDispatcher
{
    /**
     * 分发请求到正确的控制器
     */
    public static function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $reqUri = $_SERVER['REQUEST_URI'] ?? '/';
        
        // 轻量日志
        $logDir = defined('LOG_PATH') ? LOG_PATH : (__DIR__ . '/../../logs');
        if (!is_dir($logDir)) { @mkdir($logDir, 0777, true); }
        $logFile = $logDir . '/route_dispatcher.log';
        $dbg = function(string $msg) use ($logFile) {
            @file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . "] " . $msg . "\n", FILE_APPEND);
        };
        
        $dbg(sprintf('RouteDispatcher: method=%s uri=%s', $method, $reqUri));
        
        // 解析路径
        $path = parse_url($reqUri, PHP_URL_PATH) ?: '/';
        $basePath = defined('BASE_URL') ? (parse_url(BASE_URL, PHP_URL_PATH) ?: '/') : '/';
        
        // 移除基础路径前缀
        if ($basePath !== '/' && strpos($path, $basePath) === 0) {
            $path = '/' . ltrim(substr($path, strlen($basePath)), '/');
            if ($path === '//') { $path = '/'; }
        }
        
        $dbg(sprintf('Normalized path: %s', $path));
        
        // 判断是否为管理后台请求
        if (self::isAdminRequest($path)) {
            $dbg('Admin request detected');
            self::handleAdminRequest($path, $method);
        } else {
            $dbg('Frontend request detected');
            self::handleFrontendRequest($path, $method);
        }
    }
    
    /**
     * 判断是否为管理后台请求
     */
    private static function isAdminRequest(string $path): bool
    {
        // 检查路径是否以 /admin 开头
        if (strpos($path, '/admin') === 0) {
            return true;
        }
        
        // 检查 r 参数是否为管理功能
        $r = $_GET['r'] ?? '';
        $adminRoutes = [
            'admin', 'admin/', 'admin/index',  // 添加admin相关路由
            'sys/', 'auth/', 'users/', 'reports/', 'templates/', 'devices/', 'static/'
        ];

        foreach ($adminRoutes as $adminRoute) {
            if ($r === $adminRoute || strpos($r, $adminRoute) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 处理管理后台请求
     */
    private static function handleAdminRequest(string $path, string $method): void
    {
        // 设置管理后台标识
        $_SERVER['IS_ADMIN_REQUEST'] = true;

        // 保持admin路径，不要移除前缀！
        // 因为路由注册的就是 /admin/, /admin/login 等
        $originalUri = $_SERVER['REQUEST_URI'] ?? '/';
        $queryString = parse_url($originalUri, PHP_URL_QUERY);
        $newUri = $path . ($queryString ? '?' . $queryString : '');
        $_SERVER['REQUEST_URI'] = $newUri;

        // 调用原有的路由器
        Router::dispatch();

        // 恢复原始URI
        $_SERVER['REQUEST_URI'] = $originalUri;
    }
    
    /**
     * 处理前端请求
     */
    private static function handleFrontendRequest(string $path, string $method): void
    {
        // 设置前端标识
        $_SERVER['IS_FRONTEND_REQUEST'] = true;
        
        // 检查是否为API请求
        $r = $_GET['r'] ?? '';
        if (strpos($r, 'frontend/') === 0 || strpos($r, 'api/') === 0) {
            // API请求，直接调用路由器
            Router::dispatch();
            return;
        }
        
        // 检查是否为静态页面请求
        if (strpos($path, '/static_pages/') === 0) {
            self::serveStaticPage($path);
            return;
        }
        
        // 检查是否为资源文件请求
        if (self::isAssetRequest($path)) {
            self::serveAsset($path);
            return;
        }
        
        // 其他请求都路由到前端控制器
        $frontendController = new \App\Controllers\FrontendController();
        echo $frontendController->index();
    }
    
    /**
     * 判断是否为资源文件请求
     */
    private static function isAssetRequest(string $path): bool
    {
        $assetExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf'];
        
        foreach ($assetExtensions as $ext) {
            if (substr($path, -strlen($ext)) === $ext) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 服务静态页面
     */
    private static function serveStaticPage(string $path): void
    {
        $filePath = __DIR__ . '/../../' . ltrim($path, '/');
        
        if (file_exists($filePath) && is_file($filePath)) {
            $mimeType = self::getMimeType($filePath);
            header('Content-Type: ' . $mimeType);
            readfile($filePath);
        } else {
            http_response_code(404);
            echo 'Static page not found';
        }
    }
    
    /**
     * 服务资源文件
     */
    private static function serveAsset(string $path): void
    {
        $filePath = __DIR__ . '/../../' . ltrim($path, '/');
        
        if (file_exists($filePath) && is_file($filePath)) {
            $mimeType = self::getMimeType($filePath);
            header('Content-Type: ' . $mimeType);
            
            // 设置缓存头
            $lastModified = filemtime($filePath);
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $lastModified) . ' GMT');
            header('Cache-Control: public, max-age=3600');
            
            // 检查 If-Modified-Since
            $ifModifiedSince = $_SERVER['HTTP_IF_MODIFIED_SINCE'] ?? '';
            if ($ifModifiedSince && strtotime($ifModifiedSince) >= $lastModified) {
                http_response_code(304);
                return;
            }
            
            readfile($filePath);
        } else {
            http_response_code(404);
            echo 'Asset not found';
        }
    }
    
    /**
     * 获取MIME类型
     */
    private static function getMimeType(string $filePath): string
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    /**
     * 获取当前请求类型
     */
    public static function getRequestType(): string
    {
        if (isset($_SERVER['IS_ADMIN_REQUEST'])) {
            return 'admin';
        } elseif (isset($_SERVER['IS_FRONTEND_REQUEST'])) {
            return 'frontend';
        }
        return 'unknown';
    }
    
    /**
     * 检查是否为管理后台请求
     */
    public static function isAdmin(): bool
    {
        return isset($_SERVER['IS_ADMIN_REQUEST']);
    }
    
    /**
     * 检查是否为前端请求
     */
    public static function isFrontend(): bool
    {
        return isset($_SERVER['IS_FRONTEND_REQUEST']);
    }
}
