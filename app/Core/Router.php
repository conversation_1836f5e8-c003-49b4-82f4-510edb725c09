<?php
declare(strict_types=1);

namespace App\Core;

class Router
{
    private static array $routes = [];

    public static function register(string $method, string $path, $handler): void
    {
        self::$routes[strtoupper($method)][$path] = $handler;
    }

    public static function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $reqUri = $_SERVER['REQUEST_URI'] ?? '/';

        // 轻量日志，帮助定位 404 路由问题（不影响性能）
        $logDir = defined('LOG_PATH') ? LOG_PATH : (__DIR__ . '/../../logs');
        if (!is_dir($logDir)) { @mkdir($logDir, 0777, true); }
        $logFile = $logDir . '/router.log';
        $dbg = function(string $msg) use ($logFile) {
            @file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . "] " . $msg . "\n", FILE_APPEND);
        };
        $dbg(sprintf('dispatch begin: method=%s uri=%s r=%s', strtoupper($method), $reqUri, $_GET['r'] ?? ''));

        // 支持 /index.php?r=controller/action 形式
        if (isset($_GET['r'])) {
            $r = trim($_GET['r'], '/');
            // 容错：若 r 中意外包含了 ? 后续查询串（例如 r=api/entry/load?report_id=...），只取路径部分
            if (strpos($r, '?') !== false) { $r = substr($r, 0, strpos($r, '?')); }
            $dbg('r param detected: ' . $r);

            // 先检查是否有直接注册的路由（检查多种格式）
            $possiblePaths = [$r, '/' . $r];
            $handler = null;

            foreach ($possiblePaths as $path) {
                $dbg('try r-route: ' . $path);
                $handler = self::$routes[strtoupper($method)][$path] ?? null;
                if ($handler !== null) {
                    $dbg('matched r-route: ' . $path);
                    break;
                }
            }

            if ($handler !== null) {
                if (is_callable($handler)) {
                    $dbg('invoke callable handler');
                    echo $handler();
                    return;
                }
                if (is_array($handler) && count($handler) === 2) {
                    [$class, $methodName] = $handler;
                    $dbg('invoke controller: ' . (is_string($class) ? $class : get_class($class)) . '::' . $methodName);
                    $instance = is_string($class) ? new $class() : $class;
                    echo $instance->$methodName();
                    return;
                }
            }

            // 回退到传统 controller/action 解析
            [$controller, $action] = array_pad(explode('/', $r), 2, 'index');
            $controllerClass = '\\App\\Controllers\\' . ucfirst($controller) . 'Controller';
            $actionMethod = $action;
            $dbg('fallback to controller/action: ' . $controllerClass . '::' . $actionMethod);
            if (class_exists($controllerClass)) {
                $obj = new $controllerClass();
                if (method_exists($obj, $actionMethod)) {
                    $dbg('matched controller/action');
                    echo $obj->$actionMethod();
                    return;
                }
            }
            $dbg('no match for r route');
        }

        // 规范化路径（去掉查询串并去除 BASE_URL 的路径前缀 /bbgl/）
        $path = parse_url($reqUri, PHP_URL_PATH) ?: '/';
        $basePath = defined('BASE_URL') ? (parse_url(BASE_URL, PHP_URL_PATH) ?: '/') : '/';
        if ($basePath !== '/' && strpos($path, $basePath) === 0) {
            $path = '/' . ltrim(substr($path, strlen($basePath)), '/');
            if ($path === '//') { $path = '/'; }
        }
        $dbg(sprintf('normalized path: %s (base=%s)', $path, $basePath));

        $handler = self::$routes[strtoupper($method)][$path] ?? null;
        if ($handler === null) {
            $dbg('no handler for path, 404');
            http_response_code(404);
            echo 'Not Found';
            return;
        }

        if (is_callable($handler)) {
            $dbg('invoke callable path handler');
            echo $handler();
            return;
        }
        if (is_array($handler) && count($handler) === 2) {
            [$class, $method] = $handler;
            $dbg('invoke controller path handler: ' . (is_string($class) ? $class : get_class($class)) . '::' . $method);
            $instance = is_string($class) ? new $class() : $class;
            echo $instance->$method();
            return;
        }
        $dbg('invalid route handler');
        echo 'Invalid route handler';
    }
}

