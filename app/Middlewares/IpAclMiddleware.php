<?php
declare(strict_types=1);

namespace App\Middlewares;

use App\Core\DB;
use App\Services\ConfigService;

/**
 * IP访问控制中间件
 */
class IpAclMiddleware
{
    /**
     * 检查IP访问权限
     */
    public static function check(): bool
    {
        // 获取IP访问控制模式
        $mode = ConfigService::get('ip_acl_mode', 'disabled');
        
        if ($mode === 'disabled') {
            return true; // 禁用状态，允许所有访问
        }
        
        $clientIp = self::getClientIp();
        
        try {
            $pdo = DB::conn();
            
            if ($mode === 'whitelist') {
                // 白名单模式：只允许白名单中的IP
                return self::isIpInList($pdo, $clientIp, 'whitelist');
            } elseif ($mode === 'blacklist') {
                // 黑名单模式：禁止黑名单中的IP
                return !self::isIpInList($pdo, $clientIp, 'blacklist');
            }
            
        } catch (\Exception $e) {
            // 数据库错误时，记录日志但允许访问（避免系统完全不可用）
            error_log("IP ACL check failed: " . $e->getMessage());
            return true;
        }
        
        return true;
    }
    
    /**
     * 获取客户端IP地址
     */
    private static function getClientIp(): string
    {
        $ipKeys = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP', 
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * 检查IP是否在指定类型的列表中
     */
    private static function isIpInList(\PDO $pdo, string $clientIp, string $type): bool
    {
        $stmt = $pdo->prepare('SELECT cidr FROM ip_acl WHERE type = ?');
        $stmt->execute([$type]);
        
        while ($row = $stmt->fetch()) {
            if (self::ipMatchesCidr($clientIp, $row['cidr'])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查IP是否匹配CIDR规则
     */
    private static function ipMatchesCidr(string $ip, string $cidr): bool
    {
        // 如果CIDR就是单个IP地址
        if (strpos($cidr, '/') === false) {
            return $ip === $cidr;
        }
        
        [$network, $mask] = explode('/', $cidr, 2);
        $mask = (int)$mask;
        
        // 验证输入
        if (!filter_var($ip, FILTER_VALIDATE_IP) || !filter_var($network, FILTER_VALIDATE_IP)) {
            return false;
        }
        
        if ($mask < 0 || $mask > 32) {
            return false;
        }
        
        // 转换为长整型进行比较
        $ipLong = ip2long($ip);
        $networkLong = ip2long($network);
        
        if ($ipLong === false || $networkLong === false) {
            return false;
        }
        
        // 计算网络掩码
        $maskLong = -1 << (32 - $mask);
        
        // 检查IP是否在网络范围内
        return ($ipLong & $maskLong) === ($networkLong & $maskLong);
    }
    
    /**
     * 拒绝访问时的响应
     */
    public static function deny(): void
    {
        http_response_code(403);
        
        // 记录访问被拒绝的日志
        $clientIp = self::getClientIp();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        
        error_log("IP Access Denied: IP={$clientIp}, URI={$requestUri}, UserAgent={$userAgent}");
        
        // 输出错误页面
        echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问被拒绝</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-code { font-size: 72px; font-weight: bold; color: #e74c3c; margin-bottom: 20px; }
        .error-message { font-size: 18px; color: #333; margin-bottom: 30px; }
        .error-details { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">403</div>
        <div class="error-message">访问被拒绝</div>
        <div class="error-details">
            您的IP地址不在允许访问的范围内。<br>
            如有疑问，请联系系统管理员。<br><br>
            <small>IP: ' . htmlspecialchars($clientIp) . '</small>
        </div>
    </div>
</body>
</html>';
        exit;
    }
}
