<?php
declare(strict_types=1);

namespace App\Services;

use App\Core\DB;
use App\Core\Auth;

/**
 * 审计日志服务
 */
class AuditService
{
    /**
     * 记录审计日志
     */
    public static function log(string $action, string $resource, array $detail = [], ?int $userId = null): bool
    {
        $pdo = DB::conn();
        
        // 如果没有指定用户ID，尝试从当前会话获取
        if ($userId === null) {
            $user = Auth::user();
            $userId = $user['id'] ?? null;
        }
        
        // 获取客户端IP
        $ip = self::getClientIp();
        
        $stmt = $pdo->prepare('
            INSERT INTO audit_logs (user_id, action, resource, detail, ip) 
            VALUES (?, ?, ?, ?, ?)
        ');
        
        return $stmt->execute([
            $userId,
            $action,
            $resource,
            json_encode($detail, JSON_UNESCAPED_UNICODE),
            $ip
        ]);
    }
    
    /**
     * 获取客户端IP地址
     */
    private static function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return 'unknown';
    }
    
    /**
     * 查询审计日志
     */
    public static function query(array $filters = [], int $page = 1, int $size = 20): array
    {
        $pdo = DB::conn();
        
        $where = [];
        $params = [];
        
        // 构建查询条件
        if (!empty($filters['user_id'])) {
            $where[] = 'al.user_id = ?';
            $params[] = $filters['user_id'];
        }
        
        if (!empty($filters['action'])) {
            $where[] = 'al.action LIKE ?';
            $params[] = '%' . $filters['action'] . '%';
        }
        
        if (!empty($filters['resource'])) {
            $where[] = 'al.resource LIKE ?';
            $params[] = '%' . $filters['resource'] . '%';
        }
        
        if (!empty($filters['start_date'])) {
            $where[] = 'DATE(al.created_at) >= ?';
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where[] = 'DATE(al.created_at) <= ?';
            $params[] = $filters['end_date'];
        }
        
        $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';
        
        // 查询总数
        $countSql = "
            SELECT COUNT(*) as total 
            FROM audit_logs al 
            {$whereClause}
        ";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = (int)$countStmt->fetchColumn();
        
        // 查询数据
        $offset = ($page - 1) * $size;
        $dataSql = "
            SELECT 
                al.id, al.user_id, al.action, al.resource, al.detail, al.ip, al.created_at,
                u.username, u.real_name
            FROM audit_logs al 
            LEFT JOIN users u ON al.user_id = u.id
            {$whereClause}
            ORDER BY al.created_at DESC 
            LIMIT {$size} OFFSET {$offset}
        ";
        
        $dataStmt = $pdo->prepare($dataSql);
        $dataStmt->execute($params);
        $logs = $dataStmt->fetchAll();
        
        // 解析detail字段
        foreach ($logs as &$log) {
            $log['detail'] = json_decode($log['detail'], true) ?: [];
        }
        
        return [
            'total' => $total,
            'page' => $page,
            'size' => $size,
            'pages' => ceil($total / $size),
            'data' => $logs
        ];
    }
}
