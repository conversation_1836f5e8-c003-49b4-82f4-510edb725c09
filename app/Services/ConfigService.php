<?php
declare(strict_types=1);

namespace App\Services;

use App\Core\DB;

/**
 * 系统配置服务
 */
class ConfigService
{
    /**
     * 获取配置值
     */
    public static function get(string $key, $default = null)
    {
        $pdo = DB::conn();
        $stmt = $pdo->prepare('SELECT cfg_value FROM system_config WHERE cfg_key = ?');
        $stmt->execute([$key]);
        $row = $stmt->fetch();
        
        if (!$row) {
            return $default;
        }
        
        $value = $row['cfg_value'];
        
        // 尝试解析JSON
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }
        
        // 如果不是JSON，返回原始值
        return $value;
    }
    
    /**
     * 设置配置值
     */
    public static function set(string $key, $value, string $remark = ''): bool
    {
        $pdo = DB::conn();
        
        // 如果值不是字符串，转换为JSON
        if (!is_string($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        $stmt = $pdo->prepare('
            INSERT INTO system_config (cfg_key, cfg_value, remark) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE cfg_value = VALUES(cfg_value), remark = VALUES(remark)
        ');
        
        return $stmt->execute([$key, $value, $remark]);
    }
    
    /**
     * 获取所有配置
     */
    public static function getAll(): array
    {
        $pdo = DB::conn();
        $stmt = $pdo->query('SELECT cfg_key, cfg_value, remark FROM system_config ORDER BY cfg_key');
        $configs = [];
        
        while ($row = $stmt->fetch()) {
            $value = $row['cfg_value'];
            
            // 尝试解析JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $value = $decoded;
            }
            
            $configs[$row['cfg_key']] = [
                'value' => $value,
                'remark' => $row['remark']
            ];
        }
        
        return $configs;
    }
    
    /**
     * 批量设置配置
     */
    public static function setMultiple(array $configs): bool
    {
        $pdo = DB::conn();
        
        try {
            $pdo->beginTransaction();
            
            foreach ($configs as $key => $data) {
                $value = $data['value'] ?? $data;
                $remark = $data['remark'] ?? '';
                
                if (!self::set($key, $value, $remark)) {
                    throw new \Exception("Failed to set config: {$key}");
                }
            }
            
            $pdo->commit();
            return true;
        } catch (\Exception $e) {
            $pdo->rollBack();
            return false;
        }
    }
    
    /**
     * 删除配置
     */
    public static function delete(string $key): bool
    {
        $pdo = DB::conn();
        $stmt = $pdo->prepare('DELETE FROM system_config WHERE cfg_key = ?');
        return $stmt->execute([$key]);
    }
}
