<?php
declare(strict_types=1);

namespace App\Services;

use App\Core\DB;

class PumpsService
{
    // 获取某报表（平台泵类）关联的泵清单（可选按设备/泵类型过滤）
    public static function listPumpsForReport(int $reportId, ?int $deviceId = null): array
    {
        $pdo = DB::conn();
        // 从报表设备关联表获取配置的泵，优先使用 object_id
        $whereDevice = $deviceId ? ' AND rd.device_id='.(int)$deviceId.' ' : '';
        $sql = "SELECT COALESCE(rd.object_id, rd.pump_id) as pump_id, rd.device_id, d.name AS device_name, p.pump_no,
                       rd.object_id
                FROM report_devices rd
                JOIN devices d ON rd.device_id=d.id
                LEFT JOIN pumps p ON (rd.object_id=p.id OR (rd.object_id IS NULL AND rd.pump_id=p.id))
                WHERE rd.report_id=? AND (rd.object_id IS NOT NULL OR rd.pump_id IS NOT NULL) {$whereDevice}
                ORDER BY d.name, p.pump_no";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$reportId]);
        $result = $stmt->fetchAll() ?: [];

        // 如果没有配置，回退到所有泵（向后兼容）
        if (empty($result)) {
            $sql = "SELECT p.id AS pump_id, d.id AS device_id, d.name AS device_name, p.pump_no
                    FROM pumps p JOIN devices d ON p.device_id=d.id"
                    . ($deviceId ? " WHERE d.id=".(int)$deviceId : "") .
                    " ORDER BY d.name, p.pump_no";
            $result = $pdo->query($sql)->fetchAll() ?: [];
        }

        return $result;
    }

    // 获取该报表下已配置的泵类型（设备）列表
    public static function listPumpDevicesForReport(int $reportId): array
    {
        $pdo = DB::conn();
        $sql = "SELECT DISTINCT d.id, d.name
                FROM report_devices rd
                JOIN devices d ON rd.device_id = d.id
                WHERE rd.report_id=?
                ORDER BY d.name";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$reportId]);
        $rows = $stmt->fetchAll() ?: [];
        if (empty($rows)) {
            // 回退所有泵设备类型
            $rows = $pdo->query("SELECT DISTINCT d.id, d.name FROM devices d JOIN pumps p ON p.device_id=d.id ORDER BY d.name")->fetchAll() ?: [];
        }
        return $rows;
    }

    // 查询当天已录入的数据（按泵）
    public static function loadEntries(int $reportId, int $pumpId, string $date): array
    {
        $pdo = DB::conn();

        // 优先使用 object_id 字段，向后兼容 pump_id
        try {
            // 查询包含用户信息
            $stmt = $pdo->prepare("SELECT e.time_slot_start, e.time_slot_end, e.data_json,
                                         e.created_by, e.created_at, e.updated_by, e.updated_at,
                                         u1.username as created_by_name, u2.username as updated_by_name
                                  FROM report_entries e
                                  LEFT JOIN users u1 ON e.created_by = u1.id
                                  LEFT JOIN users u2 ON e.updated_by = u2.id
                                  WHERE e.report_id=? AND (e.object_id=? OR (e.object_id IS NULL AND e.pump_id=?))
                                  AND e.entry_date=?
                                  ORDER BY e.time_slot_start");
            $stmt->execute([$reportId, $pumpId, $pumpId, $date]);
            $rows = $stmt->fetchAll();
        } catch (\Exception $e) {
            // 如果查询失败，回退到简单查询
            $stmt = $pdo->prepare("SELECT time_slot_start, time_slot_end, data_json,
                                         created_by, created_at, updated_by, updated_at
                                  FROM report_entries
                                  WHERE report_id=? AND (object_id=? OR (object_id IS NULL AND pump_id=?))
                                  AND entry_date=?
                                  ORDER BY time_slot_start");
            $stmt->execute([$reportId, $pumpId, $pumpId, $date]);
            $rows = $stmt->fetchAll();
            // 为每行添加空的用户名
            foreach ($rows as &$row) {
                $row['created_by_name'] = null;
                $row['updated_by_name'] = null;
            }
        }
        $map = [];
        foreach ($rows as $r) {
            $key = substr($r['time_slot_start'],0,5) . '-' . substr($r['time_slot_end'],0,5);
            $data = json_decode($r['data_json'], true) ?: [];

            // 添加录入信息和修改信息
            $data['_meta'] = [
                'created_by' => $r['created_by'],
                'created_by_name' => $r['created_by_name'],
                'created_at' => $r['created_at'],
                'updated_by' => $r['updated_by'],
                'updated_by_name' => $r['updated_by_name'],
                'updated_at' => $r['updated_at']
            ];

            $map[$key] = $data;
        }
        return $map; // 以“HH:MM-HH:MM”为键
    }

    // 保存（部分或全部）。$rows 为键-值映射；仅覆盖提交的时段。
    public static function saveEntries(int $reportId, int $pumpId, string $date, array $rows, int $userId): array
    {
        $pdo = DB::conn();
        $errors = [];
        $saved = 0;

        // 获取报表时间限制
        $stmt = $pdo->prepare("SELECT time_limit_days FROM reports WHERE id=?");
        $stmt->execute([$reportId]);
        $timeLimitDays = (int)($stmt->fetchColumn() ?: 2);

        // 检查用户角色（管理员不受时间限制）
        $isAdmin = !empty($_SESSION['roles']) && in_array('admin', $_SESSION['roles']);

        $pdo->beginTransaction();
        try {
            foreach ($rows as $slot => $data) {
                // slot 形如 00:00-02:00
                [$s,$e] = explode('-', $slot);
                $slotStart = new \DateTime($date.' '.$s, new \DateTimeZone('Asia/Shanghai'));
                $slotEnd = new \DateTime($date.' '.$e, new \DateTimeZone('Asia/Shanghai'));
                $now = new \DateTime('now', new \DateTimeZone('Asia/Shanghai'));

                // 校验1：禁止未来时段
                if ($slotEnd > $now) {
                    $errors[] = "时段 {$slot} 为未来时间，无法保存";
                    continue;
                }

                // 校验2：时间限制（非管理员）
                if (!$isAdmin) {
                    $limitDate = (clone $now)->sub(new \DateInterval("P{$timeLimitDays}D"));
                    if ($slotStart < $limitDate) {
                        $errors[] = "时段 {$slot} 超出可录入时间限制（{$timeLimitDays}天）";
                        continue;
                    }
                }

                // 校验3：数据完整性（可选）
                $hasRequiredData = !empty($data['start_time']) || !empty($data['voltage_v']) || !empty($data['current_a']);
                if (!$hasRequiredData && array_filter($data)) {
                    // 有部分数据但缺少关键字段，给出提示但仍保存
                    $errors[] = "时段 {$slot} 数据不完整，建议填写开泵时间、电压或电流";
                }

                // upsert：先删后插，同时处理 object_id 和 pump_id 字段
                $pdo->prepare("DELETE FROM report_entries
                              WHERE report_id=? AND (object_id=? OR (object_id IS NULL AND pump_id=?))
                              AND entry_date=? AND time_slot_start=? AND time_slot_end=?")
                    ->execute([$reportId, $pumpId, $pumpId, $date, $s.':00', $e.':00']);

                // 获取泵对应的设备ID
                $stmt = $pdo->prepare("SELECT device_id FROM pumps WHERE id = ?");
                $stmt->execute([$pumpId]);
                $deviceId = $stmt->fetchColumn();

                $pdo->prepare("INSERT INTO report_entries(report_id, device_id, pump_id, object_id, entry_date, time_slot_start, time_slot_end, data_json, created_by)
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)")
                    ->execute([$reportId, $deviceId, $pumpId, $pumpId, $date, $s.':00', $e.':00', json_encode($data, JSON_UNESCAPED_UNICODE), $userId]);
                $saved++;
            }
            $pdo->commit();
            return ['success' => true, 'saved' => $saved, 'errors' => $errors];
        } catch (\Throwable $e) {
            $pdo->rollBack();
            throw $e;
        }
    }
}

