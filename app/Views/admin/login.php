<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>管理后台登录 - 设备资料录入管理系统</title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 20px;
        }
        
        .admin-login-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
        }
        
        .admin-login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .admin-login-header .content {
            position: relative;
            z-index: 1;
        }
        
        .admin-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .admin-icon i {
            font-size: 2.5rem;
            color: white;
        }
        
        .admin-login-body {
            padding: 2.5rem 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }
        
        .input-wrapper {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 2;
        }
        
        .form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #2a5298;
            background: white;
            box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.1);
        }
        
        .form-options {
            margin-bottom: 2rem;
        }
        
        .checkbox-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            padding: 0.5rem 0;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            cursor: pointer;
            accent-color: #2a5298;
        }
        
        .checkbox-item label {
            cursor: pointer;
            font-size: 0.9rem;
            color: #6c757d;
            user-select: none;
            margin-bottom: 0;
        }
        
        .checkbox-item:hover label {
            color: #2a5298;
        }
        
        @media (max-width: 480px) {
            .checkbox-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.8rem;
            }
        }
        
        .btn-admin-login {
            width: 100%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-admin-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(42, 82, 152, 0.3);
        }
        
        .btn-admin-login:active {
            transform: translateY(0);
        }
        
        .btn-admin-login .btn-text {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background: #fee;
            color: #c53030;
            border-left: 4px solid #e53e3e;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .user-portal-link {
            color: #6c757d;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .user-portal-link:hover {
            color: #2a5298;
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn-admin-login.loading .btn-text {
            opacity: 0.7;
        }
        
        .btn-admin-login.loading .loading-spinner {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="admin-login-container">
        <div class="admin-login-header">
            <div class="content">
                <div class="admin-icon">
                    <i class="fa fa-shield-alt"></i>
                </div>
                <h3 class="mb-0">管理后台</h3>
                <p class="mb-0 mt-2 opacity-75">设备资料录入管理系统</p>
            </div>
        </div>
        
        <div class="admin-login-body">
            <?php if (!empty($error ?? '')): ?>
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="post" action="<?php echo BASE_URL; ?>admin/login.php" class="admin-login-form" id="adminLoginForm">
                <div class="form-group">
                    <label class="form-label">管理员账号</label>
                    <div class="input-wrapper">
                        <i class="fa fa-user-shield input-icon"></i>
                        <input 
                            required 
                            name="username" 
                            class="form-control" 
                            autocomplete="username" 
                            placeholder="请输入管理员账号" 
                            value="<?php echo htmlspecialchars($rememberedUsername ?? ''); ?>"
                            id="adminUsername">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">管理员密码</label>
                    <div class="input-wrapper">
                        <i class="fa fa-key input-icon"></i>
                        <input 
                            required 
                            type="password" 
                            name="password" 
                            class="form-control" 
                            autocomplete="current-password" 
                            placeholder="请输入管理员密码"
                            value="<?php echo htmlspecialchars($rememberedPassword ?? ''); ?>"
                            id="adminPassword">
                    </div>
                </div>
                
                <div class="form-options">
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="remember_username" name="remember_username" <?php echo !empty($rememberedUsername)?'checked':''; ?>>
                            <label for="remember_username">记住账号</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="remember_password" name="remember_password" <?php echo !empty($rememberedPassword)?'checked':''; ?>>
                            <label for="remember_password">记住密码</label>
                        </div>
                    </div>
                </div>
                
                <button class="btn-admin-login" type="submit" id="adminLoginBtn">
                    <div class="btn-text">
                        <span>登录管理后台</span>
                        <i class="fa fa-arrow-right"></i>
                    </div>
                    <div class="loading-spinner"></div>
                </button>
            </form>
            
            <div class="login-footer">
                <a href="<?php echo BASE_URL; ?>" class="user-portal-link">
                    <i class="fa fa-home me-1"></i>
                    返回用户端
                </a>
            </div>
        </div>
    </div>

    <script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 表单提交处理
            $('#adminLoginForm').on('submit', function(e) {
                const $btn = $('#adminLoginBtn');
                const $username = $('#adminUsername');
                const $password = $('#adminPassword');
                
                // 基础验证
                if (!$username.val().trim() || !$password.val().trim()) {
                    e.preventDefault();
                    alert('请填写完整的登录信息');
                    return;
                }
                
                // 显示加载状态
                $btn.addClass('loading');
                $btn.prop('disabled', true);
            });
            
            // 回车键登录
            $('#adminUsername, #adminPassword').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#adminLoginForm').submit();
                }
            });
            
            // 记住密码逻辑：勾选记住密码时自动勾选记住账号
            $('#remember_password').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#remember_username').prop('checked', true);
                }
            });
            
            // 取消记住账号时，同时取消记住密码
            $('#remember_username').on('change', function() {
                if (!$(this).is(':checked')) {
                    $('#remember_password').prop('checked', false);
                }
            });
            
            // 自动聚焦逻辑
            const $username = $('#adminUsername');
            const $password = $('#adminPassword');
            
            if ($username.val().trim() === '') {
                // 如果账号为空，聚焦到账号输入框
                $username.focus();
            } else if ($password.val().trim() === '') {
                // 如果账号已填写但密码为空，聚焦到密码输入框
                $password.focus();
            } else {
                // 如果账号和密码都已填写，聚焦到登录按钮
                $('#adminLoginBtn').focus();
            }
            
            // 输入框焦点效果
            $('.form-control').on('focus', function() {
                $(this).closest('.input-wrapper').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.input-wrapper').removeClass('focused');
            });
            
            // 密码可见性切换提示
            let passwordVisible = false;
            $password.on('dblclick', function() {
                if (!passwordVisible) {
                    $(this).attr('type', 'text');
                    passwordVisible = true;
                    setTimeout(() => {
                        $(this).attr('type', 'password');
                        passwordVisible = false;
                    }, 2000);
                }
            });
        });
    </script>
</body>
</html>
