<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>设备资料录入管理系统</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,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">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
    <style>
        /* Apple 高级风格设计系统 */
        :root {
            --primary-color: #007aff;
            --secondary-color: #5856d6;
            --success-color: #34c759;
            --warning-color: #ff9500;
            --danger-color: #ff3b30;
            --info-color: #5ac8fa;
            --light-color: #f2f2f7;
            --dark-color: #1c1c1e;
            --text-primary: #000000;
            --text-secondary: #3c3c43;
            --text-tertiary: #8e8e93;
            --background-primary: #ffffff;
            --background-secondary: #f2f2f7;
            --background-tertiary: #ffffff;
            --border-color: #d1d1d6;
            --separator-color: #c6c6c8;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 15px 35px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --transition: all 0.2s ease;
        }

        /* Apple 风格侧边栏 */
        .sidebar {
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            background: var(--background-secondary);
            color: var(--text-primary);
            overflow-y: auto;
            transition: var(--transition);
            z-index: 1000;
            border-right: 1px solid var(--separator-color);
        }

        .sidebar.collapsed {
            margin-left: -280px;
        }

        .main-content {
            margin-left: 280px;
            transition: var(--transition);
            min-height: 100vh;
            background: var(--background-secondary);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Apple 风格导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.8) !important;
            backdrop-filter: saturate(180%) blur(20px);
            box-shadow: 0 1px 0 var(--separator-color);
            border-bottom: none;
        }

        /* Apple 风格菜单项 */
        .menu-item {
            display: block;
            padding: 12px 20px;
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            margin: 2px 16px;
            transition: var(--transition);
            font-weight: 400;
        }

        .menu-item:hover {
            background: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .menu-item.active {
            background: var(--primary-color);
            color: white;
        }

        .menu-group {
            padding: 16px 20px 8px;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .menu-submenu {
            background: transparent;
            margin: 0 16px;
        }

        .menu-submenu .menu-item {
            padding-left: 40px;
            font-size: 0.9rem;
            margin: 1px 0;
        }

        /* Apple 风格卡片 */
        .card {
            border: 1px solid var(--separator-color);
            box-shadow: none;
            border-radius: var(--border-radius-lg);
            background: var(--background-primary);
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: var(--shadow-sm);
        }

        /* Apple 风格统计卡片 */
        .stats-card {
            background: var(--background-primary);
            border: 1px solid var(--separator-color);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            transition: var(--transition);
        }

        .stats-card .stats-icon {
            width: 44px;
            height: 44px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            margin-bottom: 12px;
        }

        .stats-card .stats-number {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
        }

        .stats-card .stats-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 400;
        }

        /* Apple 风格功能卡片 */
        .function-card {
            transition: var(--transition);
            cursor: pointer;
            border-radius: var(--border-radius-lg);
            background: var(--background-primary);
            border: 1px solid var(--separator-color);
        }

        .function-card:hover {
            box-shadow: var(--shadow-sm);
            border-color: var(--primary-color);
        }

        .function-card .card-body {
            padding: 16px;
        }

        .function-card .function-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            margin-bottom: 12px;
        }

        .function-card .function-title {
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
        }

        .function-card .function-desc {
            font-size: 0.8rem;
            color: var(--text-tertiary);
            line-height: 1.3;
        }

        /* 页面类型徽章 */
        .page-type-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 加载动画现代化 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息样式现代化 */
        .error-message {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 12px 16px;
            border-radius: var(--border-radius);
            margin: 12px 0;
        }

        .success-message {
            color: #059669;
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            padding: 12px 16px;
            border-radius: var(--border-radius);
            margin: 12px 0;
        }

        /* 用户信息现代化 */
        .dropdown-item-text {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .dropdown-header {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        #userDropdown {
            border: 1px solid var(--border-color);
            background-color: white;
            border-radius: 8px;
            transition: var(--transition);
        }

        #userDropdown:hover {
            background-color: var(--light-color);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        /* 响应式设计优化 */
        @media (max-width: 1200px) {
            .stats-card .stats-number {
                font-size: 1.75rem;
            }

            .function-card .card-body {
                padding: 16px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                width: 260px;
            }

            .main-content {
                margin-left: 260px;
            }

            .welcome-banner .col-md-4 {
                text-align: center !important;
                margin-top: 1rem;
            }

            .quick-action-item {
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 280px;
                z-index: 1050;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 0;
            }

            .stats-card {
                padding: 16px;
                margin-bottom: 1rem;
            }

            .stats-card .stats-number {
                font-size: 1.5rem;
            }

            .stats-card .stats-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .welcome-banner .card-body {
                padding: 1.5rem !important;
                text-align: center;
            }

            .welcome-banner h3 {
                font-size: 1.5rem;
            }

            .navbar .container-fluid {
                padding: 0.5rem 1rem;
            }

            .function-card .card-body {
                padding: 12px;
            }

            .function-card .function-icon {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .function-card .function-title {
                font-size: 0.9rem;
            }

            .function-card .function-desc {
                font-size: 0.8rem;
            }

            /* 移动端搜索优化 */
            .row.mb-4 .col-md-6 {
                margin-bottom: 0.5rem;
            }

            .input-group, .form-select {
                font-size: 0.9rem;
            }

            /* 移动端按钮组优化 */
            .btn-group .btn {
                padding: 0.375rem 0.5rem;
                font-size: 0.8rem;
            }

            /* 移动端卡片间距 */
            .card {
                margin-bottom: 1rem;
            }

            .card-header {
                padding: 0.75rem 1rem;
            }

            .card-body {
                padding: 1rem;
            }
        }

        @media (max-width: 576px) {
            .container-fluid {
                padding: 0.5rem;
            }

            .stats-card {
                padding: 12px;
            }

            .stats-card .stats-number {
                font-size: 1.25rem;
            }

            .stats-card .stats-icon {
                width: 36px;
                height: 36px;
                font-size: 14px;
                margin-bottom: 12px;
            }

            .welcome-banner h3 {
                font-size: 1.25rem;
            }

            .welcome-banner p {
                font-size: 0.85rem;
            }

            .quick-action-item {
                padding: 0.75rem !important;
            }

            .quick-action-item h6 {
                font-size: 0.9rem;
            }

            .quick-action-item small {
                font-size: 0.75rem;
            }

            .function-card {
                margin-bottom: 0.75rem;
            }

            /* 移动端列表视图优化 */
            .list-group-item {
                padding: 0.75rem;
            }

            .list-group-item h6 {
                font-size: 0.9rem;
            }

            .list-group-item small {
                font-size: 0.75rem;
            }

            /* 移动端搜索栏堆叠 */
            .row.mb-4 .col-md-6 {
                margin-bottom: 0.75rem;
            }

            /* 移动端按钮优化 */
            .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            /* 移动端导航优化 */
            .navbar-brand {
                font-size: 1rem;
            }

            #userDropdown {
                font-size: 0.8rem;
                padding: 0.25rem 0.5rem;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 400px) {
            .welcome-banner .card-body {
                padding: 1rem !important;
            }

            .welcome-banner h3 {
                font-size: 1.1rem;
            }

            .stats-card .stats-number {
                font-size: 1.1rem;
            }

            .function-card .function-title {
                font-size: 0.85rem;
            }

            .function-card .function-desc {
                font-size: 0.75rem;
            }

            .navbar .container-fluid {
                padding: 0.25rem 0.5rem;
            }

            .card-header h5 {
                font-size: 1rem;
            }

            .card-header p {
                font-size: 0.75rem;
            }
        }

        /* 横屏平板优化 */
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
            .stats-card {
                padding: 18px;
            }

            .function-card .card-body {
                padding: 18px;
            }

            .welcome-banner .card-body {
                padding: 2rem !important;
            }
        }

        /* 高分辨率屏幕优化 */
        @media (min-width: 1400px) {
            .stats-card {
                padding: 28px;
            }

            .stats-card .stats-number {
                font-size: 2.25rem;
            }

            .function-card .card-body {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="p-3 border-bottom">
            <h5 class="mb-0">
                <i class="fa fa-cogs me-2"></i>
                设备录入系统
            </h5>
            <small class="text-muted">用户端</small>
        </div>
        
        <div class="menu" id="menuContainer">
            <div class="text-center p-3">
                <i class="fa fa-spinner fa-spin"></i>
                <div>加载菜单中...</div>
            </div>
        </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white" style="position: relative; z-index: 1000;">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary me-3" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </button>
                
                <span class="navbar-brand mb-0 h1">设备资料录入管理系统</span>
                
                <div class="ms-auto d-flex align-items-center" style="position: relative; z-index: 1000;">
                    <span class="text-muted me-3">
                        <i class="fa fa-clock me-1"></i>
                        <span id="currentTime"></span>
                    </span>

                    <!-- 用户信息显示 -->
                    <div class="dropdown me-3" style="position: relative;">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['user']['real_name'] ?? $_SESSION['user']['username'] ?? '用户'); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown" style="position: absolute; z-index: 10000; right: 0; top: 100%;"
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">
                                <small class="text-muted">用户名：</small><?php echo htmlspecialchars($_SESSION['user']['username'] ?? ''); ?>
                            </span></li>
                            <li><span class="dropdown-item-text">
                                <small class="text-muted">姓名：</small><?php echo htmlspecialchars($_SESSION['user']['real_name'] ?? ''); ?>
                            </span></li>
                            <li><span class="dropdown-item-text">
                                <small class="text-muted">角色：</small><?php
                                    $roleNames = [
                                        'admin' => '管理员',
                                        'mod' => '版主',
                                        'user' => '用户',
                                        'site' => '现场',
                                        'cb26' => 'CB26',
                                        'ctrl' => '中控室'
                                    ];
                                    $userRoles = $_SESSION['roles'] ?? [];
                                    $displayRoles = array_map(function($role) use ($roleNames) {
                                        return $roleNames[$role] ?? $role;
                                    }, $userRoles);
                                    echo implode(', ', $displayRoles);
                                ?>
                            </span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="frontendLogout(); return false;">
                                <i class="fa fa-sign-out-alt me-1"></i>退出登录
                            </a></li>
                        </ul>
                    </div>

                    <?php if (!empty($_SESSION['roles']) && in_array('admin', $_SESSION['roles'])): ?>
                    <a href="<?php echo BASE_URL; ?>index.php?r=sys/config" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fa fa-cog me-1"></i>管理后台
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
        
        <!-- 页面内容 -->
        <div class="container-fluid p-4">
            <div class="row">
                <div class="col-12">
                    <!-- 默认内容 -->
                    <div id="defaultContent">
                        <!-- Apple 风格欢迎区域 -->
                        <div class="welcome-banner mb-4">
                            <div class="card border-0" style="background: var(--background-primary); border: 1px solid var(--separator-color);">
                                <div class="card-body p-4">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h4 class="mb-2" style="color: var(--text-primary); font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;">
                                                欢迎回来，<?php echo htmlspecialchars($_SESSION['user']['real_name'] ?? $_SESSION['user']['username'] ?? '用户'); ?>
                                            </h4>
                                            <p class="mb-0" style="color: var(--text-secondary); font-size: 0.9rem;">设备资料录入管理系统</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="welcome-icon" style="color: var(--text-tertiary);">
                                                <i class="fa fa-desktop fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 统计面板 -->
                        <div class="row g-4 mb-4">
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="stats-icon" style="background: var(--primary-color);">
                                        <i class="fa fa-file-alt"></i>
                                    </div>
                                    <div class="stats-number" id="reportCount">-</div>
                                    <div class="stats-label">录入模块</div>
                                    <div class="stats-trend mt-2">
                                        <small style="color: var(--success-color);">
                                            <i class="fa fa-circle me-1" style="font-size: 0.5rem;"></i>
                                            运行正常
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="stats-icon" style="background: var(--success-color);">
                                        <i class="fa fa-database"></i>
                                    </div>
                                    <div class="stats-number" id="staticPageCount">-</div>
                                    <div class="stats-label">设备数量</div>
                                    <div class="stats-trend mt-2">
                                        <small style="color: var(--info-color);">
                                            <i class="fa fa-circle me-1" style="font-size: 0.5rem;"></i>
                                            实时同步
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="stats-icon" style="background: var(--warning-color);">
                                        <i class="fa fa-user"></i>
                                    </div>
                                    <div class="stats-number">1</div>
                                    <div class="stats-label">当前用户</div>
                                    <div class="stats-trend mt-2">
                                        <small style="color: var(--text-secondary);">
                                            <i class="fa fa-circle me-1" style="font-size: 0.5rem;"></i>
                                            <?php
                                                $userRoles = $_SESSION['roles'] ?? [];
                                                $roleNames = [
                                                    'admin' => '管理员',
                                                    'mod' => '版主',
                                                    'user' => '用户',
                                                    'site' => '现场',
                                                    'cb26' => 'CB26',
                                                    'ctrl' => '中控室'
                                                ];
                                                $displayRoles = array_map(function($role) use ($roleNames) {
                                                    return $roleNames[$role] ?? $role;
                                                }, $userRoles);
                                                echo implode(', ', $displayRoles);
                                            ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="stats-icon" style="background: var(--info-color);">
                                        <i class="fa fa-clock"></i>
                                    </div>
                                    <div class="stats-number" id="systemUptime">计算中...</div>
                                    <div class="stats-label">系统运行</div>
                                    <div class="stats-trend mt-2">
                                        <small style="color: var(--success-color);">
                                            <i class="fa fa-circle me-1" style="font-size: 0.5rem;"></i>
                                            稳定运行
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>

                    <!-- 动态加载的内容区域 -->
                    <div id="dynamicContent" class="h-100" style="display: none;">
                        <!-- 内容加载区域 -->
                        <div id="embeddedContent" class="h-100"></div>
                    </div>
                </div>
            </div>
            
            <!-- 功能模块列表 -->
            <div id="staticPagesSection" class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0 pb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1 fw-bold">
                                        <i class="fa fa-th-large me-2 text-primary"></i>
                                        功能模块
                                    </h5>
                                    <p class="text-muted mb-0 small">选择您需要的功能模块开始工作</p>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="refreshPages()">
                                        <i class="fa fa-sync-alt me-1"></i>刷新
                                    </button>
                                    <div class="btn-group" role="group">
                                        <input type="radio" class="btn-check" name="viewMode" id="gridView" autocomplete="off" checked>
                                        <label class="btn btn-outline-secondary btn-sm" for="gridView">
                                            <i class="fa fa-th"></i>
                                        </label>
                                        <input type="radio" class="btn-check" name="viewMode" id="listView" autocomplete="off">
                                        <label class="btn btn-outline-secondary btn-sm" for="listView">
                                            <i class="fa fa-list"></i>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-2">
                            <!-- 搜索和筛选 -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <span class="input-group-text" style="background: var(--background-secondary); border-color: var(--border-color);">
                                            <i class="fa fa-search" style="color: var(--text-tertiary);"></i>
                                        </span>
                                        <input type="text" class="form-control" placeholder="搜索设备..." id="searchDevices" style="border-color: var(--border-color);">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-select" id="filterDeviceGroup" style="border-color: var(--border-color);">
                                        <option value="">全部分组</option>
                                        <option value="site">现场设备</option>
                                        <option value="cb26">CB26设备</option>
                                        <option value="ctrl">中控室设备</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="showCustomizeModal()" style="border-color: var(--border-color);">
                                        <i class="fa fa-cog me-1"></i>自定义显示
                                    </button>
                                </div>
                            </div>

                            <!-- 页面容器 -->
                            <div id="staticPagesContainer">
                                <div class="text-center p-5">
                                    <div class="loading-container">
                                        <div class="loading-spinner mb-3"></div>
                                        <h6 class="text-muted">加载功能模块中...</h6>
                                        <p class="text-muted small mb-0">请稍候，正在为您准备最佳体验</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义显示模态框 -->
    <div class="modal fade" id="customizeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="border: 1px solid var(--separator-color); border-radius: var(--border-radius-lg);">
                <div class="modal-header" style="border-bottom: 1px solid var(--separator-color);">
                    <h5 class="modal-title" style="color: var(--text-primary); font-weight: 600;">自定义显示设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label" style="color: var(--text-secondary); font-weight: 500;">显示的设备分组</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showSite" checked>
                            <label class="form-check-label" for="showSite">现场设备</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showCb26" checked>
                            <label class="form-check-label" for="showCb26">CB26设备</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showCtrl" checked>
                            <label class="form-check-label" for="showCtrl">中控室设备</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" style="color: var(--text-secondary); font-weight: 500;">默认视图模式</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="defaultView" id="defaultGrid" value="grid" checked>
                            <label class="form-check-label" for="defaultGrid">网格视图</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="defaultView" id="defaultList" value="list">
                            <label class="form-check-label" for="defaultList">列表视图</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--separator-color);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="border-color: var(--border-color);">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCustomSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
    <script src="<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_URL; ?>js/frontend-menu.js"></script>
    <script>
        $(document).ready(function() {
            // 初始化动画
            initializeAnimations();

            // 初始化时间显示
            updateTime();
            setInterval(updateTime, 1000);

            // 初始化系统运行时间
            updateSystemUptime();
            setInterval(updateSystemUptime, 60000); // 每分钟更新一次

            // 添加点击外部关闭用户菜单的功能
            $(document).on('click', function(e) {
                const $dropdown = $('#userDropdown');
                const $dropdownMenu = $dropdown.next('.dropdown-menu');

                // 如果点击的不是用户按钮或菜单内容，则关闭菜单
                if (!$dropdown.is(e.target) && !$dropdown.has(e.target).length &&
                    !$dropdownMenu.is(e.target) && !$dropdownMenu.has(e.target).length) {
                    if ($dropdownMenu.hasClass('show')) {
                        $dropdown.dropdown('hide');
                    }
                }
            });

            // 初始化前端菜单系统
            frontendMenu.init().then(() => {
                // 菜单初始化完成后加载录入模块
                loadStaticPages();

                // 应用用户自定义设置
                setTimeout(() => {
                    applyCustomSettings();
                }, 500);
            }).catch(error => {
                console.error('前端菜单系统初始化失败:', error);
                showMessage('菜单系统初始化失败: ' + error.message, 'error');
            });

            // 侧边栏切换优化
            $('#sidebarToggle').on('click', function() {
                const $sidebar = $('#sidebar');
                const $mainContent = $('#mainContent');
                const $button = $(this);

                // 添加点击动画
                $button.addClass('animate__animated animate__pulse');
                setTimeout(() => {
                    $button.removeClass('animate__animated animate__pulse');
                }, 600);

                $sidebar.toggleClass('collapsed');
                $mainContent.toggleClass('expanded');

                // 在移动端显示遮罩
                if (window.innerWidth <= 768) {
                    if (!$sidebar.hasClass('collapsed')) {
                        $sidebar.addClass('show');
                        $('body').append('<div class="sidebar-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999;"></div>');
                        $('.sidebar-overlay').on('click', function() {
                            $sidebar.removeClass('show').addClass('collapsed');
                            $(this).remove();
                        });
                    } else {
                        $sidebar.removeClass('show');
                        $('.sidebar-overlay').remove();
                    }
                }
            });

            // 添加键盘快捷键支持
            $(document).on('keydown', function(e) {
                // Ctrl/Cmd + K 打开搜索
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    $('#searchPages').focus();
                }

                // ESC 关闭侧边栏（移动端）
                if (e.key === 'Escape' && window.innerWidth <= 768) {
                    $('#sidebar').removeClass('show').addClass('collapsed');
                    $('.sidebar-overlay').remove();
                }
            });

            // 添加滚动优化
            let ticking = false;
            $(window).on('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(function() {
                        const scrollTop = $(window).scrollTop();
                        const navbar = $('.navbar');

                        if (scrollTop > 50) {
                            navbar.addClass('scrolled');
                        } else {
                            navbar.removeClass('scrolled');
                        }

                        ticking = false;
                    });
                    ticking = true;
                }
            });
        });
        
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#currentTime').text(timeStr);
        }
        
        // 加载菜单数据（使用新的菜单管理器）
        function loadMenuData() {
            // 菜单数据现在由 frontend-menu.js 管理
            // 这里只需要更新统计信息
            updateStatsFromAPI();
        }
        
        // 从API更新统计信息
        function updateStatsFromAPI() {
            // 获取系统统计信息
            $.ajax({
                url: '<?php echo BASE_URL; ?>index.php?r=api/stats',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        updateStatsDisplay(response.data);
                    }
                },
                error: function() {
                    // 静默处理统计信息更新失败
                }
            });

            // 获取菜单数据用于报表统计
            $.ajax({
                url: '<?php echo BASE_URL; ?>index.php?r=frontend/getMenuData',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        updateStats(response.data);
                    }
                },
                error: function() {
                    // 静默处理菜单统计信息更新失败
                }
            });
        }

        // 更新统计信息显示
        function updateStatsDisplay(stats) {
            $('#reportCount').text(stats.reports || '-');
            $('#staticPageCount').text(stats.static_pages || '-');

            // 可以添加更多统计信息显示
            if (stats.today_entries !== undefined) {
                // 如果有今日录入统计，可以显示
            }
        }

        // 更新统计信息
        function updateStats(menuData) {
            let reportCount = 0;
            if (menuData.entry) {
                menuData.entry.forEach(function(group) {
                    reportCount += group.reports.length;
                });
            }
            $('#reportCount').text(reportCount);
        }
        
        // 加载录入模块数据
        function loadStaticPages() {

            // 同时加载菜单数据和静态页面数据
            Promise.all([
                fetch('<?php echo BASE_URL; ?>index.php?r=frontend/getMenuData').then(r => r.json()),
                fetch('<?php echo BASE_URL; ?>index.php?r=frontend/getStaticPages').then(r => r.json())
            ]).then(([menuResponse, pagesResponse]) => {
                if (menuResponse.success && pagesResponse.success) {
                    const entryModules = processEntryModules(menuResponse.data, pagesResponse.data);

                    // 存储到全局变量
                    window.allModules = entryModules;

                    // 按设备分组并渲染
                    const groupedModules = groupModulesByDevice(entryModules);
                    const isGridView = $('#gridView').is(':checked');

                    if (isGridView) {
                        renderModulesGrid(groupedModules);
                    } else {
                        renderModulesList(groupedModules);
                    }

                    // 更新统计数据
                    $('#reportCount').text(entryModules.length);
                    $('#staticPageCount').text(entryModules.length);
                } else {
                    $('#staticPagesContainer').html('<div class="text-center text-danger">录入模块加载失败</div>');
                    showMessage('录入模块加载失败', 'error');
                }
            }).catch(error => {
                console.error('加载录入模块失败:', error);
                $('#staticPagesContainer').html('<div class="text-center text-danger">录入模块加载失败</div>');
                showMessage('录入模块加载失败: ' + error.message, 'error');
            });
        }

        // 处理录入模块数据
        function processEntryModules(menuData, staticPages) {
            const modules = [];



            // 只处理录入菜单
            if (menuData.entry && menuData.entry.length > 0) {
                for (const group of menuData.entry) {
                    if (group.reports && group.reports.length > 0) {
                        for (const report of group.reports) {
                            if (report.devices && report.devices.length > 0) {
                                for (const device of report.devices) {
                                    // 处理设备数据，设备是对象格式 {id: xx, name: "xxx"}
                                    let deviceId, deviceName;
                                    if (typeof device === 'object' && device.id) {
                                        deviceId = device.id;
                                        deviceName = device.name; // 直接使用数据库返回的设备名称
                                    } else {
                                        // 兼容直接ID的情况（不应该出现，但保留兼容性）
                                        deviceId = device;
                                        deviceName = `设备${device}`;
                                    }

                                    // 查找对应的静态页面，精确匹配文件名格式
                                    const expectedFilename = `entry_report_${report.id}_device_${deviceId}.html`;
                                    const staticPage = staticPages.find(page =>
                                        page.filename === expectedFilename
                                    );

                                    if (staticPage) {
                                        modules.push({
                                            deviceId: deviceId,
                                            deviceName: deviceName,
                                            reportId: report.id,
                                            reportName: report.name,
                                            groupName: group.name,
                                            group: group.group,
                                            filename: staticPage.filename,
                                            title: staticPage.title || `${deviceName} - ${report.name}`
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return modules;
        }


        
        // 渲染录入模块
        function renderEntryModules(modules) {
            if (modules.length === 0) {
                $('#staticPagesContainer').html(`
                    <div class="text-center p-5">
                        <div class="empty-state">
                            <i class="fa fa-inbox fa-3x" style="color: var(--text-tertiary); margin-bottom: 1rem;"></i>
                            <h6 style="color: var(--text-secondary);">暂无录入模块</h6>
                            <p style="color: var(--text-tertiary); font-size: 0.875rem; margin-bottom: 0;">请联系管理员生成录入页面</p>
                        </div>
                    </div>
                `);
                return;
            }


        }

        // 渲染录入模块（兼容旧版本调用）
        function renderEntryModules(modules) {
            // 存储原始数据用于搜索和筛选
            window.allModules = modules;

            // 按设备分组
            const modulesByDevice = groupModulesByDevice(modules);

            // 根据当前视图模式渲染
            const isGridView = $('#gridView').is(':checked');
            if (isGridView) {
                renderModulesGrid(modulesByDevice);
            } else {
                renderModulesList(modulesByDevice);
            }

            // 绑定搜索和筛选事件（先解绑避免重复绑定）
            $('#searchDevices').off('input').on('input', filterModules);
            $('#filterDeviceGroup').off('change').on('change', filterModules);
            $('#gridView, #listView').off('change').on('change', function() {
                const isGridView = $('#gridView').is(':checked');
                const filteredModules = getFilteredModules();
                const groupedModules = groupModulesByDevice(filteredModules);

                if (isGridView) {
                    renderModulesGrid(groupedModules);
                } else {
                    renderModulesList(groupedModules);
                }
            });
        }

        // 按设备分组模块
        function groupModulesByDevice(modules) {
            const grouped = {};

            modules.forEach(module => {
                const deviceKey = `${module.deviceId}_${module.deviceName}`;
                if (!grouped[deviceKey]) {
                    grouped[deviceKey] = {
                        deviceId: module.deviceId,
                        deviceName: module.deviceName,
                        group: module.group,
                        modules: []
                    };
                }
                grouped[deviceKey].modules.push(module);
            });

            return Object.values(grouped);
        }

        // 网格视图渲染（水平排列所有模块）
        function renderModulesGrid(deviceGroups) {
            let html = '';

            // 将所有模块平铺到一个网格中，不按设备分组
            const allModules = [];
            deviceGroups.forEach(function(deviceGroup) {
                deviceGroup.modules.forEach(function(module) {
                    allModules.push(module);
                });
            });

            if (allModules.length === 0) {
                html = '<div class="text-center p-4"><p style="color: var(--text-tertiary);">没有找到匹配的录入模块</p></div>';
            } else {
                html += '<div class="row g-3">';
                allModules.forEach(function(module, moduleIndex) {
                    const groupColors = {
                        'site': 'var(--success-color)',
                        'cb26': 'var(--warning-color)',
                        'ctrl': 'var(--info-color)'
                    };
                    const iconColor = groupColors[module.group] || 'var(--primary-color)';

                    html += '<div class="col-md-6 col-lg-4">';
                    html += '<div class="function-card" onclick="openEntryModule(\'' + module.filename + '\', \'' + module.title + '\')">';
                    html += '<div class="card-body">';
                    html += '<div class="function-icon" style="background: ' + iconColor + ';">';
                    html += '<i class="fa fa-microchip"></i>';
                    html += '</div>';
                    html += '<div class="function-title">' + module.deviceName + '</div>';
                    html += '<div class="function-desc">' + module.groupName + '</div>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                });
                html += '</div>';
            }

            $('#staticPagesContainer').html(html);
        }

        // 列表视图渲染（按设备分组）
        function renderModulesList(deviceGroups) {
            let html = '';

            deviceGroups.forEach(function(deviceGroup) {
                // 设备分组标题
                html += '<div class="device-group mb-3">';
                html += '<div class="d-flex align-items-center mb-2 p-2" style="background: var(--background-secondary); border-radius: var(--border-radius);">';
                html += '<div class="device-group-icon me-3" style="width: 28px; height: 28px; background: var(--primary-color); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">';
                html += '<i class="fa fa-microchip" style="color: white; font-size: 12px;"></i>';
                html += '</div>';
                html += '<div>';
                html += '<h6 class="mb-0" style="color: var(--text-primary); font-weight: 600; font-size: 0.9rem;">' + deviceGroup.deviceName + '</h6>';
                html += '<small style="color: var(--text-tertiary); font-size: 0.75rem;">设备ID: ' + deviceGroup.deviceId + '</small>';
                html += '</div>';
                html += '</div>';

                // 该设备下的模块列表
                html += '<div class="list-group list-group-flush">';
                deviceGroup.modules.forEach(function(module) {
                    const groupColors = {
                        'site': 'var(--success-color)',
                        'cb26': 'var(--warning-color)',
                        'ctrl': 'var(--info-color)'
                    };
                    const iconColor = groupColors[module.group] || 'var(--primary-color)';

                    html += '<div class="list-group-item border-0 rounded-3 mb-1" onclick="openEntryModule(\'' + module.filename + '\', \'' + module.title + '\')" style="cursor: pointer; transition: var(--transition); border: 1px solid var(--separator-color) !important;">';
                    html += '<div class="d-flex justify-content-between align-items-center">';
                    html += '<div class="d-flex align-items-center">';
                    html += '<div class="function-icon me-3" style="background: ' + iconColor + '; width: 32px; height: 32px;">';
                    html += '<i class="fa fa-edit"></i>';
                    html += '</div>';
                    html += '<div>';
                    html += '<h6 class="mb-1" style="font-size: 0.9rem;">' + module.reportName + '</h6>';
                    html += '<small style="color: var(--text-tertiary); font-size: 0.75rem;">' + module.groupName + '</small>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                });
                html += '</div>';
                html += '</div>';
            });

            if (html === '') {
                html = '<div class="text-center p-4"><p style="color: var(--text-tertiary);">没有找到匹配的录入模块</p></div>';
            }

            $('#staticPagesContainer').html(html);
        }

        // 筛选模块
        function filterModules() {
            const filteredModules = getFilteredModules();
            const groupedModules = groupModulesByDevice(filteredModules);
            const isGridView = $('#gridView').is(':checked');

            if (isGridView) {
                renderModulesGrid(groupedModules);
            } else {
                renderModulesList(groupedModules);
            }
        }

        // 获取筛选后的模块
        function getFilteredModules() {
            if (!window.allModules) return [];

            const searchTerm = $('#searchDevices').val().toLowerCase();
            const groupFilter = $('#filterDeviceGroup').val();
            const settings = getCustomSettings();

            return window.allModules.filter(module => {
                const matchesSearch = !searchTerm ||
                    module.deviceName.toLowerCase().includes(searchTerm) ||
                    module.reportName.toLowerCase().includes(searchTerm);
                const matchesGroup = !groupFilter || module.group === groupFilter;
                const matchesCustomSettings = settings.showGroups.includes(module.group);

                return matchesSearch && matchesGroup && matchesCustomSettings;
            });
        }

        // 打开录入模块（性能优化版）
        function openEntryModule(filename, title) {
            const url = '<?php echo BASE_URL; ?>index.php?r=static/show&file=' + encodeURIComponent(filename);
            window.open(url, '_blank');
        }

        // 刷新页面列表
        function refreshPages() {
            const $button = $('button[onclick="refreshPages()"]');
            const originalHtml = $button.html();

            // 添加加载动画
            $button.html('<i class="fa fa-spinner fa-spin me-1"></i>刷新中...');
            $button.prop('disabled', true);



            // 模拟加载延迟以显示动画效果
            setTimeout(() => {
                loadStaticPages();

                // 恢复按钮状态
                setTimeout(() => {
                    $button.html(originalHtml);
                    $button.prop('disabled', false);
                }, 500);
            }, 300);
        }

        // 显示自定义设置模态框
        function showCustomizeModal() {
            // 加载当前设置
            loadCustomSettings();
            $('#customizeModal').modal('show');
        }

        // 加载自定义设置
        function loadCustomSettings() {
            const settings = getCustomSettings();

            $('#showSite').prop('checked', settings.showGroups.includes('site'));
            $('#showCb26').prop('checked', settings.showGroups.includes('cb26'));
            $('#showCtrl').prop('checked', settings.showGroups.includes('ctrl'));

            $('input[name="defaultView"][value="' + settings.defaultView + '"]').prop('checked', true);
        }

        // 保存自定义设置
        function saveCustomSettings() {
            const settings = {
                showGroups: [],
                defaultView: $('input[name="defaultView"]:checked').val()
            };

            if ($('#showSite').prop('checked')) settings.showGroups.push('site');
            if ($('#showCb26').prop('checked')) settings.showGroups.push('cb26');
            if ($('#showCtrl').prop('checked')) settings.showGroups.push('ctrl');

            localStorage.setItem('entryModuleSettings', JSON.stringify(settings));

            // 应用设置
            applyCustomSettings();

            $('#customizeModal').modal('hide');
            showMessage('设置已保存', 'success', 2000);
        }

        // 获取自定义设置
        function getCustomSettings() {
            const defaultSettings = {
                showGroups: ['site', 'cb26', 'ctrl'],
                defaultView: 'grid'
            };

            try {
                const saved = localStorage.getItem('entryModuleSettings');
                return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
            } catch (e) {
                return defaultSettings;
            }
        }

        // 应用自定义设置
        function applyCustomSettings() {
            const settings = getCustomSettings();

            // 应用视图模式
            if (settings.defaultView === 'list') {
                $('#listView').prop('checked', true);
            } else {
                $('#gridView').prop('checked', true);
            }

            // 重新筛选和渲染
            filterModules();
        }

        // 初始化基础样式（性能优化版）
        function initializeAnimations() {
            // 只添加必要的样式，移除复杂动画
            const style = document.createElement('style');
            style.textContent = `
                .navbar.scrolled {
                    box-shadow: var(--shadow-sm) !important;
                }

                .loading-container {
                    opacity: 0.8;
                }

                .form-control:focus, .form-select:focus {
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 0.2rem rgba(0, 122, 255, 0.25);
                }

                .dropdown {
                    position: relative;
                    z-index: 9999;
                }

                .dropdown-menu {
                    border: 1px solid var(--separator-color);
                    box-shadow: var(--shadow-md);
                    z-index: 9999 !important;
                    position: absolute !important;
                }

                /* 修复用户按钮样式 */
                .btn-outline-secondary {
                    background-color: white !important;
                    border-color: var(--border-color) !important;
                    color: var(--text-primary) !important;
                }

                .btn-outline-secondary:hover {
                    background-color: var(--primary-color) !important;
                    border-color: var(--primary-color) !important;
                    color: white !important;
                }

                .btn-outline-secondary:focus {
                    background-color: white !important;
                    border-color: var(--primary-color) !important;
                    color: var(--text-primary) !important;
                    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
                }

                .alert {
                    border-radius: var(--border-radius);
                }

                /* 移除所有transform动画以提升性能 */
                .stats-card:hover .stats-icon,
                .function-card:hover .function-icon,
                .quick-action-item:hover,
                .btn:hover,
                .btn:active {
                    transform: none;
                }
            `;
            document.head.appendChild(style);
        }

        // 简化的页面打开功能（性能优化）
        function openStaticPage(filename) {
            const url = '<?php echo BASE_URL; ?>index.php?r=static/show&file=' + encodeURIComponent(filename);
            window.open(url, '_blank');
        }

        // 增强的消息显示功能
        function showMessage(message, type = 'info', duration = 5000) {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const iconClass = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            }[type] || 'fa-info-circle';

            // 查找或创建消息容器
            let container = document.getElementById('messageContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'messageContainer';
                container.style.cssText = 'position:fixed;top:80px;right:20px;z-index:9999;max-width:400px;';
                document.body.appendChild(container);
            }

            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = `alert ${alertClass} alert-dismissible fade show border-0`;
            messageEl.style.cssText = 'box-shadow: var(--shadow-lg); border-radius: var(--border-radius);';
            messageEl.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fa ${iconClass} me-2"></i>
                    <div style="white-space: pre-line;">${escapeHtml(message)}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            container.appendChild(messageEl);

            // 自动隐藏（除了错误消息）
            if (type !== 'error') {
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.classList.remove('show');
                        setTimeout(() => {
                            messageEl.remove();
                        }, 300);
                    }
                }, duration);
            }
        }
        


        // 全局变量
        let currentContentUrl = null;

        // 在右侧区域加载内容
        function loadContentInArea(url, title) {
            // loadContentInArea called with: url, title
            currentContentUrl = url;

            // 更新菜单项活动状态
            updateMenuActiveState(url);

            // 显示动态内容区域，隐藏默认内容和静态页面列表
            const defaultContent = document.getElementById('defaultContent');
            const dynamicContent = document.getElementById('dynamicContent');
            const staticPagesSection = document.getElementById('staticPagesSection');

            if (defaultContent) {
                defaultContent.style.display = 'none';
            }

            if (staticPagesSection) {
                staticPagesSection.style.display = 'none';
            }

            if (dynamicContent) {
                dynamicContent.style.display = 'block';
            }

            // 直接创建并加载iframe
            const embeddedContent = document.getElementById('embeddedContent');
            const iframe = document.createElement('iframe');
            iframe.src = url;
            iframe.style.width = '100%';
            iframe.style.height = 'calc(100vh - 120px)'; // 减去头部和边距的高度
            iframe.style.minHeight = '600px'; // 设置最小高度
            iframe.style.border = 'none';

            // 清空容器并添加iframe
            embeddedContent.innerHTML = '';
            embeddedContent.appendChild(iframe);


        }

        // 更新菜单活动状态
        function updateMenuActiveState(url) {
            // 移除所有活动状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 为当前URL添加活动状态
            document.querySelectorAll('.menu-item').forEach(item => {
                if (item.getAttribute('onclick') && item.getAttribute('onclick').includes(url)) {
                    item.classList.add('active');
                }
            });
        }

        // 显示默认内容
        function showDefaultContent() {
            document.getElementById('dynamicContent').style.display = 'none';
            document.getElementById('defaultContent').style.display = 'block';

            // 也要显示静态页面列表
            const staticPagesSection = document.getElementById('staticPagesSection');
            if (staticPagesSection) {
                staticPagesSection.style.display = 'block';
            }

            currentContentUrl = null;

            // 重置菜单活动状态，高亮首页
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === frontendMenu.baseUrl) {
                    item.classList.add('active');
                }
            });
        }

        // 监听iframe消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type) {
                switch (event.data.type) {
                    case 'close_static_page':
                        showDefaultContent();
                        break;
                    case 'load_content':
                        loadContentInArea(event.data.url, event.data.title);
                        break;
                }
            }
        });

        // 计算并更新系统运行时间
        function updateSystemUptime() {
            // 获取服务器启动时间
            fetch('<?php echo BASE_URL; ?>index.php?r=frontend/getSystemUptime')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        $('#systemUptime').text(data.uptime);
                    } else {
                        // 如果获取失败，使用本地计算
                        if (!window.pageLoadTime) {
                            window.pageLoadTime = new Date();
                        }

                        const now = new Date();
                        const uptimeMs = now - window.pageLoadTime;

                        const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));
                        const hours = Math.floor((uptimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));

                        let uptimeText = '';
                        if (days > 0) {
                            uptimeText = `${days}天${hours}小时`;
                        } else if (hours > 0) {
                            uptimeText = `${hours}小时${minutes}分钟`;
                        } else if (minutes > 0) {
                            uptimeText = `${minutes}分钟`;
                        } else {
                            uptimeText = '刚刚启动';
                        }

                        $('#systemUptime').text(uptimeText);
                    }
                })
                .catch(error => {
                    // 网络错误时使用本地时间
                    $('#systemUptime').text('无法获取');
                });
        }

        // 前端退出登录
        function frontendLogout() {
            if (confirm('确定要退出登录吗？')) {
                showMessage('正在退出登录...', 'info');
                $.ajax({
                    url: '<?php echo BASE_URL; ?>index.php?r=frontend/logout',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showMessage('退出成功，正在跳转...', 'success');
                            setTimeout(() => {
                                window.location.href = '<?php echo BASE_URL; ?>';
                            }, 1000);
                        } else {
                            showMessage('退出失败：' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('退出失败，正在跳转...', 'warning');
                        setTimeout(() => {
                            window.location.href = '<?php echo BASE_URL; ?>';
                        }, 1000);
                    }
                });
            }
        }



        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示加载状态
        function showLoading(element, text = '加载中...') {
            const $element = $(element);
            $element.html(`<span class="loading-spinner"></span> ${text}`);
        }

        // 隐藏加载状态
        function hideLoading(element, originalText) {
            const $element = $(element);
            $element.html(originalText);
        }
    </script>
</body>
</html>
