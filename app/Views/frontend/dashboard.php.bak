<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>设备资料录入管理系统</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,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">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
    <style>
        .sidebar {
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            background: #2c3e50;
            color: white;
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .sidebar.collapsed {
            margin-left: -280px;
        }
        
        .main-content {
            margin-left: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }
        
        .main-content.expanded {
            margin-left: 0;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .menu-item {
            display: block;
            padding: 12px 20px;
            color: #bdc3c7;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .menu-item:hover {
            background: #34495e;
            color: white;
            border-left-color: #3498db;
        }
        
        .menu-item.active {
            background: #34495e;
            color: white;
            border-left-color: #e74c3c;
        }
        
        .menu-group {
            padding: 15px 20px 8px;
            font-size: 0.85rem;
            font-weight: 600;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .menu-submenu {
            background: #34495e;
        }
        
        .menu-submenu .menu-item {
            padding-left: 40px;
            font-size: 0.9rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .static-page-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .static-page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .success-message {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .page-type-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        /* 用户信息样式 */
        .dropdown-item-text {
            padding: 0.25rem 1rem;
            font-size: 0.875rem;
        }

        .dropdown-header {
            font-size: 0.875rem;
            font-weight: 600;
        }

        #userDropdown {
            border: 1px solid #dee2e6;
            background-color: white;
        }

        #userDropdown:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="p-3 border-bottom">
            <h5 class="mb-0">
                <i class="fa fa-cogs me-2"></i>
                设备录入系统
            </h5>
            <small class="text-muted">用户端</small>
        </div>
        
        <div class="menu" id="menuContainer">
            <div class="text-center p-3">
                <i class="fa fa-spinner fa-spin"></i>
                <div>加载菜单中...</div>
            </div>
        </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary me-3" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </button>
                
                <span class="navbar-brand mb-0 h1">设备资料录入管理系统</span>
                
                <div class="ms-auto d-flex align-items-center">
                    <span class="text-muted me-3">
                        <i class="fa fa-clock me-1"></i>
                        <span id="currentTime"></span>
                    </span>

                    <!-- 用户信息显示 -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['user']['real_name'] ?? $_SESSION['user']['username'] ?? '用户'); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">
                                <small class="text-muted">用户名：</small><?php echo htmlspecialchars($_SESSION['user']['username'] ?? ''); ?>
                            </span></li>
                            <li><span class="dropdown-item-text">
                                <small class="text-muted">姓名：</small><?php echo htmlspecialchars($_SESSION['user']['real_name'] ?? ''); ?>
                            </span></li>
                            <li><span class="dropdown-item-text">
                                <small class="text-muted">角色：</small><?php
                                    $roleNames = [
                                        'admin' => '管理员',
                                        'mod' => '版主',
                                        'user' => '用户',
                                        'site' => '现场',
                                        'cb26' => 'CB26',
                                        'ctrl' => '中控室'
                                    ];
                                    $userRoles = $_SESSION['roles'] ?? [];
                                    $displayRoles = array_map(function($role) use ($roleNames) {
                                        return $roleNames[$role] ?? $role;
                                    }, $userRoles);
                                    echo implode(', ', $displayRoles);
                                ?>
                            </span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="frontendLogout(); return false;">
                                <i class="fa fa-sign-out-alt me-1"></i>退出登录
                            </a></li>
                        </ul>
                    </div>

                    <?php if (!empty($_SESSION['roles']) && in_array('admin', $_SESSION['roles'])): ?>
                    <a href="<?php echo BASE_URL; ?>index.php?r=sys/config" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fa fa-cog me-1"></i>管理后台
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
        
        <!-- 页面内容 -->
        <div class="container-fluid p-4">
            <div class="row">
                <div class="col-12">
                    <!-- 默认内容 -->
                    <div id="defaultContent">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fa fa-tachometer-alt me-2"></i>
                                    系统概览
                                </h5>
                                <p class="text-muted">欢迎使用设备资料录入管理系统，请从左侧菜单选择功能模块。</p>

                                <div class="row g-3 mt-3">
                                    <div class="col-md-4">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h6>可用报表</h6>
                                                        <h4 id="reportCount">-</h4>
                                                    </div>
                                                    <i class="fa fa-file-alt fa-2x opacity-75"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="card bg-success text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h6>静态页面</h6>
                                                        <h4 id="staticPageCount">-</h4>
                                                    </div>
                                                    <i class="fa fa-file-code fa-2x opacity-75"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="card bg-info text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h6>当前用户</h6>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($_SESSION['real_name'] ?? $_SESSION['username'] ?? '用户'); ?></h6>
                                                    </div>
                                                    <i class="fa fa-user fa-2x opacity-75"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 动态加载的内容区域 -->
                    <div id="dynamicContent" class="h-100" style="display: none;">
                        <!-- 内容加载区域 -->
                        <div id="embeddedContent" class="h-100"></div>
                    </div>
                </div>
            </div>
            
            <!-- 静态页面列表 -->
            <div id="staticPagesSection" class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fa fa-list me-2"></i>
                                可用页面
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="staticPagesContainer">
                                <div class="text-center p-4">
                                    <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
                                    <p>加载页面列表中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
    <script src="<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_URL; ?>js/frontend-menu.js"></script>
    <script>
        $(document).ready(function() {
            // 初始化
            updateTime();
            setInterval(updateTime, 1000);

            // 初始化前端菜单系统
            frontendMenu.init().then(() => {
                console.log('前端菜单系统初始化完成');
                // 菜单初始化完成后加载静态页面
                loadStaticPages();
                // 不再显示系统初始化完成消息
            }).catch(error => {
                console.error('前端菜单系统初始化失败:', error);
                showMessage('菜单系统初始化失败: ' + error.message, 'error');
            });

            // 侧边栏切换
            $('#sidebarToggle').on('click', function() {
                $('#sidebar').toggleClass('collapsed');
                $('#mainContent').toggleClass('expanded');
            });
        });
        
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#currentTime').text(timeStr);
        }
        
        // 加载菜单数据（使用新的菜单管理器）
        function loadMenuData() {
            // 菜单数据现在由 frontend-menu.js 管理
            // 这里只需要更新统计信息
            updateStatsFromAPI();
        }
        
        // 从API更新统计信息
        function updateStatsFromAPI() {
            // 获取系统统计信息
            $.ajax({
                url: '<?php echo BASE_URL; ?>index.php?r=api/stats',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        updateStatsDisplay(response.data);
                    }
                },
                error: function() {
                    console.warn('统计信息更新失败');
                }
            });

            // 获取菜单数据用于报表统计
            $.ajax({
                url: '<?php echo BASE_URL; ?>index.php?r=frontend/getMenuData',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        updateStats(response.data);
                    }
                },
                error: function() {
                    console.warn('菜单统计信息更新失败');
                }
            });
        }

        // 更新统计信息显示
        function updateStatsDisplay(stats) {
            $('#reportCount').text(stats.reports || '-');
            $('#staticPageCount').text(stats.static_pages || '-');

            // 可以添加更多统计信息显示
            if (stats.today_entries !== undefined) {
                // 如果有今日录入统计，可以显示
                console.log('今日录入数据:', stats.today_entries);
            }
        }

        // 更新统计信息
        function updateStats(menuData) {
            let reportCount = 0;
            if (menuData.entry) {
                menuData.entry.forEach(function(group) {
                    reportCount += group.reports.length;
                });
            }
            $('#reportCount').text(reportCount);
        }
        
        // 加载静态页面
        function loadStaticPages() {
            $.ajax({
                url: '<?php echo BASE_URL; ?>index.php?r=frontend/getStaticPages',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        renderStaticPages(response.data);
                        $('#staticPageCount').text(response.data.length);
                    } else {
                        $('#staticPagesContainer').html('<div class="text-center text-danger">静态页面加载失败</div>');
                    }
                },
                error: function() {
                    $('#staticPagesContainer').html('<div class="text-center text-danger">静态页面加载失败</div>');
                }
            });
        }
        
        // 渲染静态页面
        function renderStaticPages(pages) {
            if (pages.length === 0) {
                $('#staticPagesContainer').html('<div class="text-center text-muted">暂无静态页面</div>');
                return;
            }
            
            let html = '<div class="row g-3">';
            pages.forEach(function(page) {
                const badgeClass = page.type === 'entry' ? 'bg-primary' : 'bg-success';
                const badgeText = page.type === 'entry' ? '录入' : '查询';
                const iconClass = page.type === 'entry' ? 'fa-edit' : 'fa-search';
                
                html += '<div class="col-md-6 col-lg-4">';
                html += '<div class="card static-page-card position-relative" onclick="openStaticPage(\'' + page.filename + '\')">';
                html += '<span class="badge ' + badgeClass + ' page-type-badge">' + badgeText + '</span>';
                html += '<div class="card-body">';
                html += '<h6 class="card-title"><i class="fa ' + iconClass + ' me-2"></i>' + page.title + '</h6>';
                html += '<p class="card-text small text-muted">' + page.filename + '</p>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
            });
            html += '</div>';
            
            $('#staticPagesContainer').html(html);
        }
        
        // 打开静态页面
        function openStaticPage(filename) {
            const url = '<?php echo BASE_URL; ?>index.php?r=static/show&file=' + encodeURIComponent(filename);
            window.open(url, '_blank');
        }

        // 全局变量
        let currentContentUrl = null;

        // 在右侧区域加载内容
        function loadContentInArea(url, title) {
            console.log('loadContentInArea called with:', url, title);
            currentContentUrl = url;

            // 更新菜单项活动状态
            updateMenuActiveState(url);

            // 显示动态内容区域，隐藏默认内容和静态页面列表
            const defaultContent = document.getElementById('defaultContent');
            const dynamicContent = document.getElementById('dynamicContent');
            const staticPagesSection = document.getElementById('staticPagesSection');

            console.log('Before hiding - defaultContent:', defaultContent, 'dynamicContent:', dynamicContent, 'staticPagesSection:', staticPagesSection);

            if (defaultContent) {
                defaultContent.style.display = 'none';
                console.log('Hidden defaultContent');
            } else {
                console.error('defaultContent element not found!');
            }

            if (staticPagesSection) {
                staticPagesSection.style.display = 'none';
                console.log('Hidden staticPagesSection');
            } else {
                console.error('staticPagesSection element not found!');
            }

            if (dynamicContent) {
                dynamicContent.style.display = 'block';
                console.log('Shown dynamicContent');
            } else {
                console.error('dynamicContent element not found!');
            }

            // 直接创建并加载iframe
            const embeddedContent = document.getElementById('embeddedContent');
            const iframe = document.createElement('iframe');
            iframe.src = url;
            iframe.style.width = '100%';
            iframe.style.height = 'calc(100vh - 120px)'; // 减去头部和边距的高度
            iframe.style.minHeight = '600px'; // 设置最小高度
            iframe.style.border = 'none';

            // 清空容器并添加iframe
            embeddedContent.innerHTML = '';
            embeddedContent.appendChild(iframe);

            console.log('iframe created with height:', iframe.style.height);
        }

        // 更新菜单活动状态
        function updateMenuActiveState(url) {
            // 移除所有活动状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 为当前URL添加活动状态
            document.querySelectorAll('.menu-item').forEach(item => {
                if (item.getAttribute('onclick') && item.getAttribute('onclick').includes(url)) {
                    item.classList.add('active');
                }
            });
        }

        // 显示默认内容
        function showDefaultContent() {
            document.getElementById('dynamicContent').style.display = 'none';
            document.getElementById('defaultContent').style.display = 'block';

            // 也要显示静态页面列表
            const staticPagesSection = document.getElementById('staticPagesSection');
            if (staticPagesSection) {
                staticPagesSection.style.display = 'block';
            }

            currentContentUrl = null;

            // 重置菜单活动状态，高亮首页
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === frontendMenu.baseUrl) {
                    item.classList.add('active');
                }
            });
        }

        // 监听iframe消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type) {
                switch (event.data.type) {
                    case 'close_static_page':
                        showDefaultContent();
                        break;
                    case 'load_content':
                        loadContentInArea(event.data.url, event.data.title);
                        break;
                }
            }
        });

        // 前端退出登录
        function frontendLogout() {
            if (confirm('确定要退出登录吗？')) {
                showMessage('正在退出登录...', 'info');
                $.ajax({
                    url: '<?php echo BASE_URL; ?>index.php?r=frontend/logout',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showMessage('退出成功，正在跳转...', 'success');
                            setTimeout(() => {
                                window.location.href = '<?php echo BASE_URL; ?>';
                            }, 1000);
                        } else {
                            showMessage('退出失败：' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('退出失败，正在跳转...', 'warning');
                        setTimeout(() => {
                            window.location.href = '<?php echo BASE_URL; ?>';
                        }, 1000);
                    }
                });
            }
        }

        // 显示消息 - 使用后台管理相同的样式
        function showMessage(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            // 查找或创建消息容器
            let container = document.getElementById('messageContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'messageContainer';
                container.style.cssText = 'position:fixed;top:80px;right:20px;z-index:9999;max-width:400px;';
                document.body.appendChild(container);
            }

            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = `alert ${alertClass} alert-dismissible fade show`;
            messageEl.innerHTML = `
                <div style="white-space: pre-line;">${escapeHtml(message)}</div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            container.appendChild(messageEl);

            // 自动隐藏（除了错误消息）
            if (type !== 'error') {
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.remove();
                    }
                }, 5000);
            }
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示加载状态
        function showLoading(element, text = '加载中...') {
            const $element = $(element);
            $element.html(`<span class="loading-spinner"></span> ${text}`);
        }

        // 隐藏加载状态
        function hideLoading(element, originalText) {
            const $element = $(element);
            $element.html(originalText);
        }
    </script>
</body>
</html>
