<?php
// 前端用户界面底部
?>
<footer class="bg-light text-center py-3 mt-5">
  <div class="container">
    <p class="text-muted mb-0">&copy; <?php echo date('Y'); ?> 设备资料录入管理系统</p>
  </div>
</footer>

<script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
<script src="<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js"></script>
<script>
// 简单的前端交互
document.addEventListener('DOMContentLoaded', function() {
    // 卡片悬停效果
    const cards = document.querySelectorAll('.device-type-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });
    
    // 页面加载动画
    const elements = document.querySelectorAll('.card, .alert');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            el.style.transition = 'all 0.5s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
</body>
</html>
