<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>用户登录 - 设备资料录入管理系统</title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            margin: 0;
            padding: 0 0 60px 0; /* 为底部单位信息留出空间 */
        }

        /* 动态背景效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: backgroundMove 20s ease-in-out infinite;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translate(0, 0); }
            50% { transform: translate(-20px, -20px); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 1200px;
            width: 90%;
            height: 700px;
            margin: 20px;
            position: relative;
            z-index: 1;
            animation: slideUp 0.6s ease-out;
            display: flex;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 左侧品牌展示区域 */
        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 15s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .login-left .content {
            position: relative;
            z-index: 1;
        }

        .brand-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .brand-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .brand-icon {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 2rem;
            border: 4px solid rgba(255, 255, 255, 0.3);
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .brand-icon i {
            font-size: 4rem;
            color: white;
        }

        .brand-text h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .brand-text p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
        }

        .features-section {
            margin-top: 3rem;
        }

        .system-info {
            display: grid;
            gap: 1.5rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .info-item i {
            font-size: 1.2rem;
            margin-right: 1rem;
            width: 30px;
            text-align: center;
            opacity: 0.9;
        }

        .info-item span {
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.95;
        }

        /* 单位信息样式 */
        .unit-info {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
            z-index: 1000;
        }

        .unit-name {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
            letter-spacing: 1px;
        }

        /* 右侧登录表单区域 */
        .login-right {
            flex: 0 0 480px;
            padding: 4rem 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: white;
        }

        .login-form-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .login-form-header h2 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .login-form-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .input-wrapper {
            position: relative;
            transition: all 0.3s ease;
        }

        .input-wrapper.focused {
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 2;
            transition: color 0.3s ease;
        }

        .input-wrapper.focused .input-icon {
            color: #667eea;
        }

        .form-control {
            width: 100%;
            padding: 1.25rem 1.25rem 1.25rem 3.5rem;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
            position: relative;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow:
                0 0 0 4px rgba(102, 126, 234, 0.1),
                0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        .form-control::placeholder {
            color: #adb5bd;
            transition: opacity 0.3s ease;
        }

        .form-control:focus::placeholder {
            opacity: 0.7;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .checkbox-wrapper {
            display: flex !important;
            align-items: center;
            cursor: pointer;
            font-size: 0.9rem;
            color: #495057;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            gap: 0.5rem;
        }

        .checkbox-wrapper:hover {
            color: #343a40;
            background: rgba(102, 126, 234, 0.1);
        }

        .checkbox-wrapper input[type="checkbox"] {
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            margin-right: 8px !important;
            transform: scale(1.2) !important;
            accent-color: #6366f1 !important;
            cursor: pointer !important;
            flex-shrink: 0 !important;
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: relative !important;
            z-index: 999 !important;
            background: white !important;
            border: 2px solid #6366f1 !important;
        }

        /* 确保checkbox在所有情况下都可见 */
        input[type="checkbox"]#rememberMe {
            width: 18px !important;
            height: 18px !important;
            display: inline-block !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: static !important;
        }

        .checkbox-wrapper span {
            user-select: none;
            font-weight: 500;
        }

        .btn-login {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.25rem 2rem;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .btn-login:active {
            transform: translateY(-1px);
        }

        .btn-login.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            position: relative;
            z-index: 1;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-login.loading .loading-spinner {
            display: inline-block;
        }

        .btn-login.loading .btn-text {
            opacity: 0.7;
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee 0%, #fdd 100%);
            color: #c53030;
            border-left: 4px solid #e53e3e;
        }

        .alert-success {
            background: linear-gradient(135deg, #efe 0%, #dfd 100%);
            color: #2d7d32;
            border-left: 4px solid #4caf50;
        }

        .login-footer {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .admin-link {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            border: 1px solid rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.1);
        }

        .admin-link:hover {
            color: #5a67d8;
            transform: translateY(-1px);
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.5);
        }

        .security-info {
            margin-top: 1rem;
            font-size: 0.8rem;
            color: #adb5bd;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .login-container {
                max-width: 1000px;
                height: 600px;
            }

            .brand-icon {
                width: 100px;
                height: 100px;
                margin-right: 1.5rem;
            }

            .brand-icon i {
                font-size: 3.5rem;
            }

            .brand-text h1 {
                font-size: 2.2rem;
            }
        }

        @media (max-width: 1200px) {
            .login-container {
                max-width: 900px;
                height: 550px;
            }

            .login-left {
                padding: 3rem 2rem;
            }

            .login-right {
                flex: 0 0 420px;
                padding: 3rem 2rem;
            }

            .brand-logo {
                flex-direction: column;
                text-align: center;
            }

            .brand-icon {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 992px) {
            .login-container {
                flex-direction: column;
                max-width: 500px;
                height: auto;
                min-height: 600px;
            }

            .login-left {
                flex: none;
                padding: 2rem;
            }

            .login-right {
                flex: none;
                padding: 2rem;
            }

            .features-section {
                margin-top: 2rem;
            }

            .system-info {
                gap: 1rem;
            }

            .info-item {
                padding: 0.8rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 左侧品牌展示区域 -->
        <div class="login-left">
            <div class="content">
                <div class="brand-section">
                    <div class="brand-logo">
                        <div class="brand-icon">
                            <i class="fa fa-cogs"></i>
                        </div>
                        <div class="brand-text">
                            <h1>设备资料录入系统</h1>
                            <p>专业的设备数据管理平台</p>
                        </div>
                    </div>
                </div>

                <div class="features-section">
                    <div class="system-info">
                        <div class="info-item">
                            <i class="fa fa-clock"></i>
                            <span>当前时间: <span id="currentTime"></span></span>
                        </div>
                        <div class="info-item">
                            <i class="fa fa-server"></i>
                            <span>系统状态: 正常运行</span>
                        </div>
                        <div class="info-item">
                            <i class="fa fa-users"></i>
                            <span>内部使用系统</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="login-right">
            <div class="login-form-header">
                <h2>欢迎回来</h2>
                <p>请登录您的账户以继续使用系统</p>
            </div>

            <div id="loginAlert" class="alert alert-danger d-none" role="alert">
                <i class="fa fa-exclamation-circle me-2"></i>
                <span id="loginAlertMessage"></span>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-wrapper">
                        <i class="fa fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="请输入您的用户名" required autocomplete="username">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-wrapper">
                        <i class="fa fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="请输入您的密码" required autocomplete="current-password">
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-wrapper" title="勾选后将保存账号和密码，请在个人设备上使用">
                        <input type="checkbox" id="rememberMe" name="remember_me">
                        <span>记住我</span>
                    </label>
                    <a href="<?php echo BASE_URL; ?>admin/" class="admin-link">
                        <i class="fa fa-shield-alt me-1"></i>管理员登录
                    </a>
                </div>

                <button type="submit" class="btn-login" id="loginBtn">
                    <div class="btn-content">
                        <span class="btn-text">立即登录</span>
                        <i class="fa fa-arrow-right"></i>
                        <div class="loading-spinner"></div>
                    </div>
                </button>
            </form>
            
            <div class="login-footer">
                <div class="security-info">
                    <i class="fa fa-lock"></i>
                    <span>安全登录，请妥善保管账号密码</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 单位信息 -->
    <div class="unit-info">
        <div class="container">
            <div class="text-center">
                <span class="unit-name">使用单位：海四采油管理区中心三号平台</span>
            </div>
        </div>
    </div>

    <script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
    <script src="<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 初始化
            initializeForm();

            // 表单初始化
            function initializeForm() {
                // 实时时间显示
                updateTime();
                setInterval(updateTime, 1000);

                // 检查记住的账号和密码
                const rememberedUsername = localStorage.getItem('rememberedUsername');
                const rememberedPassword = localStorage.getItem('rememberedPassword');
                if (rememberedUsername) {
                    $('#username').val(rememberedUsername);
                    $('#rememberMe').prop('checked', true);
                    if (rememberedPassword) {
                        $('#password').val(rememberedPassword);
                    }
                    $('#password').focus(); // 如果有记住的用户名，焦点移到密码框
                } else {
                    $('#username').focus(); // 否则聚焦用户名输入框
                }

                // 输入框焦点效果
                $('.form-control').on('focus', function() {
                    $(this).closest('.input-wrapper').addClass('focused');
                }).on('blur', function() {
                    $(this).closest('.input-wrapper').removeClass('focused');
                });

                // 实时验证
                $('#username, #password').on('input', function() {
                    validateForm();
                });

                // 记住我选项变化时的处理
                $('#rememberMe').on('change', function() {
                    if (!$(this).is(':checked')) {
                        // 取消勾选时立即清除保存的信息
                        localStorage.removeItem('rememberedUsername');
                        localStorage.removeItem('rememberedPassword');
                    }
                });
            }

            // 更新时间显示
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                $('#currentTime').text(timeString);
            }

            // 表单验证
            function validateForm() {
                const username = $('#username').val().trim();
                const password = $('#password').val().trim();
                const $loginBtn = $('#loginBtn');

                if (username && password) {
                    $loginBtn.removeClass('disabled').prop('disabled', false);
                } else {
                    $loginBtn.addClass('disabled').prop('disabled', true);
                }
            }

            // 登录表单提交
            $('#loginForm').on('submit', function(e) {
                e.preventDefault();

                const username = $('#username').val().trim();
                const password = $('#password').val().trim();

                if (!username || !password) {
                    showAlert('请输入完整的登录信息', 'danger');
                    return;
                }

                // 显示加载状态
                const $loginBtn = $('#loginBtn');
                $loginBtn.addClass('loading').prop('disabled', true);

                // 发送登录请求
                $.ajax({
                    url: '<?php echo BASE_URL; ?>index.php?r=frontend/login',
                    method: 'POST',
                    data: {
                        username: username,
                        password: password,
                        remember: $('#rememberMe').is(':checked') ? 1 : 0
                    },
                    dataType: 'json',
                    timeout: 10000,
                    success: function(response) {
                        if (response.success) {
                            showAlert('登录成功，正在跳转...', 'success');

                            // 处理记住我功能
                            const rememberMe = $('#rememberMe').is(':checked');
                            if (rememberMe) {
                                localStorage.setItem('rememberedUsername', username);
                                localStorage.setItem('rememberedPassword', password);
                            } else {
                                localStorage.removeItem('rememberedUsername');
                                localStorage.removeItem('rememberedPassword');
                            }

                            setTimeout(function() {
                                window.location.href = '<?php echo BASE_URL; ?>';
                            }, 1500);
                        } else {
                            showAlert(response.message || '登录失败', 'danger');
                            $loginBtn.removeClass('loading').prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        let message = '登录失败，请稍后重试';

                        if (status === 'timeout') {
                            message = '请求超时，请检查网络连接';
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        } else if (xhr.status === 0) {
                            message = '网络连接失败，请检查网络';
                        }

                        showAlert(message, 'danger');
                        $loginBtn.removeClass('loading').prop('disabled', false);
                    }
                });
            });

            // 显示提示信息
            function showAlert(message, type) {
                const $alert = $('#loginAlert');
                const $message = $('#loginAlertMessage');

                $alert.removeClass('alert-danger alert-success alert-warning alert-info')
                      .addClass('alert-' + type)
                      .removeClass('d-none');
                $message.text(message);

                // 自动隐藏消息
                setTimeout(function() {
                    $alert.addClass('d-none');
                }, type === 'success' ? 5000 : 8000);
            }

            // 回车键登录
            $('#username, #password').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#loginForm').submit();
                }
            });

            // 初始验证
            validateForm();
        });

        // 忘记密码功能
        function showForgotPassword() {
            alert('请联系系统管理员重置密码');
        }
    </script>
</body>
</html>
