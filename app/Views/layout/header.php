<?php
// 统一头部（Apple 高级风格）
?><!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?php echo isset($title) ? htmlspecialchars($title) : '系统'; ?></title>
  <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
  <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
  <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
  <!-- 内存优化脚本 -->
  <script src="<?php echo BASE_URL; ?>js/memory-optimizer.js"></script>
  <script src="<?php echo BASE_URL; ?>js/low-memory-mode.js"></script>
  <script src="<?php echo BASE_URL; ?>js/table-optimizer.js"></script>
  <script src="<?php echo BASE_URL; ?>js/memory-monitor.js"></script>
</head>
<?php
$CURRENT_PATH = parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_PATH) ?: '/';
$CURRENT_QUERY = parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_QUERY) ?: '';
parse_str($CURRENT_QUERY, $QRY);
$CURRENT_ROUTE = $QRY['r'] ?? trim($CURRENT_PATH, '/');
?>
<body class="sidebar-open <?php echo $bodyClass ?? ''; ?>" data-current-path="<?php echo htmlspecialchars($CURRENT_PATH); ?>" data-route="<?php echo htmlspecialchars($CURRENT_ROUTE); ?>">
<nav class="navbar navbar-expand-lg fixed-top apple-nav shadow-sm">
  <div class="container-fluid px-3">
    <button class="btn-icon me-2" id="sidebarToggle" aria-label="Toggle Sidebar"><span class="bar"></span><span class="bar"></span><span class="bar"></span></button>
    <a class="navbar-brand d-flex align-items-center gap-2 fw-semibold" href="<?php echo BASE_URL; ?>">
      <span class="brand-dot"></span>
      <span class="d-none d-sm-inline">设备资料录入管理系统</span>
    </a>

    <div class="d-flex align-items-center gap-3 ms-auto">
      <span class="text-muted small">v1.0</span>
      <?php if (!empty($_SESSION['user'])): ?>
        <form method="post" action="<?php echo BASE_URL; ?>index.php?r=auth/logout" class="m-0">
          <span class="text-muted small me-2"><?php echo htmlspecialchars($_SESSION['user']['real_name'] ?? $_SESSION['user']['username']); ?></span>
          <button class="btn btn-sm btn-ghost" type="submit"><i class="fa fa-sign-out"></i> 退出</button>
        </form>
      <?php else: ?>
        <a class="btn btn-sm btn-ghost" href="<?php echo BASE_URL; ?>index.php?r=auth/login"><i class="fa fa-user"></i> 登录</a>
      <?php endif; ?>
    </div>
  </div>
</nav>

