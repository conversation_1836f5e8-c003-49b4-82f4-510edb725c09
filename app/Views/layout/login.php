<?php
// 登录页专属布局（无顶栏/侧栏）
?><!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?php echo isset($title) ? htmlspecialchars($title) : '登录'; ?></title>
  <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
  <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css">
  <link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
</head>
<body class="login-page">
  <div class="login-container">
    <div class="login-left">
      <div class="login-brand-section">
        <div class="brand-logo">
          <div class="logo-icon"></div>
          <div class="logo-text">
            <div class="system-name">设备资料录入管理系统</div>
            <div class="system-desc">专业的设备数据管理平台</div>
          </div>
        </div>
        <div class="feature-list">
          <div class="feature-item">
            <i class="fa fa-shield-alt"></i>
            <span>安全可靠的数据保护</span>
          </div>
          <div class="feature-item">
            <i class="fa fa-chart-line"></i>
            <span>实时数据分析与报表</span>
          </div>
          <div class="feature-item">
            <i class="fa fa-users"></i>
            <span>多角色权限管理</span>
          </div>
        </div>
      </div>
    </div>
    <div class="login-right">
      <div class="login-form-container">
        <div class="login-header">
          <h2>欢迎回来</h2>
          <p>请登录您的账户以继续</p>
        </div>
        
        <?php if (!empty($error ?? '')): ?>
          <div class="alert alert-danger login-alert">
            <i class="fa fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
          </div>
        <?php endif; ?>
        
        <form method="post" action="<?php echo BASE_URL; ?>index.php?r=auth/login" class="login-form">
          <div class="form-group">
            <label class="form-label">账号</label>
            <div class="input-wrapper">
              <i class="fa fa-user input-icon"></i>
              <input required name="username" class="form-control" autocomplete="username" 
                     placeholder="请输入您的账号" value="<?php echo htmlspecialchars($rememberedUsername ?? ''); ?>">
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">密码</label>
            <div class="input-wrapper">
              <i class="fa fa-lock input-icon"></i>
              <input required type="password" name="password" class="form-control" autocomplete="current-password" 
                     placeholder="请输入您的密码" value="<?php echo htmlspecialchars($rememberedPassword ?? ''); ?>">
            </div>
          </div>
          
          <div class="form-options">
            <div class="remember-options">
              <label class="checkbox-wrapper">
                <input type="checkbox" name="remember_username" <?php echo !empty($rememberedUsername)?'checked':''; ?>>
                <span class="checkmark"></span>
                <span class="checkbox-text">记住账号</span>
              </label>
              <label class="checkbox-wrapper">
                <input type="checkbox" name="remember_password" <?php echo !empty($rememberedPassword)?'checked':''; ?>>
                <span class="checkmark"></span>
                <span class="checkbox-text">记住密码</span>
              </label>
            </div>
          </div>
          
          <button class="btn-login" type="submit">
            <span>登录</span>
            <i class="fa fa-arrow-right"></i>
          </button>
        </form>
        
        <div class="login-footer">
          <div class="version-info">
            <span>版本 v1.0</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
  <script src="<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js"></script>
  <script src="<?php echo BASE_URL; ?>js/app.js"></script>
</body>
</html>
