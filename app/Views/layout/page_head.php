<?php
// 自动面包屑与标题组件（支持 r= 与伪静态路径，含兜底生成）
$routeQuery = $_GET['r'] ?? trim(parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_PATH), '/');
$routeQuery = ($routeQuery === '' || $routeQuery === 'index.php') ? 'index' : $routeQuery;

if (!function_exists('_bbgl_breadcrumb_map')) {
function _bbgl_breadcrumb_map(string $route, ?string $fallbackTitle = null): array {
  $map = [
    'index' => ['首页'],
    'home/index' => ['首页'],
    'auth/login' => ['登录'],
    'auth/logout' => ['退出'],
    // 系统设置
    'sys/config' => ['系统设置','系统配置'],
    'sys/users' => ['系统设置','用户管理'],
    'sys/static' => ['系统设置','静态页面管理'],
    'sys/reports' => ['系统设置','报表配置'],
    'sys/templates' => ['系统设置','模板管理'],
    'sys/devices' => ['系统设置','设备管理'],
    'sys/ipAcl' => ['系统设置','IP访问控制'],
    // 录入/查询
    'entry/report' => ['录入','报表录入'],
    'entry/site' => ['录入','现场资料'],
    'entry/cb26' => ['录入','CB26资料'],
    'entry/ctrl' => ['录入','中控室资料'],
    'query/site' => ['查询','现场资料'],
    'query/cb26' => ['查询','CB26资料'],
    'query/ctrl' => ['查询','中控室资料'],
    // API
    'api/reports/devices' => ['API','报表设备'],
    'api/reports/all-devices' => ['API','所有设备'],
    'api/reports/save-devices' => ['API','保存设备配置'],
  ];

  // 最长前缀匹配
  $best = null; $bestLen = 0;
  foreach ($map as $k => $v) {
    if (strpos($route, $k) === 0 && strlen($k) > $bestLen) { $best = $v; $bestLen = strlen($k); }
  }
  if ($best) return $best;

  // 兜底：根据分段智能生成
  $parts = array_values(array_filter(explode('/', $route)));
  if (!$parts) return ['首页'];
  $firstMap = ['sys' => '系统设置', 'entry' => '录入', 'query' => '查询', 'auth' => '认证', 'api' => 'API'];
  $crumbs = [];
  if (isset($firstMap[$parts[0]])) $crumbs[] = $firstMap[$parts[0]];
  if (!empty($parts[1])) {
    $secondMap = [
      'reports' => '报表配置', 'templates' => '模板管理', 'devices' => '设备管理',
      'config' => '系统配置', 'users' => '用户管理', 'ipAcl' => 'IP访问控制'
    ];
    $crumbs[] = $secondMap[$parts[1]] ?? ($fallbackTitle ?: '页面');
  }
  if (!$crumbs) $crumbs = ['首页'];
  return $crumbs;
}
}

$crumbs = _bbgl_breadcrumb_map($routeQuery, $pageTitle ?? null);

// 如果没有设置 pageTitle，尝试从面包屑推导
if (empty($pageTitle) && !empty($crumbs)) {
  $pageTitle = end($crumbs);
}
?>
<div class="page-head mb-3">
  <!-- 面包屑导航 -->
  <div class="breadcrumb small text-muted mb-2"><?php echo htmlspecialchars($crumbs ? implode(' / ', $crumbs) : ''); ?></div>
  <!-- 页面标题居中 -->
  <div class="text-center">
    <div class="page-title"><?php echo htmlspecialchars($pageTitle ?? '页面'); ?></div>
  </div>
</div>

