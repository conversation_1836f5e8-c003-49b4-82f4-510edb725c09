<?php
// 左侧栏（演示用，真实菜单应由角色过滤后生成）
?>
<aside class="sidebar" id="sidebar">
  <div class="sidebar-inner">
    <div class="sidebar-title text-muted small mb-2">导航</div>
    <script>
      function toggleGroup(name, forceOpen){
        // 只选择直接属于该分组的项目，排除子级分组的项目
        // 统一根据分组名选择对应的折叠菜单项
        var items = document.querySelectorAll('.collapse-item.' + name);

        var groupHeader = document.querySelector('[data-group="'+name+'"] .group-header');
        var anyVisible = false;

        // 检查是否有可见的项目
        items.forEach(function(el){
          if (!el.classList.contains('hide')) anyVisible = true;
        });

        // 如果强制打开，或者切换显示/隐藏状态
        var shouldShow = forceOpen === true || (forceOpen !== false && !anyVisible);

        items.forEach(function(el){
          if (shouldShow) {
            // 只显示直接子项，不自动展开更深层级
            el.classList.remove('hide');
            // 如果是可折叠的子项（如报表组），确保它们默认是关闭状态
            if (el.classList.contains('collapsible')) {
              el.classList.add('collapsed');
            }
          } else {
            el.classList.add('hide');     // 隐藏直接子项
            // 如果是关闭操作，同时关闭所有子级菜单
            if (el.classList.contains('collapsible')) {
              var subGroupName = el.getAttribute('data-group');
              if (subGroupName) {
                var subItems = document.querySelectorAll('.collapse-item.' + subGroupName);
                subItems.forEach(function(subEl) {
                  subEl.classList.add('hide');
                });
                // 更新子级箭头状态
                el.classList.add('collapsed');
              }
            }
          }
        });

        // 更新箭头方向
        if (groupHeader) {
          groupHeader.parentElement.classList.toggle('collapsed', !shouldShow);
        }

        // 保存菜单状态到localStorage
        try {
          var menuState = JSON.parse(localStorage.getItem('menuState') || '{}');
          menuState[name] = shouldShow;
          localStorage.setItem('menuState', JSON.stringify(menuState));
        } catch(e) {
          // 忽略菜单状态保存错误
        }
      }

      document.addEventListener('DOMContentLoaded', function(){
        // 默认折叠所有二级及以下菜单（包括所有collapse-item）
        document.querySelectorAll('.collapse-item').forEach(function(el){
          el.classList.add('hide');
        });

        // 确保所有可折叠组默认关闭状态（显示箭头向右）
        document.querySelectorAll('.collapsible').forEach(function(el){
          el.classList.add('collapsed');
        });

        // 为所有菜单链接添加滚动位置保存
        document.querySelectorAll('.menu .item').forEach(function(item){
          item.addEventListener('click', function() {
            // 保存当前滚动位置
            sessionStorage.setItem('scrollPosition', window.pageYOffset || document.documentElement.scrollTop);
          });
        });

        // 智能恢复滚动位置
        function restoreScrollPosition() {
          const savedPosition = sessionStorage.getItem('scrollPosition');
          if (savedPosition) {
            const position = parseInt(savedPosition);
            // 延迟恢复，等待页面内容加载完成
            setTimeout(function() {
              window.scrollTo(0, position);
              sessionStorage.removeItem('scrollPosition');
            }, 100);
          }
        }

        // 立即尝试恢复
        restoreScrollPosition();

        // 如果页面还在加载，等待加载完成后再次尝试
        if (document.readyState !== 'complete') {
          window.addEventListener('load', function() {
            setTimeout(restoreScrollPosition, 200);
          });
        }

        // 保持侧边栏滚动位置
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
          // 恢复侧边栏滚动位置
          const sidebarScrollTop = sessionStorage.getItem('sidebarScrollTop');
          if (sidebarScrollTop) {
            sidebar.scrollTop = parseInt(sidebarScrollTop);
            sessionStorage.removeItem('sidebarScrollTop');
          }

          // 保存侧边栏滚动位置
          window.addEventListener('beforeunload', function() {
            sessionStorage.setItem('sidebarScrollTop', sidebar.scrollTop);
          });
        }

        // 获取当前路由
        var current = document.body.getAttribute('data-route')||'';

        // 获取当前URL参数
        var urlParams = new URLSearchParams(window.location.search);
        var currentR = urlParams.get('r') || '';

        // 构建当前路由（无需附加参数）
        var fullCurrentRoute = currentR;

        // 高亮当前菜单项
        document.querySelectorAll('.menu .item').forEach(function(item){
          var itemRoute = item.getAttribute('data-route');
          if (itemRoute && (current === itemRoute || fullCurrentRoute === itemRoute)) {
            item.classList.add('active');
          }
        });

        // 恢复保存的菜单状态
        try {
          var menuState = JSON.parse(localStorage.getItem('menuState') || '{}');
          Object.keys(menuState).forEach(function(groupName) {
            if (menuState[groupName]) {
              toggleGroup(groupName, true); // 强制打开
            }
          });
        } catch(e) {
          // 忽略菜单状态恢复错误
        }

        // 如果没有保存的状态，根据当前路由自动展开对应的菜单组
        var hasOpenGroups = false;
        try {
          var menuState = JSON.parse(localStorage.getItem('menuState') || '{}');
          hasOpenGroups = Object.values(menuState).some(function(state) { return state; });
        } catch(e) {}

        if (!hasOpenGroups) {
          if (current.startsWith('sys/')) {
            if (current.startsWith('sys/reports') || current.startsWith('sys/templates') || current.startsWith('sys/devices')) {
              toggleGroup('reports-config', true);
            }
          }
        }
      });
    </script>
    <ul class="menu">
      <li><a class="item" data-route="index" href="<?php echo BASE_URL; ?>index.php"><i class="fa fa-home"></i><span>首页</span></a></li>

      <?php if (!empty($_SESSION['roles']) && in_array('admin', $_SESSION['roles'])): ?>
      <li class="group">系统设置</li>
      <li><a class="item depth-1" data-route="sys/config" href="<?php echo BASE_URL; ?>index.php?r=sys/config"><span>系统配置</span></a></li>
      <li><a class="item depth-1" data-route="sys/users" href="<?php echo BASE_URL; ?>index.php?r=sys/users"><span>用户管理</span></a></li>

      <li class="collapsible" data-group="reports-config">
        <div class="group-header depth-1" onclick="toggleGroup('reports-config')">
          <span>报表配置</span><i class="fa fa-chevron-down float-end"></i>
        </div>
      </li>
      <li class="collapse-item reports-config">
        <a class="item depth-2" data-route="sys/reports" href="<?php echo BASE_URL; ?>index.php?r=sys/reports"><span>报表列表</span></a>
      </li>
      <li class="collapse-item reports-config">
        <a class="item depth-2" data-route="sys/templates" href="<?php echo BASE_URL; ?>index.php?r=sys/templates"><span>模板管理</span></a>
      </li>
      <li class="collapse-item reports-config">
        <a class="item depth-2" data-route="sys/devices" href="<?php echo BASE_URL; ?>index.php?r=sys/devices"><span>设备管理</span></a>
      </li>
      <li class="collapse-item reports-config">
        <a class="item depth-2" data-route="sys/static" href="<?php echo BASE_URL; ?>index.php?r=sys/static"><span>静态页面管理</span></a>
      </li>
      <?php endif; ?>
    </ul>
  </div>
</aside>