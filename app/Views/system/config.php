<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fa fa-sliders me-2"></i>系统配置
        </h5>
    </div>
    <div class="card-body">
        <form id="configForm" method="POST" action="<?php echo BASE_URL; ?>index.php?r=sys/saveConfig">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="system_name" class="form-label">系统名称</label>
                        <input type="text" class="form-control" id="system_name" name="system_name" 
                               value="<?php echo htmlspecialchars($configs['system_name']['value'] ?? '设备资料录入管理系统'); ?>" required>
                        <div class="form-text">显示在页面标题和登录页面的系统名称</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="system_version" class="form-label">系统版本</label>
                        <input type="text" class="form-control" id="system_version" name="system_version" 
                               value="<?php echo htmlspecialchars($configs['system_version']['value'] ?? '1.0.0'); ?>" required>
                        <div class="form-text">当前系统版本号</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="session_ttl_days" class="form-label">会话超时（天）</label>
                        <input type="number" class="form-control" id="session_ttl_days" name="session_ttl_days"
                               value="<?php echo (int)($configs['session_ttl_days']['value'] ?? 7); ?>" min="1" max="30" required>
                        <div class="form-text">用户登录后的会话有效期，单位：天</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="ip_acl_mode" class="form-label">IP访问控制</label>
                        <select class="form-select" id="ip_acl_mode" name="ip_acl_mode">
                            <option value="disabled" <?php echo ($configs['ip_acl_mode']['value'] ?? 'disabled') === 'disabled' ? 'selected' : ''; ?>>禁用</option>
                            <option value="whitelist" <?php echo ($configs['ip_acl_mode']['value'] ?? 'disabled') === 'whitelist' ? 'selected' : ''; ?>>白名单模式</option>
                            <option value="blacklist" <?php echo ($configs['ip_acl_mode']['value'] ?? 'disabled') === 'blacklist' ? 'selected' : ''; ?>>黑名单模式</option>
                        </select>
                        <div class="form-text">
                            禁用：不进行IP限制<br>
                            白名单：仅允许白名单中的IP访问<br>
                            黑名单：禁止黑名单中的IP访问
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_entry_info" name="show_entry_info"
                                   <?php echo ($configs['show_entry_info']['value'] ?? true) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="show_entry_info">
                                显示录入信息
                            </label>
                        </div>
                        <div class="form-text">在查询页面显示录入人、录入时间信息</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_modify_info" name="show_modify_info"
                                   <?php echo ($configs['show_modify_info']['value'] ?? true) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="show_modify_info">
                                显示修改信息
                            </label>
                        </div>
                        <div class="form-text">在查询页面显示修改人、修改时间信息</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fa fa-save me-1"></i>保存配置
                </button>
                <a href="<?php echo BASE_URL; ?>index.php?r=sys/ipAcl" class="btn btn-outline-secondary">
                    <i class="fa fa-shield-alt me-1"></i>IP访问控制管理
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('configForm');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>保存中...';
        
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示成功消息
                showAlert('success', data.message || '配置保存成功');
                
                // 如果系统名称发生变化，更新页面标题
                const systemName = formData.get('system_name');
                if (systemName) {
                    document.title = systemName;
                    const headerTitle = document.querySelector('.navbar-brand');
                    if (headerTitle) {
                        headerTitle.textContent = systemName;
                    }
                }
            } else {
                showAlert('danger', data.message || '配置保存失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '网络错误，请稍后重试');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
    
    function showAlert(type, message) {
        // 移除现有的提示
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // 创建新的提示
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到表单前面
        form.parentNode.insertBefore(alert, form);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
});
</script>
