<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fa fa-shield-alt me-2"></i>IP访问控制
        </h5>
        <div>
            <a href="<?php echo BASE_URL; ?>index.php?r=sys/config" class="btn btn-outline-secondary me-2">
                <i class="fa fa-arrow-left me-1"></i>返回系统配置
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ipAclModal" onclick="openCreateModal()">
                <i class="fa fa-plus me-1"></i>新增规则
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fa fa-info-circle me-2"></i>
            <strong>说明：</strong>
            <ul class="mb-0 mt-2">
                <li><strong>白名单模式：</strong>仅允许白名单中的IP地址访问系统</li>
                <li><strong>黑名单模式：</strong>禁止黑名单中的IP地址访问系统</li>
                <li><strong>IP格式：</strong>支持单个IP（如：*************）或CIDR网段（如：***********/24）</li>
                <li><strong>优先级：</strong>白名单优先于黑名单</li>
            </ul>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>类型</th>
                        <th>IP/网段</th>
                        <th>备注</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($ipAcls)): ?>
                    <tr>
                        <td colspan="6" class="text-center text-muted">暂无IP访问控制规则</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($ipAcls as $acl): ?>
                        <tr>
                            <td><?php echo $acl['id']; ?></td>
                            <td>
                                <?php if ($acl['type'] === 'whitelist'): ?>
                                    <span class="badge bg-success">白名单</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">黑名单</span>
                                <?php endif; ?>
                            </td>
                            <td><code><?php echo htmlspecialchars($acl['cidr']); ?></code></td>
                            <td><?php echo htmlspecialchars($acl['remark'] ?? ''); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($acl['created_at'])); ?></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" onclick="openEditModal(<?php echo htmlspecialchars(json_encode($acl)); ?>)">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteIpAcl(<?php echo $acl['id']; ?>, '<?php echo htmlspecialchars($acl['cidr']); ?>')">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- IP访问控制编辑模态框 -->
<div class="modal fade" id="ipAclModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ipAclModalTitle">新增IP访问控制规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="ipAclForm">
                <div class="modal-body">
                    <input type="hidden" id="acl_id" name="acl_id">
                    
                    <div class="mb-3">
                        <label for="type" class="form-label">类型 <span class="text-danger">*</span></label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="whitelist">白名单</option>
                            <option value="blacklist">黑名单</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cidr" class="form-label">IP地址/网段 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="cidr" name="cidr" required 
                               placeholder="例如：************* 或 ***********/24">
                        <div class="form-text">
                            支持单个IP地址或CIDR网段格式
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="remark" class="form-label">备注</label>
                        <textarea class="form-control" id="remark" name="remark" rows="3" 
                                  placeholder="可选的备注信息"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let isEditMode = false;

function openCreateModal() {
    isEditMode = false;
    document.getElementById('ipAclModalTitle').textContent = '新增IP访问控制规则';
    document.getElementById('ipAclForm').reset();
    document.getElementById('acl_id').value = '';
}

function openEditModal(acl) {
    isEditMode = true;
    document.getElementById('ipAclModalTitle').textContent = '编辑IP访问控制规则';
    
    document.getElementById('acl_id').value = acl.id;
    document.getElementById('type').value = acl.type;
    document.getElementById('cidr').value = acl.cidr;
    document.getElementById('remark').value = acl.remark || '';
    
    new bootstrap.Modal(document.getElementById('ipAclModal')).show();
}

function deleteIpAcl(aclId, cidr) {
    if (confirm(`确定要删除IP访问控制规则 "${cidr}" 吗？`)) {
        const formData = new FormData();
        formData.append('acl_id', aclId);
        
        fetch('<?php echo BASE_URL; ?>index.php?r=sys/deleteIpAcl', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '删除失败，请稍后重试');
        });
    }
}

// 表单提交处理
document.getElementById('ipAclForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.disabled = true;
    submitBtn.textContent = '保存中...';
    
    const action = isEditMode ? 'updateIpAcl' : 'createIpAcl';
    
    fetch(`<?php echo BASE_URL; ?>index.php?r=sys/${action}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('ipAclModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '操作失败，请稍后重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
});

function showAlert(type, message) {
    const existingAlert = document.querySelector('.alert:not(.alert-info)');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.card-body').insertBefore(alert, document.querySelector('.table-responsive'));
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}
</script>
