<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fa fa-users me-2"></i>用户管理
        </h5>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal" onclick="openCreateModal()">
            <i class="fa fa-plus me-1"></i>新增用户
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>姓名</th>

                        <th>角色</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td><?php echo $user['id']; ?></td>
                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                        <td><?php echo htmlspecialchars($user['real_name']); ?></td>
                        <td>
                            <?php if ($user['role_names']): ?>
                                <?php foreach (explode(',', $user['role_names']) as $roleName): ?>
                                    <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($roleName); ?></span>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <span class="text-muted">无角色</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($user['status'] == 1): ?>
                                <span class="badge bg-success">启用</span>
                            <?php else: ?>
                                <span class="badge bg-danger">禁用</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="openEditModal(<?php echo htmlspecialchars(json_encode($user)); ?>)">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <?php if ($user['id'] != ($_SESSION['user']['id'] ?? 0)): ?>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                    <i class="fa fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 用户编辑模态框 -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalTitle">新增用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="userForm">
                <div class="modal-body">
                    <input type="hidden" id="user_id" name="user_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="real_name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="real_name" name="real_name" required>
                            </div>
                        </div>
                    </div>
                    

                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">密码 <span class="text-danger" id="passwordRequired">*</span></label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text" id="passwordHelp">新增用户时必填，编辑时留空表示不修改密码</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">权限角色 <span class="text-danger">*</span></label>
                        <div class="row">
                            <?php
                            $permissionRoles = array_filter($roles, function($role) {
                                return in_array($role['code'], ['admin', 'mod', 'user']);
                            });
                            foreach ($permissionRoles as $role): ?>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="permission_role" value="<?php echo $role['id']; ?>" id="perm_role_<?php echo $role['id']; ?>" required>
                                    <label class="form-check-label" for="perm_role_<?php echo $role['id']; ?>">
                                        <?php echo htmlspecialchars($role['name']); ?>
                                    </label>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="form-text">必须选择一个权限角色，决定用户的系统功能权限</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">业务角色</label>
                        <div class="row">
                            <?php
                            $businessRoles = array_filter($roles, function($role) {
                                return in_array($role['code'], ['site', 'ctrl', 'cb26']);
                            });
                            foreach ($businessRoles as $role): ?>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="business_roles[]" value="<?php echo $role['id']; ?>" id="biz_role_<?php echo $role['id']; ?>">
                                    <label class="form-check-label" for="biz_role_<?php echo $role['id']; ?>">
                                        <?php echo htmlspecialchars($role['name']); ?>
                                    </label>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="form-text">可选择多个业务角色，决定用户可访问的业务模块</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let isEditMode = false;

function openCreateModal() {
    isEditMode = false;
    document.getElementById('userModalTitle').textContent = '新增用户';
    document.getElementById('userForm').reset();
    document.getElementById('user_id').value = '';
    document.getElementById('password').required = true;
    document.getElementById('passwordRequired').style.display = 'inline';
    document.getElementById('passwordHelp').textContent = '新增用户时必填';
}

function openEditModal(user) {
    isEditMode = true;
    document.getElementById('userModalTitle').textContent = '编辑用户';
    
    // 填充表单数据
    document.getElementById('user_id').value = user.id;
    document.getElementById('username').value = user.username;
    document.getElementById('real_name').value = user.real_name;
    document.getElementById('password').value = '';
    document.getElementById('status').value = user.status;
    
    // 设置密码字段为非必填
    document.getElementById('password').required = false;
    document.getElementById('passwordRequired').style.display = 'none';
    document.getElementById('passwordHelp').textContent = '留空表示不修改密码';
    
    // 设置权限角色选择（单选）
    const permissionRadios = document.querySelectorAll('input[name="permission_role"]');
    permissionRadios.forEach(radio => radio.checked = false);

    // 设置业务角色选择（多选）
    const businessCheckboxes = document.querySelectorAll('input[name="business_roles[]"]');
    businessCheckboxes.forEach(checkbox => checkbox.checked = false);

    if (user.role_codes) {
        const userRoles = user.role_codes.split(',');

        // 设置权限角色
        permissionRadios.forEach(radio => {
            const roleId = radio.value;
            <?php foreach ($roles as $role): ?>
            if (roleId === '<?php echo $role['id']; ?>' && userRoles.includes('<?php echo $role['code']; ?>') && ['admin', 'mod', 'user'].includes('<?php echo $role['code']; ?>')) {
                radio.checked = true;
            }
            <?php endforeach; ?>
        });

        // 设置业务角色
        businessCheckboxes.forEach(checkbox => {
            const roleId = checkbox.value;
            <?php foreach ($roles as $role): ?>
            if (roleId === '<?php echo $role['id']; ?>' && userRoles.includes('<?php echo $role['code']; ?>') && ['site', 'ctrl', 'cb26'].includes('<?php echo $role['code']; ?>')) {
                checkbox.checked = true;
            }
            <?php endforeach; ?>
        });
    }
    
    // 显示模态框
    new bootstrap.Modal(document.getElementById('userModal')).show();
}

function deleteUser(userId, username) {
    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
        const formData = new FormData();
        formData.append('user_id', userId);
        
        fetch('<?php echo BASE_URL; ?>index.php?r=users/delete', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '删除失败，请稍后重试');
        });
    }
}

// 表单提交处理
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.disabled = true;
    submitBtn.textContent = '保存中...';
    
    const action = isEditMode ? 'update' : 'create';
    
    fetch(`<?php echo BASE_URL; ?>index.php?r=users/${action}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '操作失败，请稍后重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
});

function showAlert(type, message) {
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.card').parentNode.insertBefore(alert, document.querySelector('.card'));
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}
</script>
