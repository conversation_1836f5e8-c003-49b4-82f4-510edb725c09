<?php
// 基础引导：常量、自动加载、错误处理、路由注册

declare(strict_types=1);

// 目录常量
// 基础URL（自动推断），适应任何部署环境，末尾带/
if (!defined('BASE_URL')) {
    $scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

    // 动态获取基础路径，适应任何部署目录
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '/index.php';
    $basePath = dirname($scriptName);

    // 规范化路径
    $basePath = str_replace('\\', '/', $basePath); // Windows路径兼容
    $basePath = rtrim($basePath, '/'); // 移除末尾斜杠

    // 确保以斜杠结尾
    if ($basePath === '' || $basePath === '.') {
        $basePath = '/'; // 根目录部署
    } else {
        $basePath = $basePath . '/';
    }

    define('BASE_URL', $scheme . '://' . $host . $basePath);
}

const APP_PATH = __DIR__ . '/../app';
const VIEW_PATH = __DIR__ . '/../app/Views';
const LOG_PATH = __DIR__ . '/../logs';

// 会话超时（天）默认 7 天，可从系统配置读取
const DEFAULT_SESSION_TTL_DAYS = 7;

// 自动加载（简单 PSR-4）
spl_autoload_register(function ($class) {
    $prefix = 'App\\';
    $base_dir = APP_PATH . '/';
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) return;
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    if (file_exists($file)) require $file;
});

// 确保日志目录存在
if (!is_dir(LOG_PATH)) @mkdir(LOG_PATH, 0777, true);

// 基础错误/异常处理
set_exception_handler(function (Throwable $e) {
    error_log('[EXCEPTION] ' . $e->getMessage() . "\n" . $e->getTraceAsString());
    http_response_code(500);
    header('Content-Type: text/plain; charset=UTF-8');
    echo 'Server Error: ' . $e->getMessage();
});

set_error_handler(function ($errno, $errstr, $errfile, $errline) {
    error_log(sprintf('[ERROR] (%d) %s in %s:%d', $errno, $errstr, $errfile, $errline));
});

// IP访问控制检查（在Session之前）
if (!isset($_GET['r']) || $_GET['r'] !== 'auth/login') {
    // 登录页面不进行IP检查，避免管理员被锁定
    if (class_exists('\App\Middlewares\IpAclMiddleware')) {
        if (!\App\Middlewares\IpAclMiddleware::check()) {
            \App\Middlewares\IpAclMiddleware::deny();
        }
    }
}

// 启动 Session（同站点七天有效）
session_set_cookie_params(DEFAULT_SESSION_TTL_DAYS * 24 * 3600);
session_start();

// 注册基础路由 - 前端用户界面
\App\Core\Router::register('GET', '/', [\App\Controllers\FrontendController::class, 'index']);
\App\Core\Router::register('GET', '/index', [\App\Controllers\FrontendController::class, 'index']);
\App\Core\Router::register('GET', '/index.php', [\App\Controllers\FrontendController::class, 'index']);

// 管理后台路由（使用 /admin 前缀）
\App\Core\Router::register('GET', '/admin', [\App\Controllers\HomeController::class, 'index']);
\App\Core\Router::register('GET', '/admin/', [\App\Controllers\HomeController::class, 'index']);
\App\Core\Router::register('GET', '/admin/index', [\App\Controllers\HomeController::class, 'index']);

// 前端认证路由
\App\Core\Router::register('GET', '/login', [\App\Controllers\FrontendController::class, 'loginPage']);
\App\Core\Router::register('POST', '/login', [\App\Controllers\FrontendController::class, 'login']);
\App\Core\Router::register('POST', '/logout', [\App\Controllers\FrontendController::class, 'logout']);
\App\Core\Router::register('GET', 'frontend/login', [\App\Controllers\FrontendController::class, 'loginPage']);
\App\Core\Router::register('POST', 'frontend/login', [\App\Controllers\FrontendController::class, 'login']);
\App\Core\Router::register('POST', 'frontend/logout', [\App\Controllers\FrontendController::class, 'logout']);

// 前端API路由
\App\Core\Router::register('GET', 'frontend/getMenuData', [\App\Controllers\FrontendController::class, 'getMenuData']);
\App\Core\Router::register('GET', 'frontend/getStaticPages', [\App\Controllers\FrontendController::class, 'getStaticPages']);

// 静态页面路由
\App\Core\Router::register('GET', 'static/show', [\App\Controllers\StaticPageController::class, 'show']);
\App\Core\Router::register('GET', 'static/list', [\App\Controllers\StaticPageController::class, 'list']);

// API路由
\App\Core\Router::register('GET', 'api/user', [\App\Controllers\ApiController::class, 'user']);
\App\Core\Router::register('GET', 'api/stats', [\App\Controllers\ApiController::class, 'stats']);
\App\Core\Router::register('GET', 'api/reports', [\App\Controllers\ApiController::class, 'reports']);
\App\Core\Router::register('GET', 'api/devices', [\App\Controllers\ApiController::class, 'devices']);
\App\Core\Router::register('GET', 'api/menu', [\App\Controllers\ApiController::class, 'menu']);
\App\Core\Router::register('GET', 'api/health', [\App\Controllers\ApiController::class, 'health']);

// 管理后台认证路由
\App\Core\Router::register('GET', '/admin', [\App\Controllers\HomeController::class, 'index']);
\App\Core\Router::register('GET', '/admin/', [\App\Controllers\HomeController::class, 'index']);
\App\Core\Router::register('GET', '/admin/login', [\App\Controllers\AuthController::class, 'loginPage']);
\App\Core\Router::register('POST', '/admin/login', [\App\Controllers\AuthController::class, 'login']);
\App\Core\Router::register('POST', '/admin/logout', [\App\Controllers\AuthController::class, 'logout']);
\App\Core\Router::register('GET', '/auth/login', [\App\Controllers\AuthController::class, 'loginPage']);
\App\Core\Router::register('POST', '/auth/login', [\App\Controllers\AuthController::class, 'login']);
\App\Core\Router::register('POST', '/auth/logout', [\App\Controllers\AuthController::class, 'logout']);


// 管理后台页面路由（伪静态路径，r= 也兼容）
\App\Core\Router::register('GET', '/admin/sys', [\App\Controllers\SystemController::class, 'config']);
\App\Core\Router::register('GET', '/admin/sys/config', [\App\Controllers\SystemController::class, 'config']);
\App\Core\Router::register('GET', '/admin/sys/users', [\App\Controllers\UsersController::class, 'index']);
\App\Core\Router::register('GET', '/admin/sys/reports', [\App\Controllers\ReportsController::class, 'index']);

// 兼容原有路由（无admin前缀）
\App\Core\Router::register('GET', '/sys', [\App\Controllers\SystemController::class, 'config']);
\App\Core\Router::register('GET', '/sys/config', [\App\Controllers\SystemController::class, 'config']);
\App\Core\Router::register('GET', '/sys/users', [\App\Controllers\UsersController::class, 'index']);
\App\Core\Router::register('GET', '/sys/reports', [\App\Controllers\ReportsController::class, 'index']);

// r= 形式路由
\App\Core\Router::register('GET', 'sys/config', [\App\Controllers\SystemController::class, 'config']);
\App\Core\Router::register('POST', 'sys/saveConfig', [\App\Controllers\SystemController::class, 'saveConfig']);
\App\Core\Router::register('GET', 'sys/ipAcl', [\App\Controllers\SystemController::class, 'ipAcl']);
\App\Core\Router::register('POST', 'sys/createIpAcl', [\App\Controllers\SystemController::class, 'createIpAcl']);
\App\Core\Router::register('POST', 'sys/updateIpAcl', [\App\Controllers\SystemController::class, 'updateIpAcl']);
\App\Core\Router::register('POST', 'sys/deleteIpAcl', [\App\Controllers\SystemController::class, 'deleteIpAcl']);

// 查询页面路由
\App\Core\Router::register('GET', 'query/continuous', [\App\Controllers\QueryController::class, 'continuous']);
\App\Core\Router::register('GET', 'query/generic', [\App\Controllers\QueryController::class, 'generic']);
\App\Core\Router::register('GET', 'sys/users', [\App\Controllers\UsersController::class, 'index']);
\App\Core\Router::register('POST', 'users/create', [\App\Controllers\UsersController::class, 'create']);
\App\Core\Router::register('POST', 'users/update', [\App\Controllers\UsersController::class, 'update']);
\App\Core\Router::register('POST', 'users/delete', [\App\Controllers\UsersController::class, 'delete']);
\App\Core\Router::register('GET', 'sys/reports', [\App\Controllers\ReportsController::class, 'index']);
\App\Core\Router::register('GET', 'sys/templates', [\App\Controllers\TemplatesController::class, 'index']);
\App\Core\Router::register('GET', 'sys/devices', [\App\Controllers\DevicesController::class, 'index']);

\App\Core\Router::register('GET', '/entry/site', [\App\Controllers\EntryController::class, 'site']);
\App\Core\Router::register('GET', '/entry/cb26', [\App\Controllers\EntryController::class, 'cb26']);
\App\Core\Router::register('GET', '/entry/ctrl', [\App\Controllers\EntryController::class, 'ctrl']);


\App\Core\Router::register('GET', '/query/site', [\App\Controllers\QueryController::class, 'site']);
\App\Core\Router::register('GET', '/query/cb26', [\App\Controllers\QueryController::class, 'cb26']);
\App\Core\Router::register('GET', '/query/ctrl', [\App\Controllers\QueryController::class, 'ctrl']);

// r= 形式路由
\App\Core\Router::register('GET', 'entry/site', [\App\Controllers\EntryController::class, 'site']);
\App\Core\Router::register('GET', 'entry/cb26', [\App\Controllers\EntryController::class, 'cb26']);
\App\Core\Router::register('GET', 'entry/ctrl', [\App\Controllers\EntryController::class, 'ctrl']);

\App\Core\Router::register('GET', 'entry/report', [\App\Controllers\EntryController::class, 'report']);
\App\Core\Router::register('GET', 'query/site', [\App\Controllers\QueryController::class, 'site']);
\App\Core\Router::register('GET', 'query/cb26', [\App\Controllers\QueryController::class, 'cb26']);
\App\Core\Router::register('GET', 'query/ctrl', [\App\Controllers\QueryController::class, 'ctrl']);

// 注意：旧的泵类路由已删除，现在统一使用通用录入系统

// 通用录入 API
\App\Core\Router::register('GET', '/api/entry/load', [\App\Controllers\EntryController::class, 'entryLoad']);
\App\Core\Router::register('POST', '/api/entry/save', [\App\Controllers\EntryController::class, 'entrySave']);
\App\Core\Router::register('GET', 'api/entry/load', [\App\Controllers\EntryController::class, 'entryLoad']);
\App\Core\Router::register('POST', 'api/entry/save', [\App\Controllers\EntryController::class, 'entrySave']);

// 用户 API
\App\Core\Router::register('GET', '/api/user/current', [\App\Controllers\UserController::class, 'current']);
\App\Core\Router::register('GET', 'api/user/current', [\App\Controllers\UserController::class, 'current']);

// 报表配置 API
\App\Core\Router::register('GET', 'api/reports/devices', [\App\Controllers\ReportsController::class, 'getDevices']);
\App\Core\Router::register('GET', 'api/reports/all-devices', [\App\Controllers\ReportsController::class, 'getAllDevices']);
\App\Core\Router::register('POST', 'api/reports/save-devices', [\App\Controllers\ReportsController::class, 'saveDevices']);
\App\Core\Router::register('GET', 'api/reports/get', [\App\Controllers\ReportsController::class, 'getReport']);
\App\Core\Router::register('POST', 'api/reports/save', [\App\Controllers\ReportsController::class, 'saveReport']);
\App\Core\Router::register('POST', 'api/reports/delete', [\App\Controllers\ReportsController::class, 'deleteReport']);


// 设备管理 API
\App\Core\Router::register('GET', 'api/devices', [\App\Controllers\DevicesController::class, 'getDevicesList']);
\App\Core\Router::register('GET', 'devices/get', [\App\Controllers\DevicesController::class, 'getDevice']);
\App\Core\Router::register('POST', 'devices/save', [\App\Controllers\DevicesController::class, 'saveDevice']);
\App\Core\Router::register('POST', 'devices/delete', [\App\Controllers\DevicesController::class, 'deleteDevice']);
\App\Core\Router::register('POST', 'api/devices/create', [\App\Controllers\DevicesController::class, 'create']);
\App\Core\Router::register('POST', 'api/devices/update', [\App\Controllers\DevicesController::class, 'update']);
\App\Core\Router::register('POST', 'api/devices/delete', [\App\Controllers\DevicesController::class, 'delete']);
\App\Core\Router::register('GET', 'api/devices/pumps', [\App\Controllers\DevicesController::class, 'listPumps']);
\App\Core\Router::register('POST', 'api/devices/pumps/create', [\App\Controllers\DevicesController::class, 'createPump']);
\App\Core\Router::register('POST', 'api/devices/pumps/delete', [\App\Controllers\DevicesController::class, 'deletePump']);

// 模板管理 API
\App\Core\Router::register('GET', 'templates/get', [\App\Controllers\TemplatesController::class, 'getTemplate']);
\App\Core\Router::register('POST', 'templates/save', [\App\Controllers\TemplatesController::class, 'saveTemplate']);
\App\Core\Router::register('POST', 'templates/delete', [\App\Controllers\TemplatesController::class, 'deleteTemplate']);

// 静态页面管理
\App\Core\Router::register('GET', 'sys/static', [\App\Controllers\StaticController::class, 'index']);
\App\Core\Router::register('POST', 'static/generate', [\App\Controllers\StaticController::class, 'generate']);
\App\Core\Router::register('POST', 'static/generate-devices', [\App\Controllers\StaticController::class, 'generateDevices']);
\App\Core\Router::register('POST', 'static/cleanup', [\App\Controllers\StaticController::class, 'cleanup']);
\App\Core\Router::register('POST', 'static/cleanup-report', [\App\Controllers\StaticController::class, 'cleanupReport']);
\App\Core\Router::register('GET', 'static/status', [\App\Controllers\StaticController::class, 'status']);


// 静态页
\App\Core\Router::register('GET', '/health', function () { echo 'OK'; });

