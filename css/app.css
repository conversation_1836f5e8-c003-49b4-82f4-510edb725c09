/* Apple 高级风格：简洁留白、毛玻璃、柔和阴影 */
:root{
  --bg:#f5f6f7; --panel:#fff; --text:#111827; --muted:#6b7280; --border:#e5e7eb; --brand:#0ea5e9;
}
html,body{height:100%;}
body{background:var(--bg); color:var(--text);}

/* 登录页专属（Apple 高级风格） */
body.login-page{
  background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);
  margin:0;padding:0;height:100vh;overflow:hidden;
  font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;
}

.login-container{
  display:flex;height:100vh;
}

.login-left{
  flex:1;display:flex;align-items:center;justify-content:center;
  background:linear-gradient(135deg,rgba(102,126,234,0.9),rgba(118,75,162,0.9));
  position:relative;
}

.login-left::before{
  content:'';position:absolute;top:0;left:0;right:0;bottom:0;
  background:url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.login-brand-section{
  z-index:1;color:white;text-align:center;max-width:400px;padding:0 20px;
}

.brand-logo{
  display:flex;align-items:center;justify-content:center;gap:16px;margin-bottom:40px;
}

.logo-icon{
  width:64px;height:64px;border-radius:16px;
  background:linear-gradient(135deg,#60a5fa,#22d3ee);
  box-shadow:0 8px 32px rgba(0,0,0,0.3);
  position:relative;
}

.logo-icon::after{
  content:'';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);
  width:32px;height:32px;border-radius:8px;
  background:rgba(255,255,255,0.2);
}

.logo-text .system-name{
  font-size:24px;font-weight:600;margin-bottom:4px;
}

.logo-text .system-desc{
  font-size:16px;opacity:0.8;
}

.feature-list{
  display:flex;flex-direction:column;gap:16px;
}

.feature-item{
  display:flex;align-items:center;gap:12px;font-size:16px;
}

.feature-item i{
  width:20px;text-align:center;opacity:0.9;
}

.login-right{
  flex:1;background:white;display:flex;align-items:center;justify-content:center;
  position:relative;
}

.login-form-container{
  width:100%;max-width:400px;padding:0 40px;
}

.login-header{
  text-align:center;margin-bottom:32px;
}

.login-header h2{
  font-size:28px;font-weight:600;color:#1f2937;margin-bottom:8px;
}

.login-header p{
  color:#6b7280;font-size:16px;margin:0;
}

.login-alert{
  border:none;border-radius:12px;background:#fef2f2;color:#dc2626;
  padding:12px 16px;margin-bottom:24px;display:flex;align-items:center;gap:8px;
}

.form-group{
  margin-bottom:20px;
}

.form-label{
  display:block;margin-bottom:8px;font-weight:500;color:#374151;
}

.input-wrapper{
  position:relative;
}

.input-icon{
  position:absolute;left:16px;top:50%;transform:translateY(-50%);
  color:#9ca3af;z-index:1;
}

.login-form .form-control{
  width:100%;padding:16px 16px 16px 48px;border:2px solid #e5e7eb;
  border-radius:12px;font-size:16px;transition:all 0.2s ease;
  background:white;
}

.login-form .form-control:focus{
  outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px rgba(59,130,246,0.1);
}

.form-options{
  margin-bottom:24px;
}

.remember-options{
  display:flex;gap:24px;
}

.checkbox-wrapper{
  display:flex;align-items:center;gap:8px;cursor:pointer;
}

.checkbox-wrapper input[type="checkbox"]{
  display:none;
}

.checkmark{
  width:18px;height:18px;border:2px solid #d1d5db;border-radius:4px;
  position:relative;transition:all 0.2s ease;
}

.checkbox-wrapper input:checked + .checkmark{
  background:#3b82f6;border-color:#3b82f6;
}

.checkbox-wrapper input:checked + .checkmark::after{
  content:'✓';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);
  color:white;font-size:12px;font-weight:bold;
}

.checkbox-text{
  color:#6b7280;font-size:14px;
}

.btn-login{
  width:100%;padding:16px;background:linear-gradient(135deg,#3b82f6,#1d4ed8);
  border:none;border-radius:12px;color:white;font-size:16px;font-weight:600;
  cursor:pointer;transition:all 0.2s ease;display:flex;align-items:center;justify-content:center;gap:8px;
}

.btn-login:hover{
  transform:translateY(-1px);box-shadow:0 8px 25px rgba(59,130,246,0.3);
}

.btn-login:active{
  transform:translateY(0);
}

.login-footer{
  text-align:center;margin-top:32px;
}

.version-info{
  color:#9ca3af;font-size:14px;
}

@media (max-width: 768px){
  .login-container{flex-direction:column;}
  .login-left{flex:none;height:200px;}
  .login-right{flex:1;}
  .login-form-container{padding:0 20px;}
  .brand-logo{flex-direction:column;gap:12px;}
  .logo-text .system-name{font-size:20px;}
  .feature-list{display:none;}
}

/* 顶部导航 */
.apple-nav{backdrop-filter:saturate(180%) blur(20px); background:rgba(255,255,255,.85); border-bottom:1px solid var(--border);}
.brand-dot{width:10px;height:10px;border-radius:50%;background:linear-gradient(135deg,#60a5fa,#22d3ee);display:inline-block}
.btn-ghost{border:1px solid var(--border); background:#fff;}
.btn-ghost:hover{background:#f8fafc}


/* 侧栏 */
.sidebar{width:260px;position:fixed;top:56px;bottom:0;left:0;overflow:auto;border-right:1px solid var(--border);background:#fff;transition:transform .25s ease}
.sidebar.collapsed{transform:translateX(-100%)}
.sidebar-inner{padding:12px}

/* 布局响应主区宽度：默认侧栏开启时主区右移，收起时铺满 */
body.sidebar-open main, body.sidebar-open .app-footer{margin-left:260px;max-width:calc(100vw - 280px)}
body:not(.sidebar-open) main, body:not(.sidebar-open) .app-footer{margin-left:0;max-width:100vw}
main{overflow-x:auto}
.menu{list-style:none;margin:0;padding:0}
.menu .item{display:flex;align-items:center;gap:10px;padding:10px 10px;border-radius:10px;color:#111827;text-decoration:none}
.menu .item:hover{background:#f3f4f6}
.menu .item.active{background:#e6f2ff;color:#1d4ed8;font-weight:600}

/* 表单验证样式 */
.form-control.is-invalid{border-color:#dc3545;box-shadow:0 0 0 0.2rem rgba(220,53,69,.25)}
.btn-group .btn{border-radius:0.375rem}
.btn-group .btn:not(:last-child){margin-right:0.5rem}

/* 时间段禁用样式 */
.disabled-slot{background-color:#e9ecef !important;cursor:not-allowed}

/* 表格布局优化 */
.table-responsive{overflow-x:auto;max-width:100%}
.table{table-layout:fixed;width:100%;min-width:1100px;border-collapse:collapse}
.table th, .table td{white-space:nowrap;padding:0.4rem 0.2rem;font-size:0.8rem;vertical-align:middle;border-right:1px solid #dee2e6}
.table th{background-color:#f8f9fa;position:sticky;top:0;z-index:10;font-weight:600}
.table input{width:100%;box-sizing:border-box;font-size:0.8rem;padding:0.2rem 0.3rem}
.table input[type="time"]{min-width:70px}
.table input[type="number"]{min-width:60px}
.table input[type="text"]{min-width:80px}

/* 设置表格行固定高度，确保有按钮和没按钮的行高度一致 */
.table tbody tr {
  min-height: 45px;
  height: 45px;
}

/* 表格竖线分隔样式 */
.table th:last-child,
.table td:last-child {
  border-right: none; /* 最后一列不需要右边框 */
}

.table thead th {
  border-bottom: 2px solid #dee2e6; /* 表头底部加粗边框 */
}

.table tbody tr {
  border-bottom: 1px solid #dee2e6; /* 行之间的分隔线 */
}




/* 连续运行设备表格竖线样式 */
.continuous-table {
  border-collapse: collapse !important;
}

.continuous-table th,
.continuous-table td {
  border-right: 1px solid #dee2e6 !important;
}

.continuous-table th:last-child,
.continuous-table td:last-child {
  border-right: none !important; /* 最后一列不需要右边框 */
}

.continuous-table thead th {
  border-bottom: 2px solid #dee2e6 !important; /* 表头底部加粗边框 */
}

.continuous-table tbody tr {
  border-bottom: 1px solid #dee2e6 !important; /* 行之间的分隔线 */
}

.card-body{padding:1rem 0.5rem}
.menu .group{padding:12px 8px 6px;font-size:12px;color:var(--muted)}
.menu .depth-1{padding-left:26px}
.menu .depth-2{padding-left:42px;font-size:0.9rem}

.menu .collapse-item{display:none}
.menu .collapse-item:not(.hide){display:list-item}
.menu .collapse-item.hide{display:none}
.menu .collapsible .group-header{cursor:pointer;padding:8px 8px 6px;color:#6b7280;font-size:12px;user-select:none;display:block}
.menu .collapsible .group-header:hover{color:#111827}
.menu .collapsible .group-header.depth-1{padding-left:18px;font-size:1rem;color:#374151}
.menu .collapsible .group-header.depth-2{padding-left:32px;font-size:0.9rem;color:#6b7280}
.menu .item.depth-1{padding-left:18px;border-radius:8px}
.menu .item.depth-2{padding-left:32px;font-size:0.9rem;border-radius:8px}
.menu .item.depth-3{padding-left:46px;font-size:0.85rem;border-radius:6px;color:#6b7280}
.menu .item.depth-4{padding-left:60px;font-size:0.85rem;border-radius:6px;color:#9ca3af}
.menu .item.depth-3:hover,.menu .item.depth-4:hover{color:#111827;background:#f9fafb}
.menu .item{line-height:1.15}
.menu .collapsible .group-header i{transition:transform 0.2s ease}
.menu .collapsible.collapsed .group-header i{transform:rotate(-90deg)}
/* 面包屑导航样式 */
.breadcrumb{background:rgba(248,250,252,0.8);border-radius:8px;padding:8px 16px;font-size:0.9rem}
.breadcrumb-item a{color:#6b7280;text-decoration:none}
.breadcrumb-item a:hover{color:#111827}
.breadcrumb-item.active{color:#111827;font-weight:500}
.breadcrumb-item + .breadcrumb-item::before{content:"/"!important;color:#d1d5db}

/* Toggle 按钮 */
.btn-icon{display:inline-flex;flex-direction:column;gap:3px;border:1px solid var(--border);background:#fff;border-radius:10px;width:36px;height:32px;align-items:center;justify-content:center}
.btn-icon .bar{width:18px;height:2px;background:#111827;border-radius:2px}

/* 主体与页脚 */
body{min-height:100vh;display:flex;flex-direction:column}
main{padding-top:72px;flex:1}
.app-footer{border-top:1px solid var(--border);padding:12px 0;background:rgba(255,255,255,.6);backdrop-filter:saturate(180%) blur(10px);margin-top:auto}
@media (max-width: 991px){
  .sidebar{transform:translateX(-100%)}
  .sidebar.show{transform:none}
  main,.app-footer{margin-left:0;max-width:100vw}
}

/* 卡片 */
.card{border:1px solid var(--border);box-shadow:0 1px 2px rgba(0,0,0,0.03)}
/* 设备管理内联布局优化（全新样式） */
.device-pumps{margin-top:6px}
.device-pumps .add-row{max-width:360px}
.device-pumps .pump-badges{display:flex;flex-wrap:wrap;gap:6px;margin-top:8px}
/* Chip 风格的泵号标签 */
.device-pumps .chip{display:inline-flex;align-items:center;gap:6px;padding:4px 10px;border:1px solid var(--border);border-radius:999px;background:#f8fafc;color:#111827;font-size:12px;line-height:1}
.device-pumps .chip .chip-close{border:0;background:transparent;color:#a8a8a8;cursor:pointer;padding:0;width:16px;height:16px;display:inline-flex;align-items:center;justify-content:center;border-radius:50%}
.device-pumps .chip .chip-close:hover{background:#fee2e2;color:#b91c1c}
/* 更统一的 input-group 按钮视觉 */
.input-group>.btn.btn-primary{border-top-left-radius:0;border-bottom-left-radius:0}


/* 页头与面包屑 */
.page-head{padding:8px 0}
.page-title{font-size:20px;font-weight:600}
.breadcrumb{color:var(--muted)}

/* 表单/按钮主题 */
.btn{border-radius:10px}
.form-control{border-radius:10px}
.select,.form-select{border-radius:10px}

/* 动画微交互 */
.btn,.form-control,.form-select,.menu .item{transition:all .15s ease}
.btn:active{transform:translateY(1px)}
.form-control:focus,.form-select:focus{box-shadow:0 0 0 3px rgba(14,165,233,.15)}

/* 禁用时段样式 */
.disabled-slot {
  background-color: #e9ecef !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
  border-color: #ced4da !important;
}



/* 禁用输入框的灰色样式 */
input:disabled,
input.disabled-slot,
select:disabled,
textarea:disabled {
  background-color: #e9ecef !important;
  color: #6c757d !important;
  border-color: #ced4da !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
  box-shadow: none !important;
}

/* 禁用输入框的悬停和焦点状态 */
input:disabled:hover,
input:disabled:focus,
input.disabled-slot:hover,
input.disabled-slot:focus {
  background-color: #e9ecef !important;
  border-color: #ced4da !important;
  box-shadow: none !important;
}





/* 隐藏数字输入框的步进控件 */
input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox 特殊处理 */
input[type="number"] {
  -moz-appearance: textfield;
}

/* 编辑行样式 */
.editing-row {
  background-color: #fff3cd !important;
}

.editing-row td {
  background-color: #fff3cd !important;
}



