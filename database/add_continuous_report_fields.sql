-- 为连续报表添加必要的字段

-- 检查并添加 start_time 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'report_entries'
    AND COLUMN_NAME = 'start_time'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE report_entries ADD COLUMN start_time TIME NULL COMMENT ''开始时间（连续报表使用）'' AFTER entry_date',
    'SELECT ''start_time 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 end_time 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'report_entries'
    AND COLUMN_NAME = 'end_time'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE report_entries ADD COLUMN end_time TIME NULL COMMENT ''结束时间（连续报表使用）'' AFTER start_time',
    'SELECT ''end_time 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 field_data 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'report_entries'
    AND COLUMN_NAME = 'field_data'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE report_entries ADD COLUMN field_data JSON NULL COMMENT ''字段数据（连续报表使用）'' AFTER end_time',
    'SELECT ''field_data 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 updated_by 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'report_entries'
    AND COLUMN_NAME = 'updated_by'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE report_entries ADD COLUMN updated_by BIGINT NULL COMMENT ''更新人ID'' AFTER created_by',
    'SELECT ''updated_by 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 updated_at 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'report_entries'
    AND COLUMN_NAME = 'updated_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE report_entries ADD COLUMN updated_at TIMESTAMP NULL COMMENT ''更新时间'' AFTER created_at',
    'SELECT ''updated_at 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'report_entries'
AND COLUMN_NAME IN ('start_time', 'end_time', 'field_data', 'updated_by', 'updated_at')
ORDER BY ORDINAL_POSITION;
