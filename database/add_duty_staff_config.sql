-- 为 reports 表添加值班人员配置字段

-- 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'reports'
    AND COLUMN_NAME = 'duty_staff_config'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE reports ADD COLUMN duty_staff_config JSON NULL COMMENT ''值班人员配置JSON'' AFTER time_limit_days',
    'SELECT ''duty_staff_config 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'reports'
AND COLUMN_NAME = 'duty_staff_config';
