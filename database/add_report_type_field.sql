-- 为 report_templates 表添加报表类型字段

-- 检查并添加 report_type 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'report_templates'
    AND COLUMN_NAME = 'report_type'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE report_templates ADD COLUMN report_type VARCHAR(20) NOT NULL DEFAULT ''daily'' COMMENT ''报表类型：daily=日报表(固定时间段), continuous=连续报表(间歇运行)'' AFTER name',
    'SELECT ''report_type 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有模板的默认配置为日报表类型
UPDATE report_templates SET 
    report_type = 'daily'
WHERE report_type IS NULL OR report_type = '';

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'report_templates'
AND COLUMN_NAME = 'report_type'
ORDER BY ORDINAL_POSITION;

-- 显示更新后的表结构
DESCRIBE report_templates;
