-- 添加系统配置、IP访问控制和审计日志表
-- 字符集/排序请设置为 utf8mb4/utf8mb4_general_ci

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
  cfg_key VARCHAR(64) PRIMARY KEY COMMENT '配置键',
  cfg_value TEXT NOT NULL COMMENT '配置值(JSON/字符串)',
  remark VARCHAR(128) NULL COMMENT '备注',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='系统配置（含开关与限制）';

-- IP访问控制表
CREATE TABLE IF NOT EXISTS ip_acl (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  type ENUM('whitelist','blacklist') NOT NULL COMMENT '类型',
  cidr VARCHAR(64) NOT NULL COMMENT 'CIDR/IP 段',
  remark VARCHAR(128) NULL COMMENT '备注',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='IP 访问控制';

-- 审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  user_id BIGINT NULL COMMENT '操作者',
  action VARCHAR(64) NOT NULL COMMENT '动作',
  resource VARCHAR(128) NOT NULL COMMENT '资源',
  detail TEXT NULL COMMENT '详情JSON',
  ip VARCHAR(64) NULL COMMENT '来源IP',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间'
) ENGINE=InnoDB COMMENT='审计日志';

-- 插入默认系统配置
INSERT INTO system_config (cfg_key, cfg_value, remark) VALUES
('system_name', '"设备资料录入管理系统"', '系统名称'),
('system_version', '"1.0.0"', '系统版本'),
('session_ttl_days', '7', '会话超时天数'),
('time_limit_days', '2', '可录入历史天数限制'),
('show_entry_meta', 'true', '是否显示录入人信息'),
('ip_acl_mode', '"disabled"', 'IP访问控制模式: disabled/whitelist/blacklist')
ON DUPLICATE KEY UPDATE cfg_value=VALUES(cfg_value), remark=VALUES(remark);
