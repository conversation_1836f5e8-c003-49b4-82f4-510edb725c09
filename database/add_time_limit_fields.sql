-- 为 reports 表添加时间限制相关字段

-- 检查并添加 time_limit_type 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'reports'
    AND COLUMN_NAME = 'time_limit_type'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE reports ADD COLUMN time_limit_type VARCHAR(20) NOT NULL DEFAULT ''day'' COMMENT ''时间限制类型：hour/day/week/month/unlimited'' AFTER time_limit_days',
    'SELECT ''time_limit_type 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 cycle_type 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'reports'
    AND COLUMN_NAME = 'cycle_type'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE reports ADD COLUMN cycle_type VARCHAR(20) NOT NULL DEFAULT ''daily'' COMMENT ''周期类型：daily/weekly/shift/monthly/irregular'' AFTER template_code',
    'SELECT ''cycle_type 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有报表的默认配置
UPDATE reports SET 
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE time_limit_type IS NULL OR time_limit_type = '';

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'reports'
AND COLUMN_NAME IN ('time_limit_type', 'cycle_type', 'time_limit_days')
ORDER BY ORDINAL_POSITION;
