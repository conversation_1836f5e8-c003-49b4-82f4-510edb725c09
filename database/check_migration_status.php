<?php
/**
 * 检查数据库迁移状态并应用必要的更新
 * 用于确保多模板录入功能的数据库兼容性
 */

require_once __DIR__ . '/../config/db.php';

$config = require __DIR__ . '/../config/db.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "=== 数据库迁移状态检查 ===\n";
    
    // 1. 检查 reports 表是否有 template_code 字段
    echo "\n1. 检查 reports 表 template_code 字段...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM reports LIKE 'template_code'");
    if ($stmt->rowCount() > 0) {
        echo "✓ template_code 字段已存在\n";
    } else {
        echo "✗ template_code 字段不存在，正在添加...\n";
        $pdo->exec("ALTER TABLE reports ADD COLUMN template_code VARCHAR(50) NOT NULL DEFAULT 'pumps' COMMENT '录入模板类型：pumps=泵类, compressor=空压机, fan=风机, motor=电机, custom=自定义' AFTER category_id");
        echo "✓ template_code 字段添加成功\n";
    }
    
    // 2. 检查 report_entries 表是否有 object_id 字段
    echo "\n2. 检查 report_entries 表 object_id 字段...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM report_entries LIKE 'object_id'");
    if ($stmt->rowCount() > 0) {
        echo "✓ object_id 字段已存在\n";
    } else {
        echo "✗ object_id 字段不存在，正在添加...\n";
        $pdo->exec("ALTER TABLE report_entries ADD COLUMN object_id INT NULL COMMENT '关联对象ID：泵类时为pump.id，空压机时为device.id' AFTER pump_id");
        echo "✓ object_id 字段添加成功\n";
        
        // 迁移现有数据
        echo "正在迁移现有 pump_id 数据到 object_id...\n";
        $result = $pdo->exec("UPDATE report_entries SET object_id = pump_id WHERE pump_id IS NOT NULL");
        echo "✓ 迁移了 {$result} 条记录\n";
    }
    
    // 3. 检查 report_devices 表是否有 object_id 字段
    echo "\n3. 检查 report_devices 表 object_id 字段...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM report_devices LIKE 'object_id'");
    if ($stmt->rowCount() > 0) {
        echo "✓ object_id 字段已存在\n";
    } else {
        echo "✗ object_id 字段不存在，正在添加...\n";
        $pdo->exec("ALTER TABLE report_devices ADD COLUMN object_id INT NULL COMMENT '关联对象ID：泵类时为pump.id，空压机等时为device.id' AFTER pump_id");
        echo "✓ object_id 字段添加成功\n";
        
        // 迁移现有数据
        echo "正在迁移现有 pump_id 数据到 object_id...\n";
        $result = $pdo->exec("UPDATE report_devices SET object_id = pump_id WHERE pump_id IS NOT NULL");
        echo "✓ 迁移了 {$result} 条记录\n";
    }
    
    // 4. 检查并添加索引
    echo "\n4. 检查数据库索引...\n";
    $stmt = $pdo->query("SHOW INDEX FROM report_devices WHERE Key_name = 'idx_report_device_object'");
    if ($stmt->rowCount() > 0) {
        echo "✓ idx_report_device_object 索引已存在\n";
    } else {
        echo "✗ idx_report_device_object 索引不存在，正在添加...\n";
        $pdo->exec("ALTER TABLE report_devices ADD INDEX idx_report_device_object (report_id, device_id, object_id)");
        echo "✓ 索引添加成功\n";
    }
    
    // 5. 检查并创建 report_templates 表
    echo "\n5. 检查 report_templates 表...\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'report_templates'");
    if ($stmt->rowCount() > 0) {
        echo "✓ report_templates 表已存在\n";
    } else {
        echo "✗ report_templates 表不存在，正在创建...\n";
        $pdo->exec("
            CREATE TABLE report_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板代码',
                name VARCHAR(100) NOT NULL COMMENT '模板名称',
                fields_config JSON NOT NULL COMMENT '字段配置JSON',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) COMMENT '报表录入模板配置'
        ");
        echo "✓ report_templates 表创建成功\n";
        
        // 插入预定义模板
        echo "正在插入预定义模板...\n";
        $templates = [
            [
                'code' => 'pumps',
                'name' => '泵类设备',
                'fields_config' => json_encode([
                    'fields' => [
                        ['key' => 'start_time', 'label' => '开泵时间', 'type' => 'time'],
                        ['key' => 'stop_time', 'label' => '停泵时间', 'type' => 'time'],
                        ['key' => 'voltage_v', 'label' => '电压(V)', 'type' => 'number', 'step' => '0.1'],
                        ['key' => 'current_a', 'label' => '电流(A)', 'type' => 'number', 'step' => '0.1'],
                        ['key' => 'pump_pressure_mpa', 'label' => '泵压(MPa)', 'type' => 'number', 'step' => '0.01'],
                        ['key' => 'dry_pressure_mpa', 'label' => '干压(MPa)', 'type' => 'number', 'step' => '0.01'],
                        ['key' => 'flow_m3h', 'label' => '排量(m³/h)', 'type' => 'number'],
                        ['key' => 'bt_pump_front', 'label' => '泵前温(℃)', 'type' => 'number'],
                        ['key' => 'bt_pump_rear', 'label' => '泵后温(℃)', 'type' => 'number'],
                        ['key' => 'bt_motor_front', 'label' => '机前温(℃)', 'type' => 'number'],
                        ['key' => 'bt_motor_rear', 'label' => '机后温(℃)', 'type' => 'number'],
                        ['key' => 'maint_p', 'label' => '一保(h)', 'type' => 'number'],
                        ['key' => 'maint_s', 'label' => '二保(h)', 'type' => 'number'],
                        ['key' => 'total_hours', 'label' => '总时(h)', 'type' => 'number'],
                        ['key' => 'remark', 'label' => '备注', 'type' => 'text']
                    ]
                ])
            ],
            [
                'code' => 'compressor',
                'name' => '空压机设备',
                'fields_config' => json_encode([
                    'fields' => [
                        ['key' => 'start_time', 'label' => '开机时间', 'type' => 'time'],
                        ['key' => 'stop_time', 'label' => '停机时间', 'type' => 'time'],
                        ['key' => 'voltage_v', 'label' => '电压(V)', 'type' => 'number'],
                        ['key' => 'current_a', 'label' => '电流(A)', 'type' => 'number'],
                        ['key' => 'discharge_pressure', 'label' => '排气压力(MPa)', 'type' => 'number', 'step' => '0.01'],
                        ['key' => 'inlet_temp', 'label' => '进气温度(℃)', 'type' => 'number'],
                        ['key' => 'discharge_temp', 'label' => '排气温度(℃)', 'type' => 'number'],
                        ['key' => 'oil_temp', 'label' => '油温(℃)', 'type' => 'number'],
                        ['key' => 'running_hours', 'label' => '运行时间(h)', 'type' => 'number', 'step' => '0.1'],
                        ['key' => 'remark', 'label' => '备注', 'type' => 'text']
                    ]
                ])
            ],
            [
                'code' => 'fan',
                'name' => '风机设备',
                'fields_config' => json_encode([
                    'fields' => [
                        ['key' => 'start_time', 'label' => '开机时间', 'type' => 'time'],
                        ['key' => 'stop_time', 'label' => '停机时间', 'type' => 'time'],
                        ['key' => 'voltage_v', 'label' => '电压(V)', 'type' => 'number'],
                        ['key' => 'current_a', 'label' => '电流(A)', 'type' => 'number'],
                        ['key' => 'speed_rpm', 'label' => '转速(RPM)', 'type' => 'number'],
                        ['key' => 'vibration', 'label' => '振动', 'type' => 'number', 'step' => '0.01'],
                        ['key' => 'bearing_temp', 'label' => '轴承温度(℃)', 'type' => 'number'],
                        ['key' => 'running_hours', 'label' => '运行时间(h)', 'type' => 'number', 'step' => '0.1'],
                        ['key' => 'remark', 'label' => '备注', 'type' => 'text']
                    ]
                ])
            ],
            [
                'code' => 'motor',
                'name' => '电机设备',
                'fields_config' => json_encode([
                    'fields' => [
                        ['key' => 'start_time', 'label' => '开机时间', 'type' => 'time'],
                        ['key' => 'stop_time', 'label' => '停机时间', 'type' => 'time'],
                        ['key' => 'voltage_v', 'label' => '电压(V)', 'type' => 'number'],
                        ['key' => 'current_a', 'label' => '电流(A)', 'type' => 'number'],
                        ['key' => 'speed_rpm', 'label' => '转速(RPM)', 'type' => 'number'],
                        ['key' => 'power_kw', 'label' => '功率(kW)', 'type' => 'number', 'step' => '0.1'],
                        ['key' => 'temperature', 'label' => '温度(℃)', 'type' => 'number'],
                        ['key' => 'running_hours', 'label' => '运行时间(h)', 'type' => 'number', 'step' => '0.1'],
                        ['key' => 'remark', 'label' => '备注', 'type' => 'text']
                    ]
                ])
            ]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO report_templates (code, name, fields_config) VALUES (?, ?, ?)");
        foreach ($templates as $template) {
            $stmt->execute([$template['code'], $template['name'], $template['fields_config']]);
        }
        echo "✓ 预定义模板插入成功\n";
    }
    
    // 6. 添加一些示例设备（如果不存在）
    echo "\n6. 检查示例设备数据...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM devices WHERE type IN ('compressor', 'fan', 'motor')");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "✗ 缺少非泵类设备，正在添加示例设备...\n";
        $devices = [
            ['type' => 'compressor', 'name' => '1#空压机', 'code' => 'COMP_001'],
            ['type' => 'compressor', 'name' => '2#空压机', 'code' => 'COMP_002'],
            ['type' => 'fan', 'name' => '1#风机', 'code' => 'FAN_001'],
            ['type' => 'fan', 'name' => '2#风机', 'code' => 'FAN_002'],
            ['type' => 'motor', 'name' => '1#电机', 'code' => 'MOTOR_001'],
            ['type' => 'motor', 'name' => '2#电机', 'code' => 'MOTOR_002']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO devices (type, name, code) VALUES (?, ?, ?)");
        foreach ($devices as $device) {
            $stmt->execute([$device['type'], $device['name'], $device['code']]);
        }
        echo "✓ 示例设备添加成功\n";
    } else {
        echo "✓ 非泵类设备已存在 ({$count} 个)\n";
    }
    
    // 7. 验证数据完整性
    echo "\n7. 验证数据完整性...\n";
    
    // 检查 report_entries 表的数据迁移
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM report_entries");
    $total = $stmt->fetchColumn();
    $stmt = $pdo->query("SELECT COUNT(*) as migrated FROM report_entries WHERE object_id IS NOT NULL");
    $migrated = $stmt->fetchColumn();
    echo "report_entries: 总记录 {$total}, 已迁移 {$migrated}\n";
    
    // 检查 report_devices 表的数据迁移
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM report_devices");
    $total = $stmt->fetchColumn();
    $stmt = $pdo->query("SELECT COUNT(*) as migrated FROM report_devices WHERE object_id IS NOT NULL");
    $migrated = $stmt->fetchColumn();
    echo "report_devices: 总记录 {$total}, 已迁移 {$migrated}\n";
    
    echo "\n=== 迁移检查完成 ===\n";
    echo "✓ 数据库已准备好支持多模板录入功能\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
