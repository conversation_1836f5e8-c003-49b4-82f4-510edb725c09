<?php
/**
 * 创建示例报表配置，演示多模板录入功能
 */

require_once __DIR__ . '/../config/db.php';

$config = require __DIR__ . '/../config/db.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "=== 创建示例报表配置 ===\n";
    
    // 1. 创建空压机报表
    echo "\n1. 创建空压机报表...\n";
    $stmt = $pdo->prepare("INSERT IGNORE INTO reports (id, category_id, name, code, template_code, cycle_type, fields_json, time_limit_type, time_limit_days, enabled, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $compressorReport = [
        'id' => 2001,
        'category_id' => 1, // 现场设备
        'name' => '空压机运行记录',
        'code' => 'compressor_operation',
        'template_code' => 'compressor',
        'cycle_type' => 'daily',
        'fields_json' => '[]',
        'time_limit_type' => 'day',
        'time_limit_days' => 2,
        'enabled' => 1,
        'created_by' => 1
    ];
    
    $stmt->execute(array_values($compressorReport));
    echo "✓ 空压机报表创建成功 (ID: 2001)\n";
    
    // 2. 创建风机报表
    echo "\n2. 创建风机报表...\n";
    $fanReport = [
        'id' => 2002,
        'category_id' => 1,
        'name' => '风机运行记录',
        'code' => 'fan_operation',
        'template_code' => 'fan',
        'cycle_type' => 'daily',
        'fields_json' => '[]',
        'time_limit_type' => 'day',
        'time_limit_days' => 2,
        'enabled' => 1,
        'created_by' => 1
    ];
    
    $stmt->execute(array_values($fanReport));
    echo "✓ 风机报表创建成功 (ID: 2002)\n";
    
    // 3. 创建电机报表
    echo "\n3. 创建电机报表...\n";
    $motorReport = [
        'id' => 2003,
        'category_id' => 1,
        'name' => '电机运行记录',
        'code' => 'motor_operation',
        'template_code' => 'motor',
        'cycle_type' => 'daily',
        'fields_json' => '[]',
        'time_limit_type' => 'day',
        'time_limit_days' => 2,
        'enabled' => 1,
        'created_by' => 1
    ];
    
    $stmt->execute(array_values($motorReport));
    echo "✓ 电机报表创建成功 (ID: 2003)\n";
    
    // 4. 创建自定义模板报表
    echo "\n4. 创建自定义模板报表...\n";
    $customReport = [
        'id' => 2004,
        'category_id' => 1,
        'name' => '通用设备记录',
        'code' => 'generic_equipment',
        'template_code' => 'custom',
        'cycle_type' => 'daily',
        'fields_json' => '[]',
        'time_limit_type' => 'day',
        'time_limit_days' => 2,
        'enabled' => 1,
        'created_by' => 1
    ];
    
    $stmt->execute(array_values($customReport));
    echo "✓ 自定义模板报表创建成功 (ID: 2004)\n";
    
    // 5. 为报表配置设备关联
    echo "\n5. 配置报表设备关联...\n";
    
    // 获取设备列表
    $devices = $pdo->query("SELECT id, type, name FROM devices WHERE type IN ('compressor', 'fan', 'motor') ORDER BY type, id")->fetchAll();
    
    if (empty($devices)) {
        echo "✗ 没有找到非泵类设备，请先运行迁移脚本\n";
        exit(1);
    }
    
    $deviceStmt = $pdo->prepare("INSERT IGNORE INTO report_devices (report_id, device_id, object_id) VALUES (?, ?, ?)");
    
    // 空压机报表关联空压机设备
    foreach ($devices as $device) {
        if ($device['type'] === 'compressor') {
            $deviceStmt->execute([2001, $device['id'], $device['id']]);
            echo "✓ 空压机报表关联设备: {$device['name']}\n";
        }
    }
    
    // 风机报表关联风机设备
    foreach ($devices as $device) {
        if ($device['type'] === 'fan') {
            $deviceStmt->execute([2002, $device['id'], $device['id']]);
            echo "✓ 风机报表关联设备: {$device['name']}\n";
        }
    }
    
    // 电机报表关联电机设备
    foreach ($devices as $device) {
        if ($device['type'] === 'motor') {
            $deviceStmt->execute([2003, $device['id'], $device['id']]);
            echo "✓ 电机报表关联设备: {$device['name']}\n";
        }
    }
    
    // 自定义报表关联所有非泵类设备
    foreach ($devices as $device) {
        $deviceStmt->execute([2004, $device['id'], $device['id']]);
        echo "✓ 通用设备报表关联设备: {$device['name']}\n";
    }
    
    // 6. 验证创建结果
    echo "\n6. 验证创建结果...\n";
    
    $reports = $pdo->query("SELECT id, name, template_code, enabled FROM reports WHERE id >= 2001 ORDER BY id")->fetchAll();
    echo "创建的报表:\n";
    foreach ($reports as $report) {
        $status = $report['enabled'] ? '启用' : '禁用';
        echo "  - ID: {$report['id']}, 名称: {$report['name']}, 模板: {$report['template_code']}, 状态: {$status}\n";
    }
    
    $associations = $pdo->query("
        SELECT r.name as report_name, d.name as device_name, rd.report_id, rd.device_id 
        FROM report_devices rd 
        JOIN reports r ON rd.report_id = r.id 
        JOIN devices d ON rd.device_id = d.id 
        WHERE rd.report_id >= 2001 
        ORDER BY rd.report_id, d.name
    ")->fetchAll();
    
    echo "\n报表设备关联:\n";
    foreach ($associations as $assoc) {
        echo "  - {$assoc['report_name']} -> {$assoc['device_name']}\n";
    }
    
    echo "\n=== 示例报表配置创建完成 ===\n";
    echo "✓ 现在可以通过以下链接测试多模板录入功能:\n";
    echo "  - 空压机报表: /index.php?r=entry/report&id=2001\n";
    echo "  - 风机报表: /index.php?r=entry/report&id=2002\n";
    echo "  - 电机报表: /index.php?r=entry/report&id=2003\n";
    echo "  - 通用设备报表: /index.php?r=entry/report&id=2004\n";
    echo "  - 原有泵类报表: /index.php?r=entry/report&id=1001\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
