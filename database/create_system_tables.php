<?php
/**
 * 直接创建系统配置相关表
 */

declare(strict_types=1);

// 引入数据库配置
$config = require __DIR__ . '/../config/db.php';

try {
    $dsn = sprintf('mysql:host=%s;port=%d;dbname=%s;charset=%s', 
        $config['host'], $config['port'], $config['dbname'], $config['charset']);
    
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "连接数据库成功\n";
    
    // 创建系统配置表
    echo "创建系统配置表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_config (
          cfg_key VARCHAR(64) PRIMARY KEY COMMENT '配置键',
          cfg_value TEXT NOT NULL COMMENT '配置值(JSON/字符串)',
          remark VARCHAR(128) NULL COMMENT '备注',
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=InnoDB COMMENT='系统配置（含开关与限制）'
    ");
    echo "✓ 系统配置表创建成功\n";
    
    // 创建IP访问控制表
    echo "创建IP访问控制表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS ip_acl (
          id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
          type ENUM('whitelist','blacklist') NOT NULL COMMENT '类型',
          cidr VARCHAR(64) NOT NULL COMMENT 'CIDR/IP 段',
          remark VARCHAR(128) NULL COMMENT '备注',
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
        ) ENGINE=InnoDB COMMENT='IP 访问控制'
    ");
    echo "✓ IP访问控制表创建成功\n";
    
    // 创建审计日志表
    echo "创建审计日志表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS audit_logs (
          id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
          user_id BIGINT NULL COMMENT '操作者',
          action VARCHAR(64) NOT NULL COMMENT '动作',
          resource VARCHAR(128) NOT NULL COMMENT '资源',
          detail TEXT NULL COMMENT '详情JSON',
          ip VARCHAR(64) NULL COMMENT '来源IP',
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间'
        ) ENGINE=InnoDB COMMENT='审计日志'
    ");
    echo "✓ 审计日志表创建成功\n";
    
    // 插入默认系统配置
    echo "插入默认系统配置...\n";
    $pdo->exec("
        INSERT INTO system_config (cfg_key, cfg_value, remark) VALUES
        ('system_name', '\"设备资料录入管理系统\"', '系统名称'),
        ('system_version', '\"1.0.0\"', '系统版本'),
        ('session_ttl_days', '7', '会话超时天数'),
        ('time_limit_days', '2', '可录入历史天数限制'),
        ('show_entry_meta', 'true', '是否显示录入人信息'),
        ('ip_acl_mode', '\"disabled\"', 'IP访问控制模式: disabled/whitelist/blacklist')
        ON DUPLICATE KEY UPDATE cfg_value=VALUES(cfg_value), remark=VALUES(remark)
    ");
    echo "✓ 默认配置插入成功\n";
    
    // 验证表是否创建成功
    echo "\n验证表结构...\n";
    $tables = ['system_config', 'ip_acl', 'audit_logs'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "✓ 表 {$table} 存在\n";
        } else {
            echo "✗ 表 {$table} 不存在\n";
        }
    }
    
    // 检查配置数据
    echo "\n检查系统配置...\n";
    $stmt = $pdo->query("SELECT cfg_key, cfg_value, remark FROM system_config ORDER BY cfg_key");
    $configs = $stmt->fetchAll();
    
    echo "系统配置项数量: " . count($configs) . "\n";
    echo str_repeat('-', 60) . "\n";
    printf("%-20s %-20s %s\n", "配置键", "配置值", "备注");
    echo str_repeat('-', 60) . "\n";
    
    foreach ($configs as $config) {
        printf("%-20s %-20s %s\n", 
            $config['cfg_key'], 
            substr($config['cfg_value'], 0, 20), 
            $config['remark']
        );
    }
    
    echo "\n系统表初始化完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
