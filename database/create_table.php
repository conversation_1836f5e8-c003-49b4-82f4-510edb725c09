<?php
// 创建缺失的表
require_once __DIR__ . '/../config/db.php';

$config = require __DIR__ . '/../config/db.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "连接数据库成功\n";
    
    // 创建 report_devices 表
    $sql = "CREATE TABLE IF NOT EXISTS report_devices (
      id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
      report_id BIGINT NOT NULL COMMENT '报表ID',
      device_id BIGINT NOT NULL COMMENT '设备ID',
      pump_id BIGINT NULL COMMENT '泵ID（可选，为空表示整个设备）',
      created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      UNIQUE KEY uk_report_device_pump (report_id, device_id, pump_id)
    ) ENGINE=InnoDB COMMENT='报表-设备关联'";
    
    $pdo->exec($sql);
    echo "创建 report_devices 表成功\n";
    
    // 清理旧数据
    $pdo->exec("DELETE FROM pumps");
    $pdo->exec("DELETE FROM devices WHERE type='pump'");
    echo "清理旧数据成功\n";
    
    // 插入调水泵设备
    $pdo->exec("INSERT INTO devices (id, type, name, code) VALUES (1, 'pump', '调水泵', 'PUMP_SITE')");
    echo "插入设备成功\n";
    
    // 插入泵列表
    $pdo->exec("INSERT INTO pumps (id, device_id, pump_no) VALUES (1, 1, '1#'), (2, 1, '2#'), (3, 1, '3#'), (4, 1, '4#')");
    echo "插入泵列表成功\n";
    
    // 插入报表设备关联
    $pdo->exec("INSERT IGNORE INTO report_devices (report_id, device_id, pump_id) VALUES (1001, 1, 1), (1001, 1, 2), (1001, 1, 3), (1001, 1, 4)");
    echo "插入报表设备关联成功\n";
    
    // 更新报表分类
    $pdo->exec("UPDATE reports SET category_id=1 WHERE id=1001");
    echo "更新报表分类成功\n";
    
    echo "\n所有操作完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
