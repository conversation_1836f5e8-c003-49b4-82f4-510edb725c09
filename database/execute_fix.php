<?php
// 数据库修复脚本
require_once __DIR__ . '/../config/db.php';

$config = require __DIR__ . '/../config/db.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "连接数据库成功\n";
    
    // 读取并执行 SQL 文件
    $sql = file_get_contents(__DIR__ . '/fix_tables.sql');
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            echo "执行成功: " . substr($statement, 0, 50) . "...\n";
        } catch (Exception $e) {
            echo "执行失败: " . $e->getMessage() . "\n";
            echo "SQL: " . $statement . "\n";
        }
    }
    
    echo "\n数据库修复完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
