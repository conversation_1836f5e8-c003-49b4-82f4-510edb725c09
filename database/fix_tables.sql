-- 修复数据库表和数据

-- 先创建报表设备关联表
CREATE TABLE IF NOT EXISTS report_devices (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  report_id BIGINT NOT NULL COMMENT '报表ID',
  device_id BIGINT NOT NULL COMMENT '设备ID',
  pump_id BIGINT NULL COMMENT '泵ID（可选，为空表示整个设备）',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY uk_report_device_pump (report_id, device_id, pump_id)
) ENGINE=InnoDB COMMENT='报表-设备关联';

-- 清理旧数据
DELETE FROM pumps;
DELETE FROM devices WHERE type='pump';

-- 插入调水泵设备（现场设备）
INSERT INTO devices (id, type, name, code) VALUES 
(1, 'pump', '调水泵', 'PUMP_SITE');

-- 插入泵列表（4个泵号：1#、2#、3#、4#）
INSERT INTO pumps (id, device_id, pump_no) VALUES 
(1, 1, '1#'),
(2, 1, '2#'),
(3, 1, '3#'),
(4, 1, '4#');

-- 为平台泵类报表配置默认设备关联
INSERT IGNORE INTO report_devices (report_id, device_id, pump_id) VALUES 
(1001, 1, 1),
(1001, 1, 2),
(1001, 1, 3),
(1001, 1, 4);

-- 更新报表分类为现场设备
UPDATE reports SET category_id=1 WHERE id=1001;

-- 查询验证
SELECT '=== 设备列表 ===' as info;
SELECT id, name, code FROM devices WHERE type='pump' ORDER BY id;
SELECT '=== 泵列表 ===' as info;
SELECT p.id, d.name as device_name, p.pump_no FROM pumps p JOIN devices d ON p.device_id=d.id ORDER BY d.id, p.pump_no;
SELECT '=== 报表设备关联 ===' as info;
SELECT rd.report_id, d.name as device_name, p.pump_no FROM report_devices rd JOIN devices d ON rd.device_id=d.id LEFT JOIN pumps p ON rd.pump_id=p.id WHERE rd.report_id=1001 ORDER BY d.name, p.pump_no;
