<?php
/**
 * 初始化系统配置相关表结构
 * 执行此脚本来创建system_config、ip_acl、audit_logs表
 */

declare(strict_types=1);

// 引入数据库配置
require_once __DIR__ . '/../config/db.php';

try {
    $config = require __DIR__ . '/../config/db.php';
    $dsn = sprintf('mysql:host=%s;port=%d;dbname=%s;charset=%s', 
        $config['host'], $config['port'], $config['dbname'], $config['charset']);
    
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "连接数据库成功\n";
    
    // 读取并执行SQL文件
    $sqlFile = __DIR__ . '/add_system_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL文件");
    }
    
    // 分割SQL语句并执行
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            echo "执行成功: " . substr($statement, 0, 50) . "...\n";
        } catch (PDOException $e) {
            // 如果是表已存在的错误，忽略
            if (strpos($e->getMessage(), 'already exists') !== false) {
                echo "表已存在，跳过: " . substr($statement, 0, 50) . "...\n";
            } else {
                throw $e;
            }
        }
    }
    
    echo "\n系统表初始化完成！\n";
    
    // 验证表是否创建成功
    $tables = ['system_config', 'ip_acl', 'audit_logs'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "✓ 表 {$table} 创建成功\n";
        } else {
            echo "✗ 表 {$table} 创建失败\n";
        }
    }
    
    // 检查默认配置是否插入成功
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_config");
    $count = $stmt->fetch()['count'];
    echo "\n系统配置项数量: {$count}\n";
    
    if ($count > 0) {
        echo "默认配置插入成功\n";
        
        // 显示当前配置
        $stmt = $pdo->query("SELECT cfg_key, cfg_value, remark FROM system_config ORDER BY cfg_key");
        echo "\n当前系统配置:\n";
        echo str_repeat('-', 60) . "\n";
        printf("%-20s %-20s %s\n", "配置键", "配置值", "备注");
        echo str_repeat('-', 60) . "\n";
        
        while ($row = $stmt->fetch()) {
            printf("%-20s %-20s %s\n", 
                $row['cfg_key'], 
                substr($row['cfg_value'], 0, 20), 
                $row['remark']
            );
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
