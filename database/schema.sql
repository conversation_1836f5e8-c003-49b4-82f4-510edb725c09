-- 初始化数据库结构（节选，含角色/权限/菜单/报表定义）
-- 字符集/排序请设置为 utf8mb4/utf8mb4_general_ci

-- 使用当前连接的数据库（由安装脚本选择）

-- 核心表（与文档一致，省略部分约束以便快速初始化）
CREATE TABLE IF NOT EXISTS users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  username VARCHAR(64) NOT NULL UNIQUE COMMENT '登录账号',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  real_name VARCHAR(64) NOT NULL COMMENT '姓名',
  email VARCHAR(128) NULL COMMENT '邮箱',
  mobile VARCHAR(20) NULL COMMENT '手机号',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1启用 0禁用',
  last_login_at DATETIME NULL COMMENT '上次登录时间',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='用户表';

CREATE TABLE IF NOT EXISTS roles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  code VARCHAR(32) NOT NULL UNIQUE COMMENT '角色编码: admin/mod/user/site/ctrl/cb26',
  name VARCHAR(64) NOT NULL COMMENT '角色名称',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='角色表';

CREATE TABLE IF NOT EXISTS permissions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  code VARCHAR(64) NOT NULL UNIQUE COMMENT '权限编码',
  name VARCHAR(128) NOT NULL COMMENT '权限名称',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='权限表';

CREATE TABLE IF NOT EXISTS user_role (
  user_id BIGINT NOT NULL COMMENT '用户ID',
  role_id BIGINT NOT NULL COMMENT '角色ID',
  PRIMARY KEY(user_id, role_id)
) ENGINE=InnoDB COMMENT='用户-角色关联';

CREATE TABLE IF NOT EXISTS role_permission (
  role_id BIGINT NOT NULL COMMENT '角色ID',
  permission_id BIGINT NOT NULL COMMENT '权限ID',
  PRIMARY KEY(role_id, permission_id)
) ENGINE=InnoDB COMMENT='角色-权限关联';

CREATE TABLE IF NOT EXISTS menus (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  parent_id BIGINT NULL COMMENT '父级ID',
  level TINYINT NOT NULL DEFAULT 1 COMMENT '层级 1/2/3',
  title VARCHAR(64) NOT NULL COMMENT '菜单标题',
  key_code VARCHAR(64) NOT NULL UNIQUE COMMENT '菜单唯一编码',
  icon VARCHAR(32) NULL COMMENT '图标class',
  route VARCHAR(128) NULL COMMENT '路由',
  sort INT NOT NULL DEFAULT 0 COMMENT '排序',
  visible TINYINT NOT NULL DEFAULT 1 COMMENT '是否可见'
) ENGINE=InnoDB COMMENT='系统菜单';

CREATE TABLE IF NOT EXISTS role_menu (
  role_id BIGINT NOT NULL COMMENT '角色ID',
  menu_id BIGINT NOT NULL COMMENT '菜单ID',
  PRIMARY KEY(role_id, menu_id)
) ENGINE=InnoDB COMMENT='角色-菜单可见性';

CREATE TABLE IF NOT EXISTS report_categories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  scope ENUM('site','cb26','control') NOT NULL COMMENT '类别范围',
  name VARCHAR(64) NOT NULL COMMENT '分类名称',
  sort INT NOT NULL DEFAULT 0 COMMENT '排序'
) ENGINE=InnoDB COMMENT='报表分类';

CREATE TABLE IF NOT EXISTS reports (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  name VARCHAR(128) NOT NULL COMMENT '报表名称',
  code VARCHAR(64) NOT NULL UNIQUE COMMENT '报表编码',
  fields_json JSON NOT NULL COMMENT '录入字段定义(JSON)',
  time_limit_days INT NOT NULL DEFAULT 2 COMMENT '可录入天数限制',
  enabled TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
  created_by BIGINT NOT NULL COMMENT '创建人ID',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='报表定义';

CREATE TABLE IF NOT EXISTS devices (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  type VARCHAR(32) NOT NULL COMMENT '设备类型',
  name VARCHAR(128) NOT NULL COMMENT '设备名称',
  code VARCHAR(64) NOT NULL UNIQUE COMMENT '设备编码',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='设备';

CREATE TABLE IF NOT EXISTS pumps (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  device_id BIGINT NOT NULL COMMENT '设备ID',
  pump_no VARCHAR(32) NOT NULL COMMENT '泵号',
  UNIQUE KEY uk_device_pump (device_id, pump_no)
) ENGINE=InnoDB COMMENT='泵列表';

CREATE TABLE IF NOT EXISTS report_entries (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  report_id BIGINT NOT NULL COMMENT '报表ID',
  device_id BIGINT NULL COMMENT '设备ID',
  pump_id BIGINT NULL COMMENT '泵ID',
  entry_date DATE NOT NULL COMMENT '自然日',
  time_slot_start TIME NOT NULL COMMENT '开始时间段',
  time_slot_end TIME NOT NULL COMMENT '结束时间段',
  data_json JSON NOT NULL COMMENT '录入数据(JSON)',
  created_by BIGINT NOT NULL COMMENT '录入人',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间',
  updated_by BIGINT NULL COMMENT '修改人',
  updated_at DATETIME NULL COMMENT '修改时间',
  late_modified TINYINT NOT NULL DEFAULT 0 COMMENT '是否超时修改'
) ENGINE=InnoDB COMMENT='报表录入数据';

CREATE TABLE IF NOT EXISTS report_devices (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  report_id BIGINT NOT NULL COMMENT '报表ID',
  device_id BIGINT NOT NULL COMMENT '设备ID',
  pump_id BIGINT NULL COMMENT '泵ID（可选，为空表示整个设备）',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY uk_report_device_pump (report_id, device_id, pump_id)
) ENGINE=InnoDB COMMENT='报表-设备关联';

