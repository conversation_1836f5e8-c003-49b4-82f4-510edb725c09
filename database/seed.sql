-- 菜单/权限/角色/用户 种子数据（可根据需要调整）
-- 使用当前连接的数据库

-- 角色
INSERT INTO roles(code,name) VALUES
  ('admin','管理员'),('mod','普通管理员'),('user','普通用户'),
  ('site','现场角色'),('ctrl','中控角色'),('cb26','CB26角色')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 权限（示例）
INSERT INTO permissions(code,name) VALUES
  ('menus.view','菜单查看'),
  ('users.manage','用户管理'),
  ('reports.manage','报表配置'),
  ('entries.create','报表录入'),
  ('entries.query','报表查询'),
  ('entries.export','导出Excel')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 管理员与权限绑定（示例）
INSERT INTO user_role(user_id,role_id)
SELECT 1, r.id FROM roles r WHERE r.code IN ('admin');

INSERT INTO role_permission(role_id,permission_id)
SELECT r.id,p.id FROM roles r JOIN permissions p ON 1=1
WHERE r.code IN ('admin');

-- 菜单（一级）
INSERT INTO menus(parent_id,level,title,key_code,icon,route,sort,visible) VALUES
  (NULL,1,'系统设置','menu.sys','fa-gear','/sys',10,1),
  (NULL,1,'现场资料录入','menu.entry.site','fa-table','/entry/site',20,1),
  (NULL,1,'CB26资料录入','menu.entry.cb26','fa-table','/entry/cb26',30,1),
  (NULL,1,'中控室资料录入','menu.entry.ctrl','fa-table','/entry/ctrl',40,1),
  (NULL,1,'现场资料查询','menu.query.site','fa-search','/query/site',50,1),
  (NULL,1,'CB26资料查询','menu.query.cb26','fa-search','/query/cb26',60,1),
  (NULL,1,'中控室资料查询','menu.query.ctrl','fa-search','/query/ctrl',70,1)
ON DUPLICATE KEY UPDATE title=VALUES(title),route=VALUES(route);

-- 系统设置二级
INSERT INTO menus(parent_id,level,title,key_code,icon,route,sort,visible)
SELECT m.id,2,'系统配置','menu.sys.config','fa-sliders','/sys/config',1,1 FROM menus m WHERE m.key_code='menu.sys'
ON DUPLICATE KEY UPDATE title=VALUES(title),route=VALUES(route);
INSERT INTO menus(parent_id,level,title,key_code,icon,route,sort,visible)
SELECT m.id,2,'用户管理','menu.sys.users','fa-users','/sys/users',2,1 FROM menus m WHERE m.key_code='menu.sys'
ON DUPLICATE KEY UPDATE title=VALUES(title),route=VALUES(route);
INSERT INTO menus(parent_id,level,title,key_code,icon,route,sort,visible)
SELECT m.id,2,'报表配置','menu.sys.reports','fa-sitemap','/sys/reports',3,1 FROM menus m WHERE m.key_code='menu.sys'
ON DUPLICATE KEY UPDATE title=VALUES(title),route=VALUES(route);

-- 将菜单授权给管理员角色
INSERT INTO role_menu(role_id,menu_id)
SELECT r.id, m.id FROM roles r CROSS JOIN menus m WHERE r.code='admin'
ON DUPLICATE KEY UPDATE menu_id=menu_id;

