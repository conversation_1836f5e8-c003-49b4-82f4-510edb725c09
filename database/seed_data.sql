-- 基础种子数据：角色、设备、泵、报表定义
-- 请在导入 schema.sql 后执行此文件

-- 插入基础角色
INSERT IGNORE INTO roles (id, code, name) VALUES 
(1, 'admin', '系统管理员'),
(2, 'mod', '主管'),
(3, 'user', '普通用户'),
(4, 'site', '现场操作员'),
(5, 'cb26', 'CB26操作员'),
(6, 'ctrl', '中控室操作员');

-- 插入基础设备
INSERT IGNORE INTO devices (id, type, name, code) VALUES
(1, 'pump', '1#调水泵', 'PUMP_001'),
(2, 'pump', '2#调水泵', 'PUMP_002'),
(3, 'pump', '3#调水泵', 'PUMP_003'),
(4, 'pump', '4#调水泵', 'PUMP_004'),
(5, 'compressor', '1#空压机', 'COMP_001'),
(6, 'compressor', '2#空压机', 'COMP_002');

-- 插入泵列表
INSERT IGNORE INTO pumps (id, device_id, pump_no) VALUES
(1, 1, 'A'),
(2, 1, 'B'),
(3, 2, 'A'),
(4, 2, 'B'),
(5, 3, 'A'),
(6, 3, 'B'),
(7, 4, 'A'),
(8, 4, 'B');

-- 插入报表分类
INSERT IGNORE INTO report_categories (id, scope, name, sort) VALUES 
(1, 'site', '现场设备', 10),
(2, 'cb26', 'CB26设备', 20),
(3, 'control', '中控设备', 30);

-- 插入报表定义
INSERT IGNORE INTO reports (id, category_id, name, code, fields_json, time_limit_days, enabled, created_by) VALUES 
(1001, 1, '平台泵类设备运行保养记录', 'platform_pumps', 
'{"fields":[
  {"name":"start_time","label":"开泵时间","type":"time"},
  {"name":"stop_time","label":"停泵时间","type":"time"},
  {"name":"voltage_v","label":"电压(V)","type":"number","min":0,"max":600},
  {"name":"current_a","label":"电流(A)","type":"number","min":0,"max":600},
  {"name":"pump_pressure_mpa","label":"泵压(MPa)","type":"number","min":0,"max":2,"step":0.01},
  {"name":"dry_pressure_mpa","label":"干压(MPa)","type":"number","min":0,"max":2,"step":0.01},
  {"name":"flow_m3h","label":"排量(m³/h)","type":"number","min":0,"max":10000},
  {"name":"bt_pump_front","label":"轴承温度-泵-前(℃)","type":"number","min":0,"max":200},
  {"name":"bt_pump_rear","label":"轴承温度-泵-后(℃)","type":"number","min":0,"max":200},
  {"name":"bt_motor_front","label":"轴承温度-电机-前(℃)","type":"number","min":0,"max":200},
  {"name":"bt_motor_rear","label":"轴承温度-电机-后(℃)","type":"number","min":0,"max":200},
  {"name":"maint_p","label":"保养累计时数(一保)","type":"number","min":0},
  {"name":"maint_s","label":"保养累计时数(二保)","type":"number","min":0},
  {"name":"total_hours","label":"总累计时间(h)","type":"number","min":0},
  {"name":"remark","label":"备注","type":"text"}
]}', 2, 1, 1);

-- 创建默认管理员用户（如果不存在）
INSERT IGNORE INTO users (id, username, password_hash, real_name, status) VALUES 
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 1);

-- 给管理员分配 admin 角色
INSERT IGNORE INTO user_role (user_id, role_id) VALUES (1, 1);
