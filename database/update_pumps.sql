-- 更新调水泵数据：改为1#、2#、3#、4#调水泵

-- 清理旧数据
DELETE FROM report_devices WHERE report_id=1001;
DELETE FROM pumps;
DELETE FROM devices WHERE type='pump';

-- 插入调水泵设备（现场设备）
INSERT INTO devices (id, type, name, code) VALUES
(1, 'pump', '调水泵', 'PUMP_SITE');

-- 插入泵列表（4个泵号：1#、2#、3#、4#）
INSERT INTO pumps (id, device_id, pump_no) VALUES
(1, 1, '1#'),
(2, 1, '2#'),
(3, 1, '3#'),
(4, 1, '4#');

-- 确保报表分类存在
INSERT IGNORE INTO report_categories (id, scope, name, sort) VALUES 
(1, 'site', '现场设备', 10);

-- 确保报表定义存在
INSERT IGNORE INTO reports (id, category_id, name, code, fields_json, time_limit_days, enabled, created_by) VALUES 
(1001, 1, '平台泵类设备运行保养记录', 'platform_pumps', 
'{"fields":[{"name":"start_time","label":"开泵时间","type":"time"},{"name":"stop_time","label":"停泵时间","type":"time"},{"name":"voltage_v","label":"电压(V)","type":"number","min":0,"max":600},{"name":"current_a","label":"电流(A)","type":"number","min":0,"max":600},{"name":"pump_pressure_mpa","label":"泵压(MPa)","type":"number","min":0,"max":2,"step":0.01},{"name":"dry_pressure_mpa","label":"干压(MPa)","type":"number","min":0,"max":2,"step":0.01},{"name":"flow_m3h","label":"排量(m³/h)","type":"number","min":0,"max":10000},{"name":"bt_pump_front","label":"轴承温度-泵-前(℃)","type":"number","min":0,"max":200},{"name":"bt_pump_rear","label":"轴承温度-泵-后(℃)","type":"number","min":0,"max":200},{"name":"bt_motor_front","label":"轴承温度-电机-前(℃)","type":"number","min":0,"max":200},{"name":"bt_motor_rear","label":"轴承温度-电机-后(℃)","type":"number","min":0,"max":200},{"name":"maint_p","label":"保养累计时数(一保)","type":"number","min":0},{"name":"maint_s","label":"保养累计时数(二保)","type":"number","min":0},{"name":"total_hours","label":"总累计时间(h)","type":"number","min":0},{"name":"remark","label":"备注","type":"text"}]}', 2, 1, 1);

-- 创建报表设备关联表（如果不存在）
CREATE TABLE IF NOT EXISTS report_devices (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  report_id BIGINT NOT NULL COMMENT '报表ID',
  device_id BIGINT NOT NULL COMMENT '设备ID',
  pump_id BIGINT NULL COMMENT '泵ID（可选，为空表示整个设备）',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY uk_report_device_pump (report_id, device_id, pump_id)
) ENGINE=InnoDB COMMENT='报表-设备关联';

-- 为平台泵类报表配置默认设备关联
INSERT IGNORE INTO report_devices (report_id, device_id, pump_id) VALUES
(1001, 1, 1),  -- 调水泵-1#
(1001, 1, 2),  -- 调水泵-2#
(1001, 1, 3),  -- 调水泵-3#
(1001, 1, 4);  -- 调水泵-4#

-- 查询验证
SELECT '=== 设备列表 ===' as info;
SELECT id, name, code FROM devices WHERE type='pump' ORDER BY id;
SELECT '=== 泵列表 ===' as info;
SELECT p.id, d.name as device_name, p.pump_no FROM pumps p JOIN devices d ON p.device_id=d.id ORDER BY d.id, p.pump_no;
SELECT '=== 报表设备关联 ===' as info;
SELECT rd.report_id, d.name as device_name, p.pump_no FROM report_devices rd JOIN devices d ON rd.device_id=d.id LEFT JOIN pumps p ON rd.pump_id=p.id WHERE rd.report_id=1001 ORDER BY d.name, p.pump_no;

-- 更新报表分类为现场设备
UPDATE reports SET category_id=1 WHERE id=1001;
