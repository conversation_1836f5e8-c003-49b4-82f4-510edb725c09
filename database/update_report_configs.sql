-- 根据截图更新报表时间限制配置

-- 更新报表1001：泵类设备运行记录（连续） - 7天日级时效控制
UPDATE reports SET
    time_limit_days = 7,
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE id = 1001;

-- 更新报表1002：泵类设备运行记录（间歇） - 2天日级时效控制
UPDATE reports SET
    time_limit_days = 2,
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE id = 1002;

-- 更新报表2001：空压机运行记录 - 2天日级时效控制
UPDATE reports SET
    time_limit_days = 2,
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE id = 2001;

-- 更新报表2002：风机运行记录 - 2天日级时效控制
UPDATE reports SET
    time_limit_days = 2,
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE id = 2002;

-- 更新报表2003：电机运行记录 - 2天日级时效控制
UPDATE reports SET
    time_limit_days = 2,
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE id = 2003;

-- 更新报表2004：通用设备记录 - 2天日级时效控制
UPDATE reports SET
    time_limit_days = 2,
    time_limit_type = 'day',
    cycle_type = 'daily'
WHERE id = 2004;

-- 验证更新结果
SELECT id, name, time_limit_days, time_limit_type, cycle_type 
FROM reports 
WHERE id IN (1001, 1002, 2001, 2002, 2003, 2004)
ORDER BY id;
