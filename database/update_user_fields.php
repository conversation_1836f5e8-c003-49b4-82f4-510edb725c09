<?php
/**
 * 更新用户表结构和角色数据
 * 1. 删除用户表中的email和mobile字段
 * 2. 删除系统配置中的time_limit_days配置
 * 3. 确保所有6个角色都存在
 */

declare(strict_types=1);

// 引入数据库配置
$config = require __DIR__ . '/../config/db.php';

try {
    $dsn = sprintf('mysql:host=%s;port=%d;dbname=%s;charset=%s', 
        $config['host'], $config['port'], $config['dbname'], $config['charset']);
    
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "连接数据库成功\n";
    
    // 1. 检查并删除用户表中的email和mobile字段
    echo "\n1. 检查用户表字段...\n";
    
    // 检查email字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'email'");
    if ($stmt->rowCount() > 0) {
        echo "删除email字段...\n";
        $pdo->exec("ALTER TABLE users DROP COLUMN email");
        echo "✓ email字段已删除\n";
    } else {
        echo "✓ email字段不存在，无需删除\n";
    }
    
    // 检查mobile字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'mobile'");
    if ($stmt->rowCount() > 0) {
        echo "删除mobile字段...\n";
        $pdo->exec("ALTER TABLE users DROP COLUMN mobile");
        echo "✓ mobile字段已删除\n";
    } else {
        echo "✓ mobile字段不存在，无需删除\n";
    }
    
    // 2. 删除系统配置中的time_limit_days配置
    echo "\n2. 删除历史录入限制配置...\n";
    $stmt = $pdo->prepare("DELETE FROM system_config WHERE cfg_key = 'time_limit_days'");
    $stmt->execute();
    echo "✓ time_limit_days配置已删除\n";
    
    // 3. 确保所有6个角色都存在
    echo "\n3. 检查和创建角色...\n";
    
    $requiredRoles = [
        ['code' => 'admin', 'name' => '管理员'],
        ['code' => 'mod', 'name' => '普通管理员'],
        ['code' => 'user', 'name' => '普通用户'],
        ['code' => 'site', 'name' => '现场角色'],
        ['code' => 'ctrl', 'name' => '中控角色'],
        ['code' => 'cb26', 'name' => 'CB26角色']
    ];
    
    foreach ($requiredRoles as $role) {
        $stmt = $pdo->prepare("SELECT id FROM roles WHERE code = ?");
        $stmt->execute([$role['code']]);
        
        if ($stmt->rowCount() == 0) {
            // 角色不存在，创建它
            $insertStmt = $pdo->prepare("INSERT INTO roles (code, name) VALUES (?, ?)");
            $insertStmt->execute([$role['code'], $role['name']]);
            echo "✓ 创建角色: {$role['name']} ({$role['code']})\n";
        } else {
            echo "✓ 角色已存在: {$role['name']} ({$role['code']})\n";
        }
    }
    
    // 4. 验证当前用户表结构
    echo "\n4. 验证用户表结构...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM users");
    $columns = $stmt->fetchAll();
    
    echo "当前用户表字段:\n";
    foreach ($columns as $column) {
        echo "  - {$column['Field']} ({$column['Type']})\n";
    }
    
    // 5. 显示所有角色
    echo "\n5. 当前系统角色:\n";
    $stmt = $pdo->query("SELECT id, code, name FROM roles ORDER BY id");
    $roles = $stmt->fetchAll();
    
    echo str_repeat('-', 50) . "\n";
    printf("%-5s %-10s %s\n", "ID", "代码", "名称");
    echo str_repeat('-', 50) . "\n";
    
    foreach ($roles as $role) {
        printf("%-5s %-10s %s\n", $role['id'], $role['code'], $role['name']);
    }
    
    // 6. 显示当前系统配置
    echo "\n6. 当前系统配置:\n";
    $stmt = $pdo->query("SELECT cfg_key, cfg_value, remark FROM system_config ORDER BY cfg_key");
    $configs = $stmt->fetchAll();
    
    echo str_repeat('-', 70) . "\n";
    printf("%-20s %-25s %s\n", "配置键", "配置值", "备注");
    echo str_repeat('-', 70) . "\n";
    
    foreach ($configs as $config) {
        $value = strlen($config['cfg_value']) > 20 ? substr($config['cfg_value'], 0, 17) . '...' : $config['cfg_value'];
        printf("%-20s %-25s %s\n", $config['cfg_key'], $value, $config['remark']);
    }
    
    echo "\n用户表结构和角色数据更新完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
