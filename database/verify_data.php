<?php
// 验证数据库数据
require_once __DIR__ . '/../config/db.php';

$config = require __DIR__ . '/../config/db.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "=== 设备列表 ===\n";
    $devices = $pdo->query("SELECT id, name, code FROM devices WHERE type='pump' ORDER BY id")->fetchAll();
    foreach ($devices as $device) {
        echo "ID: {$device['id']}, 名称: {$device['name']}, 编码: {$device['code']}\n";
    }
    
    echo "\n=== 泵列表 ===\n";
    $pumps = $pdo->query("SELECT p.id, d.name as device_name, p.pump_no FROM pumps p JOIN devices d ON p.device_id=d.id ORDER BY d.id, p.pump_no")->fetchAll();
    foreach ($pumps as $pump) {
        echo "ID: {$pump['id']}, 设备: {$pump['device_name']}, 泵号: {$pump['pump_no']}\n";
    }
    
    echo "\n=== 报表设备关联 ===\n";
    $relations = $pdo->query("SELECT rd.report_id, d.name as device_name, p.pump_no FROM report_devices rd JOIN devices d ON rd.device_id=d.id LEFT JOIN pumps p ON rd.pump_id=p.id WHERE rd.report_id=1001 ORDER BY d.name, p.pump_no")->fetchAll();
    foreach ($relations as $rel) {
        echo "报表ID: {$rel['report_id']}, 设备: {$rel['device_name']}, 泵号: {$rel['pump_no']}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
