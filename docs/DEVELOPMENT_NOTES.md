# 设备资料录入管理系统 - 开发文档

## 重要修改记录

### 2025-09-04 数据结构优化和设备选择逻辑统一

#### 1. 数据库字段优化

**时间字段简化**：
- **删除了冗余字段**：`start_time` 和 `end_time`
- **保留字段**：`time_slot_start` 和 `time_slot_end`
- **原因**：四个时间字段中有两个是重复的，简化数据结构提高效率

**字段备注修复**：
- `field_data`：结构化字段数据(间歇运行设备专用)
- `data_json`：通用JSON数据(连续运行设备使用)

#### 2. 数据存储逻辑优化

**间歇运行设备** (Continuous Reports)：
```php
field_data = json_encode($fieldData)  // 存储结构化字段数据
data_json = '{}'                      // 空对象
```

**连续运行设备** (Generic Reports)：
```php
field_data = NULL                     // 不使用
data_json = json_encode($data)        // 存储JSON数据
```

#### 3. 设备选择逻辑修复

**问题**：间歇运行设备页面切换设备后没有反应，默认选择设备功能失效

**原因**：
- 同一设备的多个泵使用相同的 `device_id` 作为 `option.value`
- 导致选择框中有重复值，无法正确区分不同的泵
- **关键问题**：API返回的 `auto_select` 是 `device_id`，但前端 `option.value` 使用的是 `pump_id`，导致值不匹配

**解决方案**：
```javascript
// 修改前：使用device_id作为option.value（会重复）
option.value = device.device_id;

// 修改后：间歇运行设备使用pump_id作为option.value
if (device.pump_id && device.pump_no) {
    option.value = device.pump_id;        // 每个泵有唯一的ID
    option.dataset.deviceId = device.device_id;
} else {
    option.value = device.device_id;
}
```

**系统逻辑统一**：
```php
// 最终方案：统一返回device_id作为auto_select
$response['auto_select'] = $deviceId; // 简化逻辑，统一使用device_id
```

```javascript
// 前端统一逻辑：所有设备都使用device_id作为option.value
option.value = device.device_id || device.id;

// 泵设备额外存储pump_id用于后端保存
if (device.pump_id) {
    option.dataset.pumpId = device.pump_id;
}

// 保存时根据设备类型选择正确的ID
device_id: pumpId || entryDeviceId
```

#### 4. 关键代码修改

**前端设备选择框渲染** (`js/entry-continuous.js`):
```javascript
devices.forEach((device, index) => {
    const option = document.createElement('option');
    
    // 间歇运行设备：每个泵是独立的选项，使用pump_id作为值
    if (device.pump_id && device.pump_no) {
        option.value = device.pump_id;
        option.dataset.deviceId = device.device_id;
    } else {
        option.value = device.device_id || device.id;
    }
    
    // 处理设备名称显示
    let deviceName = device.device_name || device.name || '';
    if (device.pump_no) {
        deviceName = `${device.pump_no}${deviceName}`;
    }
    option.textContent = deviceName;
    
    deviceSelect.appendChild(option);
});
```

**后端数据保存** (`app/Controllers/EntryController.php`):
```php
// 删除了对start_time和end_time字段的引用
INSERT INTO report_entries (
    report_id, device_id, pump_id, object_id, entry_date,
    time_slot_start, time_slot_end, field_data, data_json, created_by, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
```

#### 5. 数据完整性保证

**设备ID关联逻辑**：
```php
// 检查传入的deviceId是否是pump_id
$stmt = $pdo->prepare("SELECT id, device_id FROM pumps WHERE id = ?");
$stmt->execute([$deviceId]);
$pumpInfo = $stmt->fetch();

if ($pumpInfo) {
    // 传入的是pump_id
    $pumpId = $deviceId;
    $objectId = $deviceId;
    $actualDeviceId = (int)$pumpInfo['device_id'];
} else {
    // 传入的是device_id
    $actualDeviceId = $deviceId;
    $objectId = $deviceId;
}
```

#### 6. 注意事项

**⚠️ 重要提醒**：

1. **间歇运行设备**：
   - 前端选择框的 `value` 是 `pump_id`
   - 保存时直接传递 `pump_id`
   - 后端会自动解析出对应的 `device_id`

2. **连续运行设备**：
   - 前端选择框的 `value` 是 `device_id`
   - 保存时传递 `device_id`
   - 后端处理时间段数据

3. **数据库字段**：
   - `device_id`：实际的设备ID
   - `pump_id`：泵的ID（如果是泵设备）
   - `object_id`：对象ID（用于查询，等于pump_id或device_id）
   - `time_slot_start/end`：统一的时间字段

4. **API数据格式**：
   - 每个泵都是独立的记录
   - 同一设备的不同泵有不同的 `pump_id`
   - 前端需要正确处理这种一对多关系

#### 7. 测试要点

- [ ] 间歇运行设备切换功能正常
- [ ] 数据保存包含完整的关联字段
- [ ] 时间字段简化后功能正常
- [ ] 字段备注显示正确
- [ ] 默认选择第一个设备功能正常

#### 8. 影响范围

**修改的文件**：
- `js/entry-continuous.js` - 前端设备选择逻辑
- `app/Controllers/EntryController.php` - 后端保存逻辑
- 数据库表 `report_entries` - 字段结构优化

**兼容性**：
- 向后兼容，不影响现有数据
- 新保存的数据结构更规范
- 前端逻辑更清晰

---

## 开发规范

### 设备类型处理

1. **间歇运行设备** (report_type = 'continuous')
   - 使用 `pump_id` 作为主要标识
   - 数据存储在 `field_data` 字段
   - 每个泵独立管理

2. **连续运行设备** (report_type = 'generic')
   - 使用 `device_id` 作为主要标识
   - 数据存储在 `data_json` 字段
   - 按时间段管理

### 数据库设计原则

- 避免冗余字段
- 明确字段用途和备注
- 保持数据完整性约束
- 合理使用关联字段

### 前端开发原则

- 根据设备类型选择正确的标识字段
- 保持选择框值的唯一性
- 提供清晰的用户界面反馈
- 处理好设备与泵的一对多关系
