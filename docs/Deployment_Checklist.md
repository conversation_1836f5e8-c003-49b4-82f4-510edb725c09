# 系统配置和用户管理功能部署检查清单

> 版本：v1.0 | 更新时间：2025-08-28

## 部署前检查

### 1. 文件结构检查

确认以下文件已正确创建：

#### 核心服务类
- [ ] `app/Services/ConfigService.php` - 系统配置服务
- [ ] `app/Services/AuditService.php` - 审计日志服务
- [ ] `app/Middlewares/IpAclMiddleware.php` - IP访问控制中间件

#### 控制器更新
- [ ] `app/Controllers/SystemController.php` - 系统配置控制器（已完善）
- [ ] `app/Controllers/UsersController.php` - 用户管理控制器（已完善）

#### 视图文件
- [ ] `app/Views/system/config.php` - 系统配置页面
- [ ] `app/Views/users/index.php` - 用户管理页面
- [ ] `app/Views/system/ip_acl.php` - IP访问控制页面

#### 数据库脚本
- [ ] `database/add_system_tables.sql` - 系统表创建脚本
- [ ] `database/init_system_tables.php` - 数据库初始化脚本

#### 工具脚本
- [ ] `scripts/test_system_features.php` - 功能测试脚本

#### 文档
- [ ] `docs/SystemConfig_UserGuide.md` - 用户使用指南
- [ ] `docs/Deployment_Checklist.md` - 部署检查清单

### 2. 路由配置检查

确认 `config/bootstrap.php` 中已添加以下路由：

```php
// 系统配置相关路由
\App\Core\Router::register('POST', 'sys/saveConfig', [\App\Controllers\SystemController::class, 'saveConfig']);
\App\Core\Router::register('GET', 'sys/ipAcl', [\App\Controllers\SystemController::class, 'ipAcl']);
\App\Core\Router::register('POST', 'sys/createIpAcl', [\App\Controllers\SystemController::class, 'createIpAcl']);
\App\Core\Router::register('POST', 'sys/updateIpAcl', [\App\Controllers\SystemController::class, 'updateIpAcl']);
\App\Core\Router::register('POST', 'sys/deleteIpAcl', [\App\Controllers\SystemController::class, 'deleteIpAcl']);

// 用户管理相关路由
\App\Core\Router::register('POST', 'users/create', [\App\Controllers\UsersController::class, 'create']);
\App\Core\Router::register('POST', 'users/update', [\App\Controllers\UsersController::class, 'update']);
\App\Core\Router::register('POST', 'users/delete', [\App\Controllers\UsersController::class, 'delete']);
```

### 3. 中间件集成检查

确认 `config/bootstrap.php` 中已添加IP访问控制中间件：

```php
// IP访问控制检查（在Session之前）
if (!isset($_GET['r']) || $_GET['r'] !== 'auth/login') {
    if (class_exists('\App\Middlewares\IpAclMiddleware')) {
        if (!\App\Middlewares\IpAclMiddleware::check()) {
            \App\Middlewares\IpAclMiddleware::deny();
        }
    }
}
```

## 部署步骤

### 步骤1：数据库初始化

```bash
# 在项目根目录执行
cd /path/to/bbgl
php database/init_system_tables.php
```

预期输出：
```
连接数据库成功
执行成功: CREATE TABLE IF NOT EXISTS system_config...
执行成功: CREATE TABLE IF NOT EXISTS ip_acl...
执行成功: CREATE TABLE IF NOT EXISTS audit_logs...
执行成功: INSERT INTO system_config...
系统表初始化完成！
✓ 表 system_config 创建成功
✓ 表 ip_acl 创建成功
✓ 表 audit_logs 创建成功
系统配置项数量: 6
默认配置插入成功
```

### 步骤2：功能测试

```bash
# 运行功能测试脚本
php scripts/test_system_features.php
```

预期输出：
```
=== 系统功能测试 ===

1. 测试数据库连接...
   ✓ 数据库连接成功

2. 测试系统配置服务...
   ✓ 获取系统名称: 设备资料录入管理系统
   ✓ 设置配置成功: test_config_xxx = test_value_xxx
   ✓ 配置读取验证成功
   ✓ 清理测试配置完成
   ✓ 获取所有配置成功，共 6 项

3. 测试审计日志服务...
   ✓ 审计日志记录成功
   ✓ 审计日志查询成功，共 X 条记录

4. 测试数据库表结构...
   ✓ 表 users 存在
   ✓ 表 roles 存在
   [... 其他表检查 ...]

=== 测试完成 ===
所有核心功能测试通过！
```

### 步骤3：Web界面测试

1. **访问系统配置页面**
   - URL: `http://your-domain/bbgl/index.php?r=sys/config`
   - 使用管理员账号登录
   - 验证配置表单显示正常
   - 测试配置保存功能

2. **访问用户管理页面**
   - URL: `http://your-domain/bbgl/index.php?r=sys/users`
   - 验证用户列表显示正常
   - 测试创建、编辑、删除用户功能

3. **访问IP访问控制页面**
   - URL: `http://your-domain/bbgl/index.php?r=sys/ipAcl`
   - 验证IP规则列表显示正常
   - 测试添加、编辑、删除IP规则功能

## 功能验证清单

### 系统配置功能
- [ ] 系统配置页面正常显示
- [ ] 配置项数据正确加载
- [ ] 配置保存功能正常
- [ ] 配置修改后立即生效
- [ ] 审计日志正确记录配置变更

### 用户管理功能
- [ ] 用户列表正常显示
- [ ] 用户角色信息正确显示
- [ ] 创建用户功能正常
- [ ] 编辑用户功能正常
- [ ] 删除用户功能正常
- [ ] 密码修改功能正常
- [ ] 角色分配功能正常
- [ ] 用户状态切换功能正常
- [ ] 审计日志正确记录用户操作

### IP访问控制功能
- [ ] IP规则列表正常显示
- [ ] 添加IP规则功能正常
- [ ] 编辑IP规则功能正常
- [ ] 删除IP规则功能正常
- [ ] IP格式验证正常
- [ ] 白名单模式正常工作
- [ ] 黑名单模式正常工作
- [ ] 访问拒绝页面正常显示
- [ ] 审计日志正确记录IP规则变更

### 权限控制
- [ ] 非管理员用户无法访问系统配置
- [ ] 非管理员用户无法访问用户管理
- [ ] 非管理员用户无法访问IP访问控制
- [ ] 权限验证正常工作

### 安全性检查
- [ ] 密码正确加密存储
- [ ] CSRF保护正常工作
- [ ] 输入验证正常工作
- [ ] SQL注入防护正常
- [ ] XSS防护正常

## 性能检查

- [ ] 页面加载速度正常（< 2秒）
- [ ] 数据库查询优化
- [ ] 大量用户数据显示正常
- [ ] 大量IP规则处理正常

## 兼容性检查

### 浏览器兼容性
- [ ] Chrome 最新版本
- [ ] Firefox 最新版本
- [ ] Safari 最新版本
- [ ] Edge 最新版本

### 移动端适配
- [ ] 响应式布局正常
- [ ] 移动端操作正常
- [ ] 触摸交互正常

## 部署后维护

### 日志监控
- [ ] 设置审计日志定期清理
- [ ] 监控系统错误日志
- [ ] 监控IP访问拒绝日志

### 备份策略
- [ ] 定期备份系统配置
- [ ] 定期备份用户数据
- [ ] 定期备份审计日志

### 安全维护
- [ ] 定期检查用户权限
- [ ] 定期更新IP访问控制规则
- [ ] 定期检查审计日志异常

## 故障恢复

### 紧急访问
如果IP访问控制配置错误导致无法访问：

```sql
-- 临时禁用IP访问控制
UPDATE system_config SET cfg_value = '"disabled"' WHERE cfg_key = 'ip_acl_mode';
```

### 管理员密码重置
如果忘记管理员密码：

```sql
-- 重置admin密码为admin123
UPDATE users SET password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'admin';
```

## 完成确认

部署完成后，请确认：

- [ ] 所有测试项目通过
- [ ] 功能验证清单完成
- [ ] 用户培训完成
- [ ] 文档交付完成
- [ ] 监控和备份策略就位

---

**部署负责人签字：** _________________ **日期：** _________________

**测试负责人签字：** _________________ **日期：** _________________
