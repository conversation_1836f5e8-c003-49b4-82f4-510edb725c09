# 设备资料录入管理系统 # 设备资料录入管理系统 - 开发文档

## 目录
- [常见问题与修复](#常见问题与修复)
- [数据库设计](#数据库设计)
- [API接口](#api接口)
- [前端组件](#前端组件)

## 常见问题与修复

### 1. 报表设备配置保存问题

**问题描述**：
在报表配置页面添加新设备时，原有的设备配置会丢失，只保存最后新增的设备。

**问题根源**：
在 `ReportsController.php` 的 `getDevices` API 中，存在错误的数据处理逻辑：

```php
// 错误逻辑（已修复）
foreach ($devices as &$device) {
    $pumpId = $device['object_id'] ?: $device['pump_id'];
    if ($pumpId) {
        $device['device_id'] = $pumpId;  // 错误：将pump_id赋值给device_id
        $device['pump_id'] = $pumpId;
    }
}
```

这导致返回的数据中 `device_id` 和 `pump_id` 相同，破坏了数据结构的正确性。

**修复方案**：
```php
// 修复后的逻辑
foreach ($devices as &$device) {
    $pumpId = $device['object_id'] ?: $device['pump_id'];
    if ($pumpId) {
        // 保持device_id为原始设备ID，不要修改
        // pump_id设置为实际的泵ID
        $device['pump_id'] = $pumpId;
    }
}
```

**影响范围**：
- 报表设备配置功能
- 设备与泵的关联关系
- 前端设备选择逻辑

### 2. 连续报表数据保存缺失关键字段

**问题描述**：
连续运行设备（如溶气泵、闭排泵等）录入数据时，数据库中缺失 `device_id`、`pump_id`、`object_id` 等关键字段，导致数据无法正确关联到设备。

**问题根源**：
1. **前端传值错误**：设备选择框传递的是 `device_id`，但对于泵类设备应该传递 `pump_id`
2. **后端保存逻辑不完整**：`saveContinuousEntry` 方法缺少关键字段的处理

**前端问题**：
```javascript
// 错误的逻辑（已修复）
option.value = device.device_id || device.id;  // 总是使用device_id

// 修复后的逻辑（同时修复了entry-continuous.js和entry-generic.js）
if (device.pump_id && device.pump_no) {
    option.value = device.pump_id;  // 泵类设备使用pump_id
} else {
    option.value = device.device_id || device.id;  // 普通设备使用device_id
}
```

**后端问题**：
```php
// 错误的INSERT语句（已修复）
INSERT INTO report_entries (report_id, device_id, entry_date, start_time, end_time, field_data, created_by, created_at)

// 修复后的INSERT语句
INSERT INTO report_entries (report_id, device_id, pump_id, object_id, entry_date, start_time, end_time,
                           time_slot_start, time_slot_end, field_data, data_json, created_by, created_at)
```

**修复方案**：
1. 修改前端设备选择逻辑，正确传递泵ID（`entry-continuous.js` 和 `entry-generic.js`）
2. 完善后端保存逻辑，处理设备与泵的关系（`EntryController.php`）
3. 确保所有必需字段都正确保存

**影响范围**：
- 连续报表数据录入
- 通用报表数据录入
- 泵类设备数据关联
- 数据查询和统计

### 3. 连续报表数据显示问题

**问题描述**：
在连续报表录入页面，已录入的数据无法显示，页面显示为空，但数据库中确实存在数据。

**问题根源**：
在 `EntryController.php` 的 `loadContinuousEntries` 方法中，查询条件过于严格：

```php
// 错误的查询条件（已修复）
$stmt = $pdo->prepare("
    SELECT e.*, u.username as created_by_name
    FROM report_entries e
    LEFT JOIN users u ON e.created_by = u.id
    WHERE e.report_id = ?
    AND e.device_id = ?
    AND e.pump_id IS NULL      // 错误：要求pump_id为NULL
    AND e.object_id IS NULL    // 错误：要求object_id为NULL
    AND e.entry_date BETWEEN ? AND ?
    ORDER BY e.entry_date {$orderBy}, e.created_at {$orderBy}
");
```

但实际数据中，泵类设备的记录都有具体的 `pump_id` 和 `object_id`，导致查询结果为空。

**修复方案**：
```php
// 修复后的查询条件
$stmt = $pdo->prepare("
    SELECT e.*, u.username as created_by_name
    FROM report_entries e
    LEFT JOIN users u ON e.created_by = u.id
    WHERE e.report_id = ?
    AND e.device_id = ?                    // 只按设备ID筛选
    AND e.entry_date BETWEEN ? AND ?
    ORDER BY e.entry_date {$orderBy}, e.created_at {$orderBy}
");
```

**数据结构说明**：
- `device_id`: 设备ID（如：13 = 闭排泵）
- `pump_id`: 泵ID（如：45 = 1#泵，46 = 2#泵）
- `object_id`: 对象ID（通常等于pump_id）

**影响范围**：
- 连续报表数据加载
- 设备数据查询逻辑
- 泵类设备的数据显示

### 4. JavaScript类型安全问题

**问题描述**：
前端可能出现 `TypeError: t.id.includes is not a function` 错误。

**问题根源**：
代码期望某些属性是字符串类型，但实际可能是数字类型。

**修复方案**：
在使用字符串方法前进行类型检查：

```javascript
// 修复前
function generateReportCode(name) {
    // ...
    if (name.includes(key)) {  // 如果name不是字符串会报错
        // ...
    }
}

// 修复后
function generateReportCode(name) {
    // 确保name是字符串
    if (typeof name !== 'string') {
        name = String(name || '');
    }
    // ...
    if (name.includes(key)) {
        // ...
    }
}
```

**最佳实践**：
- 在使用字符串方法前进行类型检查
- 使用 `Array.isArray()` 检查数组
- 对外部数据进行类型验证

## 数据库设计

### 核心表结构

#### report_entries 表
```sql
CREATE TABLE report_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    report_id INT NOT NULL,
    device_id INT NOT NULL,
    pump_id INT NULL,
    object_id INT NULL,
    entry_date DATE NOT NULL,
    time_slot_start TIME NULL,
    time_slot_end TIME NULL,
    field_data JSON NULL,
    data_json JSON NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INT NULL,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
);
```

#### 字段说明
- `device_id`: 设备ID，关联 devices 表
- `pump_id`: 泵ID，关联 pumps 表（可为空）
- `object_id`: 对象ID，泵类设备时等于pump_id，其他设备时等于device_id
- `field_data`: 字段数据（JSON格式）
- `data_json`: 完整数据（JSON格式）

### 数据查询策略

#### 按设备查询
```php
// 查询设备下所有记录（包括所有泵）
WHERE report_id = ? AND device_id = ?

// 查询特定泵的记录
WHERE report_id = ? AND (pump_id = ? OR object_id = ?)
```

#### 数据迁移注意事项
- 新增 `object_id` 字段时需要迁移现有 `pump_id` 数据
- 确保数据一致性：`object_id = pump_id`（泵类设备）

## 调试技巧

### 1. 数据库查询调试
```php
// 检查数据存在性
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM report_entries WHERE report_id=? AND device_id=?");
$stmt->execute([$reportId, $deviceId]);
$result = $stmt->fetch();
echo "记录数量: {$result['count']}\n";
```

### 2. 前端API调试
```javascript
// 在浏览器控制台查看API返回
fetch('/api/entry/load?report_id=1002&device_id=13&month=2025-09')
    .then(r => r.json())
    .then(data => console.log('API返回:', data));
```

### 3. 日志记录
```php
// 在关键位置添加日志
error_log("EntryController::loadContinuousEntries - deviceId: $deviceId, pumpInfo: " . json_encode($pumpInfo));
```

## API接口

### 报表设备配置相关

#### 获取报表关联设备
```
GET /index.php?r=api/reports/devices&report_id={id}
```

**返回格式**：
```json
{
    "success": true,
    "data": [
        {
            "device_id": 6,
            "pump_id": 28,
            "object_id": 28,
            "device_name": "热水循环泵",
            "pump_no": "1#"
        }
    ]
}
```

#### 保存设备配置
```
POST /index.php?r=api/reports/save-devices
Content-Type: application/json

{
    "report_id": 1002,
    "devices": [
        {
            "device_id": 6,
            "pump_id": 28
        }
    ]
}
```

### 数据录入相关

#### 加载连续报表数据
```
GET /index.php?r=api/entry/load?report_id={id}&device_id={id}&month={YYYY-MM}&range=month
```

**参数说明**：
- `report_id`: 报表ID
- `device_id`: 设备ID或泵ID
- `month`: 查询月份（YYYY-MM格式）
- `range`: 查询范围（month/year）
- `sort`: 排序方式（asc/desc）

## 前端组件

### 设备配置组件

#### 数据流程
1. 加载所有可用设备：`getAllDevices`
2. 加载当前配置：`getDevices`
3. 渲染左右分栏界面
4. 用户操作：添加/移除设备
5. 保存配置：`saveDevices`

#### 关键函数
```javascript
// 设备配置主函数
window.configDevices = function(reportId) {
    // 加载数据并渲染界面
}

// 添加设备到配置
window.addDevice = function(deviceId, pumpId) {
    // 检查重复，添加到右侧容器
}

// 保存设备配置
window.saveDeviceConfig = function() {
    // 收集所有配置的设备，发送到后端
}
```

### 数据录入组件

#### 连续报表录入
- 文件：`js/entry-continuous.js`
- 支持按月/年查询
- 动态表格渲染
- 实时数据保存

#### 通用报表录入
- 文件：`js/entry-generic.js`
- 支持自定义字段
- 模板驱动界面
- 值班人员管理

## 开发规范

### 代码风格

#### PHP
```php
// 类名使用PascalCase
class EntryController extends BaseController

// 方法名使用camelCase
public function loadContinuousEntries(): string

// 常量使用UPPER_CASE
const MAX_ENTRIES_PER_PAGE = 100;

// 数组使用短语法
$data = ['key' => 'value'];
```

#### JavaScript
```javascript
// 函数名使用camelCase
function loadEntryData() {}

// 常量使用UPPER_CASE
const API_BASE_URL = '/api';

// 使用严格相等
if (deviceId === 13) {}

// 类型检查
if (typeof name === 'string') {}
```

### 错误处理

#### 后端
```php
try {
    $pdo->beginTransaction();
    // 业务逻辑
    $pdo->commit();
    echo json_encode(['success' => true]);
} catch (\Throwable $e) {
    $pdo->rollBack();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
```

#### 前端
```javascript
fetch(url)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // 成功处理
        } else {
            showMessage(result.message || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('网络错误:', error);
        showMessage('网络错误，请稍后重试', 'error');
    });
```

### 数据库操作

#### 查询优化
```php
// 使用预处理语句
$stmt = $pdo->prepare("SELECT * FROM table WHERE id = ?");
$stmt->execute([$id]);

// 批量操作使用事务
$pdo->beginTransaction();
foreach ($items as $item) {
    $stmt->execute([$item]);
}
$pdo->commit();
```

#### 索引建议
```sql
-- 报表数据查询索引
CREATE INDEX idx_report_entries_query ON report_entries(report_id, device_id, entry_date);

-- 设备关联索引
CREATE INDEX idx_report_devices ON report_devices(report_id, device_id);
```

## 测试指南

### 单元测试
```php
// 测试数据加载
public function testLoadContinuousEntries() {
    $controller = new EntryController();
    $_GET = ['report_id' => 1002, 'device_id' => 13, 'month' => '2025-09'];

    $result = $controller->entryLoad();
    $data = json_decode($result, true);

    $this->assertTrue($data['success']);
    $this->assertNotEmpty($data['data']);
}
```

### 集成测试
```javascript
// 测试API接口
async function testDeviceConfigAPI() {
    const response = await fetch('/api/reports/devices?report_id=1002');
    const data = await response.json();

    console.assert(data.success === true, 'API should return success');
    console.assert(Array.isArray(data.data), 'Data should be array');
}
```

## 部署指南

### 环境要求
- PHP 8.0+
- MySQL 8.0+
- Apache/Nginx
- 支持URL重写

### 配置文件
```php
// config/database.php
return [
    'host' => '127.0.0.1',
    'port' => 3306,
    'dbname' => 'bbgl_db',
    'username' => 'db_user',
    'password' => 'db_password',
    'charset' => 'utf8mb4'
];
```

### 数据库迁移
```bash
# 运行迁移检查
php database/check_migration_status.php

# 手动执行迁移
mysql -u username -p database_name < database/migrations/001_add_object_id.sql
```

## 版本历史

### v1.1.0 (2025-09-04)
- 修复报表设备配置保存问题
- 修复连续报表数据保存缺失关键字段问题
- 修复连续报表数据显示问题
- 增强JavaScript类型安全
- 清理重复代码和调试信息
- 完善开发文档和规范

> 版本：v1.0  | 作者：Augment Agent  | 更新时间：2025-08-28

---

## 1. 概述
- 本系统用于设备（现场/CB26/中控室）运行资料的录入、查询、导出及权限化管理。
- 设计目标：内网可用、无CDN依赖、可跨平台部署（Windows Server / Linux）。
- 访问入口（本地）：http://localhost:8088/bbgl/

## 2. 技术栈与运行环境
- 操作系统：Windows 10（开发）、Windows Server / Linux（生产）
- PHP：8.0+
- Web 服务器：Apache
- 数据库：MySQL 5.7+
- 浏览器：现代浏览器
- 依赖管理：Composer（/vendor 已本地化）
- 统一时区：Asia/Shanghai（北京时间）

## 3. 目录结构
```
/bbgl/
├── app/                  # 业务代码（建议新增）
│   ├── Controllers/
│   ├── Models/
│   ├── Services/
│   ├── Middlewares/
│   └── Views/
├── config/               # 配置（常量、数据库、权限、菜单配置等）
├── css/                  # 样式（含 webfonts/ 存放 Font Awesome 字体）
│   └── webfonts/
├── database/             # 初始化 SQL、迁移脚本、示例数据
├── js/                   # 前端脚本
├── logs/                 # 应用与审计日志（自动创建、按日切割）
├── vendor/               # Composer 依赖（已下载）
├── .htaccess             # Apache 重写（可选，生产请根据虚拟主机配置）
└── index.php             # 入口文件
```

## 4. 资源引用规范（强制）
- 字体文件：/css/webfonts/，引用使用 BASE_URL 常量。
- CSS：
  1) `<?php echo BASE_URL; ?>css/bootstrap.min.css`
  2) `<?php echo BASE_URL; ?>css/all.min.css`（Font Awesome）
  3) 自定义 CSS：存放于 `/css`，第三方之后再引入。
- JS：
  1) `<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js`
  2) `<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js`
  3) `<?php echo BASE_URL; ?>js/chart.min.js`
  4) 自定义 JS：存放于 `/js`，第三方之后再引入。
- 路径规范：统一通过 BASE_URL 生成；禁止使用 `../`；大小写敏感；不含中文。

## 5. 编码、时区、依赖加载
- 全站 UTF-8（HTML `<meta charset="UTF-8">`）。
- PHP 时区：`date_default_timezone_set('Asia/Shanghai');`
- Composer：`require __DIR__ . '/vendor/autoload.php';`

### 5.1 入口文件（样例片段）
```php
// index.php（节选）
declare(strict_types=1);
ini_set('display_errors', '0');
error_reporting(E_ALL);

date_default_timezone_set('Asia/Shanghai');
require __DIR__ . '/vendor/autoload.php';

// 计算 BASE_URL，固定到 /bbgl/ 根
$scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
define('BASE_URL', $scheme . '://' . $host . '/bbgl/');

require __DIR__ . '/config/bootstrap.php';
\App\Router::dispatch();
```

### 5.2 统一头部/尾部（样例片段）
```php
// views/partials/head.php（节选）
?><meta charset="UTF-8">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>css/bootstrap.min.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>css/all.min.css"> <!-- Font Awesome -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>css/app.css">
```
```php
// views/partials/scripts.php（节选）
?><script src="<?php echo BASE_URL; ?>js/jquery-3.6.0.min.js"></script>
<script src="<?php echo BASE_URL; ?>js/bootstrap.bundle.min.js"></script>
<script src="<?php echo BASE_URL; ?>js/chart.min.js"></script>
<script src="<?php echo BASE_URL; ?>js/app.js"></script>
```

## 6. 界面与交互（Apple 高级风格）
- 视觉：
  - 浅色为主、极简留白、柔和阴影（阴影高度不超过 8dp）。
  - 字体：系统 San Francisco（Apple 平台）或 Helvetica Neue/Arial 兜底。
  - 图标：Font Awesome（本地），与文字对齐、统一 14/16px。
- 布局：
  - 顶部导航（系统名称、用户信息、全局搜索）。
  - 左侧栏（支持 1/2/3 级菜单，悬浮展开、选中高亮、图标+标题）。
  - 主内容区（卡片式面板/表格/表单），底部版权与版本号。
- 响应式：Bootstrap 栅格（≥1200 表格全显示；<992 隐藏次要列，提供详情抽屉）。
- 可参考提供的两张示意图进行风格校准（菜单悬浮高亮、表格分隔线、头部信息条）。

## 7. 权限与角色（RBAC）
- 角色：管理员、普通管理员、普通用户；另有业务角色：现场、中控、CB26（控制菜单可见性）。
- 权限粒度：菜单访问、模块/API 操作、报表录入/查询/导出。
- 可见性：基于角色-菜单映射动态生成左侧栏与路由守卫。

## 8. 业务规则摘要
- 录入日界：每日 00:00 起算。
- 不能录入未来时间段（例如当前 08:00 不可录 10:00+）。
- 可录入历史自然日范围：由系统配置（默认 2 天），管理员/普通管理员不受限制。
- 可分时段保存或整天保存；所有写入记录“录入人账号、时间”，超时修改记录“修改人、时间”。

## 9. 数据库设计（含中文备注）
> 字符集 utf8mb4、排序 utf8mb4_general_ci；如无特殊说明，时间字段均为 TIMESTAMP（默认 CURRENT_TIMESTAMP）。

```sql
-- 数据库：bbgl 关键表（节选）

-- 9.1 用户与权限
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  username VARCHAR(64) NOT NULL UNIQUE COMMENT '登录账号',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  real_name VARCHAR(64) NOT NULL COMMENT '姓名',
  email VARCHAR(128) NULL COMMENT '邮箱',
  mobile VARCHAR(20) NULL COMMENT '手机号',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1启用 0禁用',
  last_login_at DATETIME NULL COMMENT '上次登录时间',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='用户表';

CREATE TABLE roles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  code VARCHAR(32) NOT NULL UNIQUE COMMENT '角色编码: admin/mod/user/site/ctrl/cb26',
  name VARCHAR(64) NOT NULL COMMENT '角色名称',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='角色表';

CREATE TABLE permissions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  code VARCHAR(64) NOT NULL UNIQUE COMMENT '权限编码',
  name VARCHAR(128) NOT NULL COMMENT '权限名称',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='权限表';

CREATE TABLE user_role (
  user_id BIGINT NOT NULL COMMENT '用户ID',
  role_id BIGINT NOT NULL COMMENT '角色ID',
  PRIMARY KEY(user_id, role_id),
  CONSTRAINT fk_ur_u FOREIGN KEY (user_id) REFERENCES users(id),
  CONSTRAINT fk_ur_r FOREIGN KEY (role_id) REFERENCES roles(id)
) ENGINE=InnoDB COMMENT='用户-角色关联';

CREATE TABLE role_permission (
  role_id BIGINT NOT NULL COMMENT '角色ID',
  permission_id BIGINT NOT NULL COMMENT '权限ID',
  PRIMARY KEY(role_id, permission_id),
  CONSTRAINT fk_rp_r FOREIGN KEY (role_id) REFERENCES roles(id),
  CONSTRAINT fk_rp_p FOREIGN KEY (permission_id) REFERENCES permissions(id)
) ENGINE=InnoDB COMMENT='角色-权限关联';

-- 9.2 菜单（支持 3 级）
CREATE TABLE menus (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  parent_id BIGINT NULL COMMENT '父级ID，根为NULL',
  level TINYINT NOT NULL DEFAULT 1 COMMENT '层级 1/2/3',
  title VARCHAR(64) NOT NULL COMMENT '菜单标题',
  key_code VARCHAR(64) NOT NULL UNIQUE COMMENT '菜单唯一编码',
  icon VARCHAR(32) NULL COMMENT '图标class',
  route VARCHAR(128) NULL COMMENT '路由/页面地址',
  sort INT NOT NULL DEFAULT 0 COMMENT '排序',
  visible TINYINT NOT NULL DEFAULT 1 COMMENT '是否可见',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='系统菜单';

CREATE TABLE role_menu (
  role_id BIGINT NOT NULL COMMENT '角色ID',
  menu_id BIGINT NOT NULL COMMENT '菜单ID',
  PRIMARY KEY(role_id, menu_id),
  CONSTRAINT fk_rm_r FOREIGN KEY (role_id) REFERENCES roles(id),
  CONSTRAINT fk_rm_m FOREIGN KEY (menu_id) REFERENCES menus(id)
) ENGINE=InnoDB COMMENT='角色-菜单可见性';

-- 9.3 报表与录入
CREATE TABLE report_categories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  scope ENUM('site','cb26','control') NOT NULL COMMENT '类别范围:现场/CB26/中控',
  name VARCHAR(64) NOT NULL COMMENT '分类名称',
  sort INT NOT NULL DEFAULT 0 COMMENT '排序',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='报表分类';

CREATE TABLE reports (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  name VARCHAR(128) NOT NULL COMMENT '报表名称',
  code VARCHAR(64) NOT NULL UNIQUE COMMENT '报表编码',
  fields_json JSON NOT NULL COMMENT '录入字段定义(JSON)',
  time_limit_days INT NOT NULL DEFAULT 2 COMMENT '可录入天数限制',
  enabled TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
  created_by BIGINT NOT NULL COMMENT '创建人ID',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  CONSTRAINT fk_rep_cat FOREIGN KEY (category_id) REFERENCES report_categories(id)
) ENGINE=InnoDB COMMENT='报表定义';

CREATE TABLE devices (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  type VARCHAR(32) NOT NULL COMMENT '设备类型',
  name VARCHAR(128) NOT NULL COMMENT '设备名称',
  code VARCHAR(64) NOT NULL UNIQUE COMMENT '设备编码',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='设备';

CREATE TABLE pumps (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  device_id BIGINT NOT NULL COMMENT '设备ID',
  pump_no VARCHAR(32) NOT NULL COMMENT '泵号',
  UNIQUE KEY uk_device_pump (device_id, pump_no),
  CONSTRAINT fk_pump_device FOREIGN KEY (device_id) REFERENCES devices(id)
) ENGINE=InnoDB COMMENT='泵列表';

CREATE TABLE report_entries (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  report_id BIGINT NOT NULL COMMENT '报表ID',
  device_id BIGINT NULL COMMENT '设备ID(可选)',
  pump_id BIGINT NULL COMMENT '泵ID(可选)',
  entry_date DATE NOT NULL COMMENT '自然日',
  time_slot_start TIME NOT NULL COMMENT '开始时间段',
  time_slot_end TIME NOT NULL COMMENT '结束时间段',
  data_json JSON NOT NULL COMMENT '录入数据(JSON)',
  created_by BIGINT NOT NULL COMMENT '录入人',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间',
  updated_by BIGINT NULL COMMENT '修改人',
  updated_at DATETIME NULL COMMENT '修改时间',
  late_modified TINYINT NOT NULL DEFAULT 0 COMMENT '是否超时修改',
  CONSTRAINT fk_re_r FOREIGN KEY (report_id) REFERENCES reports(id)
) ENGINE=InnoDB COMMENT='报表录入数据';

-- 9.4 系统配置与审计
CREATE TABLE system_config (
  cfg_key VARCHAR(64) PRIMARY KEY COMMENT '配置键',
  cfg_value TEXT NOT NULL COMMENT '配置值(JSON/字符串)',
  remark VARCHAR(128) NULL COMMENT '备注',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='系统配置（含开关与限制）';

CREATE TABLE ip_acl (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  type ENUM('whitelist','blacklist') NOT NULL COMMENT '类型',
  cidr VARCHAR(64) NOT NULL COMMENT 'CIDR/IP 段',
  remark VARCHAR(128) NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='IP 访问控制';

CREATE TABLE audit_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  user_id BIGINT NULL COMMENT '操作者',
  action VARCHAR(64) NOT NULL COMMENT '动作',
  resource VARCHAR(128) NOT NULL COMMENT '资源',
  detail TEXT NULL COMMENT '详情JSON',
  ip VARCHAR(64) NULL COMMENT '来源IP',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间'
) ENGINE=InnoDB COMMENT='审计日志';
```

> 说明：MySQL 5.7 无有效 CHECK 约束，时间限制与角色豁免通过应用层校验并在审计日志记录。

## 10. 路由与接口设计
- 路由模式：`/index.php?r=module.controller.action` 或开启 `.htaccess` 的伪静态：`/module/controller/action`。
- 鉴权：登录态 Session + CSRF Token（POST 必带）。

### 10.1 认证
- POST /auth/login：username, password → 设置 Session。
- POST /auth/logout：销毁 Session。

### 10.2 用户与角色
- GET /users/list
- POST /users/create | /users/update | /users/delete
- GET /roles/list
- POST /roles/assign

### 10.3 系统配置
- GET /config/get
- POST /config/save（系统名称/版本、会话超时、显示开关、时间限制、IP ACL 等）

### 10.4 菜单
- GET /menus/tree（按角色过滤）
- POST /menus/save（支持新增/编辑/排序/可见）

### 10.5 报表
- GET /reports/list | /reports/detail
- POST /reports/create（定义字段 JSON）
- POST /reports/update | /reports/delete

### 10.6 录入/查询/导出
- POST /entries/save（含分时段/整日，校验未来时间与限制天数）
- GET /entries/query（条件：设备/泵/日期区间/报表）
- GET /entries/export (Excel)，返回 xlsx（PhpSpreadsheet）。

## 11. 关键校验逻辑（伪代码）
```php
// 录入时间校验（节选）
$now = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
$start = new DateTime($entry_date . ' ' . $time_slot_start, new DateTimeZone('Asia/Shanghai'));
$limitDays = (int)$cfg['time_limit_days'] ?? 2;
if ($start > $now) throw new BizException('不能录入未来时间');
if (!user()->hasAnyRole(['admin','mod']) && (int)$now->diff(new DateTime($entry_date))->days > $limitDays) {
  throw new BizException('已超过允许录入的自然日范围');
}
```

## 12. 前端页面结构
- 顶部：系统名称（配置项）、用户信息下拉（个人资料/退出）。
- 侧栏：1/2/3 级菜单（系统设置、资料录入/查询-现场/CB26/中控）。
- 主区：
  - 录入页：
    - 选择设备 → 泵号（多选）→ 日期（默认当日）→ 时间段（00:00 开始，按 2 小时为例可配置）
    - 表格按示意图布局，尾行显示“录入人/录入时间/修改人/修改时间”（显示开关从系统配置读取）
    - 查询、保存、整日保存、导出（查询页无保存）
  - 查询页：同布局但只读，支持导出。
- 交互顺序（JS 加载顺序）：jQuery → 第三方 → 自定义。

## 13. Excel 导出（PhpSpreadsheet）
```php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->fromArray($header, NULL, 'A1');
$sheet->fromArray($rows, NULL, 'A2');
$writer = new Xlsx($spreadsheet);
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="export.xlsx"');
$writer->save('php://output');
```

## 14. 安全设计
- 密码：`password_hash`/`password_verify`，禁止明文；支持强度校验与多次失败锁定。
- 会话：`session.cookie_httponly=1`，`cookie_secure` 按协议自动；会话超时从系统配置读取（默认 7 天）。
- CSRF：表单隐藏字段 + 服务端校验。
- XSS/SQL 注入：输出转义、PDO 预处理、统一输入验证器（白名单/规则）。
- IP ACL：在中间件中拦截 `ip_acl`，模式：白名单优先于黑名单。
- 审计：所有管理与数据写操作入表 `audit_logs`。

## 15. 日志与错误处理
- 运行日志：/logs/app-YYYYMMDD.log，按天切割。
- 错误处理：注册 `set_error_handler` / `set_exception_handler`，输出友好 JSON/HTML，记录详细堆栈至日志。
- 导出/导入失败、权限拒绝、校验失败均写审计日志。

## 16. 部署与运维
- Apache 站点根指向 `/bbgl/`，如启用伪静态，`.htaccess` 示例：
```apache
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /bbgl/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^ index.php [L]
</IfModule>
```
- PHP 配置：`memory_limit=256M`；`post_max_size` 与 `upload_max_filesize` 视导出规模设置。
- 定时任务：可选-对超时修改标记进行巡检、日志清理。

## 17. 初始数据建议
- 角色：admin(管理员)、mod(普通管理员)、user(普通用户)、site(现场)、ctrl(中控)、cb26(CB26)。
- 权限：menus.view、users.manage、reports.manage、entries.create、entries.query、entries.export 等。
- 菜单：按七个一级菜单 + 二级（系统配置/用户管理/报表配置；设备运行资料/每周/每班次/每月/其他 等）。

## 18. 开发规范
- PHP 严格类型、PSR-4 自动加载、命名空间：`App\\*`。
- 控制器返回统一 `ApiResponse`，分页参数 `page/size`，时间统一传 ISO8601/`YYYY-MM-DD`。
- 文件/路径命名使用小写中划线或驼峰，禁止中文路径；所有静态资源通过 BASE_URL 引用。
- 代码注释中文，关键表/字段均含中文备注。

## 19. 里程碑
1) 架构与数据库落库 → 2) 认证/菜单/RBAC → 3) 资料录入/查询 → 4) 导出 → 5) 系统配置/IP ACL/日志 → 6) 界面风格优化。

## 20. 验收清单
- [ ] 资源引用全部使用 BASE_URL，顺序正确；字体在 /css/webfonts/
- [ ] HTML UTF-8；PHP 时区 Asia/Shanghai；Composer 自动加载
- [ ] RBAC 正确控制菜单与接口
- [ ] 录入校验：不能未来、超期限制（管理员豁免）；记录录入/修改人时间
- [ ] 查询页与录入页一致（无编辑）；Excel 导出可用
- [ ] 系统配置可调整显示开关、会话超时、时间限制、IP ACL
- [ ] 完整日志与审计日志

---

附：示例配置键
```json
{
  "system_name": "设备资料录入管理系统",
  "system_version": "1.0.0",
  "session_ttl_days": 7,
  "time_limit_days": 2,
  "show_entry_meta": true,
  "ip_acl_mode": "disabled"  // disabled|whitelist|blacklist
}
```



## 21. 运行保养记录模板说明（新增）

本节完善两类“运行保养记录”资料模板的字段、单位、校验与导出规则，并明确：这两类模板的最右侧均新增一列“备注”。

### 21.0 通用约定
- 时间段：默认按 2 小时一个时段（00:00–02:00, 02:00–04:00, …, 22:00–24:00），可在“报表配置→总体设置”中修改步长。
- 录入/显示：
  - “运行时间(h)”默认等于时段长度（例如 2h），如有停机则允许录入 0–2 之间的半小时刻度（0/0.5/1/1.5/2）。
  - “保养累计时数(一保/二保)”与“总累计时间(h)”为累计量，系统在保存时做非减校验（不得倒退）；如累计量小于上一时段值则拒绝保存。
  - 两模板的最右列固定为“备注”（自由文本，0–120 字；不参与任何计算），Excel 导出亦包含该列。
- 审计：保存或超时修改时写入 audit_logs（action: entries.save / entries.update）。

### 21.1 空压机运行保养记录模板
- 适用：空压机（示例：中心三号生产平台3#空气压缩机）。
- 页眉信息：
  - 空压机位号（必填，选择器）
  - 设备名称/位置信息（可显示只读）
  - 日期（默认当天，可按权限与时间限制规则修改）
- 列定义（从左到右）：
  1) 时段：string，格式“HH:mm~HH:mm”；系统自动生成。
  2) 压缩机出口压力：number，单位 MPa，范围 0–2，保留 2 位小数。
  3) 转子出口温度：number，单位 ℃，范围 0–120，整数或 1 位小数。
  4) 空冷器排气温度：number，单位 ℃，范围 0–120。
  5) 过滤器差压：number，单位 kPa，范围 0–100，保留 1 位小数。
  6) 运行时间：number，单位 h，范围 0–2，步长 0.5；默认 2。
  7) 保养累计时数-一保：integer，单位 h，非减；可由系统根据“运行时间”自动+本时段运行时长后给出建议值，人工可修改但不得小于上一时段。
  8) 保养累计时数-二保：integer，单位 h，非减；同上。
  9) 总累计时间：integer，单位 h，非减；建议=上一时段“总累计时间”+本时段“运行时间”。
  10) 备注：string，0–120 字，允许中英文与数字符号。
- 计算/校验要点：
  - 禁止录入未来时段数据；历史录入遵循“时间限制天数”。
  - 累计字段（7/8/9）不得小于上一时段同列值；如上一时段为空视为从 0 开始。
- 导出（Excel）列头示例：
```
["时段","压缩机出口压力(MPa)","转子出口温度(℃)","空冷器排气温度(℃)","过滤器差压(kPa)","运行时间(h)","保养累计时数(一保)","保养累计时数(二保)","总累计时间(h)","备注"]
```
- entries.data_json 每行示例：
```json
{
  "time_slot": "00:00-02:00",
  "out_pressure_mpa": 1.03,
  "rotor_out_temp_c": 87,
  "aircool_exhaust_temp_c": 37,
  "filter_dp_kpa": 0.1,
  "run_hours": 2,
  "maint_hours": {"primary": 958, "secondary": 2665},
  "total_hours": 15307,
  "remark": "运行平稳"
}
```

### 21.2 平台泵类设备运行保养记录模板
- 适用：平台泵类（示例：4#调水泵）。
- 页眉信息：
  - 泵类名称（必填，选择器）
  - 日期（默认当天）
  - 页码信息（多页表时自动）
- 列定义（从左到右）：
  1) 工作时间段：string，格式“HH:00–HH+2:00”。
  2) 开泵时间：time，可为空；格式 HH:mm。
  3) 停泵时间：time，可为空；格式 HH:mm。
  4) 电压：integer，单位 V，范围 300–500。
  5) 电流：integer，单位 A，范围 0–600。
  6) 泵压：number，单位 MPa，范围 0–2，保留 2 位小数。
  7) 干压：number，单位 MPa，范围 0–2，保留 2 位小数。
  8) 排量：integer，单位 m³/h，范围 0–5000。
  9) 轴承温度-泵-前：integer，单位 ℃，范围 0–120。
  10) 轴承温度-泵-后：integer，单位 ℃，范围 0–120。
  11) 轴承温度-电机-前：integer，单位 ℃，范围 0–120。
  12) 轴承温度-电机-后：integer，单位 ℃，范围 0–120。
  13) 保养累计时数-一保：integer，单位 h，非减。
  14) 保养累计时数-二保：integer，单位 h，非减。
  15) 总累计时间：integer，单位 h，非减。
  16) 备注：string，0–120 字。
- 补充说明：
  - 当“开泵时间/停泵时间”均为空时，默认视为该时段持续运行，“运行时间(h)=时段长度”；若仅开或停时间存在，则按照该时段内有效运行时长折算（0/0.5/1/1.5/2）。
  - 报表展示“运行时间(h)”可选显示在表格内或仅用于计算（在本模板中不必作为独立列出现，计算用于累计时数建议值）。
- 导出（Excel）列头示例：
```
["工作时间段","开泵时间","停泵时间","电压(V)","电流(A)","泵压(MPa)","干压(MPa)","排量(m³/h)","轴承温度-泵-前(℃)","轴承温度-泵-后(℃)","轴承温度-电机-前(℃)","轴承温度-电机-后(℃)","保养累计时数(一保)","保养累计时数(二保)","总累计时间(h)","备注"]
```
- entries.data_json 每行示例：
```json
{
  "time_slot": "00:00-02:00",
  "start_time": "00:00",
  "stop_time": "02:00",
  "voltage_v": 387,
  "current_a": 238,
  "pump_pressure_mpa": 0.65,
  "dry_pressure_mpa": 0.56,
  "flow_m3h": 636,
  "bearing_temp": {"pump": {"front": 46, "rear": 40}, "motor": {"front": 39, "rear": 36}},
  "maint_hours": {"primary": 234, "secondary": 546},
  "total_hours": 24417,
  "remark": "轴承温升正常"
}
```

### 21.3 前端表格与导出联动规则
- UI 表格：以上两模板在最右侧均固定显示“备注”列；列宽建议 ≥ 180px，移动端合并为折叠详情项。
- 导出：Excel 按上面的列头顺序输出，最右列“备注”不可省略；若无值输出空字符串。
- 导入（如启用）：允许备注携带任意可见字符，不做数值校验。

### 21.4 报表定义（fields_json）示例
> 在“报表配置→新建报表”中，建议按以下字段定义保存，系统将据此渲染表头与校验。

- 空压机模板（简化示例）：
```json
[
  {"key":"out_pressure_mpa","label":"压缩机出口压力(MPa)","type":"number","precision":2,"min":0,"max":2},
  {"key":"rotor_out_temp_c","label":"转子出口温度(℃)","type":"number","min":0,"max":120},
  {"key":"aircool_exhaust_temp_c","label":"空冷器排气温度(℃)","type":"number","min":0,"max":120},
  {"key":"filter_dp_kpa","label":"过滤器差压(kPa)","type":"number","precision":1,"min":0,"max":100},
  {"key":"run_hours","label":"运行时间(h)","type":"number","precision":1,"min":0,"max":2,"step":0.5},
  {"key":"maint_p","label":"保养累计时数(一保)","type":"integer","min":0},
  {"key":"maint_s","label":"保养累计时数(二保)","type":"integer","min":0},
  {"key":"total_hours","label":"总累计时间(h)","type":"integer","min":0},
  {"key":"remark","label":"备注","type":"string","maxLength":120}
]
```
- 泵类模板（简化示例）：
```json
[
  {"key":"start_time","label":"开泵时间","type":"time"},
  {"key":"stop_time","label":"停泵时间","type":"time"},
  {"key":"voltage_v","label":"电压(V)","type":"integer","min":0,"max":1000},
  {"key":"current_a","label":"电流(A)","type":"integer","min":0,"max":1000},
  {"key":"pump_pressure_mpa","label":"泵压(MPa)","type":"number","precision":2,"min":0,"max":2},
  {"key":"dry_pressure_mpa","label":"干压(MPa)","type":"number","precision":2,"min":0,"max":2},
  {"key":"flow_m3h","label":"排量(m³/h)","type":"integer","min":0,"max":10000},
  {"key":"bt_pump_front","label":"轴承温度-泵-前(℃)","type":"integer","min":0,"max":200},
  {"key":"bt_pump_rear","label":"轴承温度-泵-后(℃)","type":"integer","min":0,"max":200},
  {"key":"bt_motor_front","label":"轴承温度-电机-前(℃)","type":"integer","min":0,"max":200},
  {"key":"bt_motor_rear","label":"轴承温度-电机-后(℃)","type":"integer","min":0,"max":200},
  {"key":"maint_p","label":"保养累计时数(一保)","type":"integer","min":0},
  {"key":"maint_s","label":"保养累计时数(二保)","type":"integer","min":0},
  {"key":"total_hours","label":"总累计时间(h)","type":"integer","min":0},
  {"key":"remark","label":"备注","type":"string","maxLength":120}
]
```


## 22. 前端 UI 规范（Apple 高级风格）

本章定义登录页、导航/面包屑、表格/表单、动画交互与统一消息提示的 UI 规范与实现要点，作为全项目风格基线。

### 22.1 登录页（专属美化）
- 结构：渐变整页背景 + 居中卡片（radius 16px，阴影轻）+ 品牌标识（渐变方块）
- 输入：圆角 10px，微阴影；表单纵向 16px 间距
- 行为：
  - 记住账号（Cookie: remember_username，30 天）
  - 记住密码（Cookie: remember_password，30 天；生产建议改为 remember token）
- 实现文件：
  - app/Views/auth/login.php（页面模板）
  - css/app.css（.login-page, .login-card, .form-control-elev 等）

### 22.2 导航当前项高亮与面包屑
- 侧栏：
  - 菜单项 .menu .item 圆角 10px，hover 背景 #f3f4f6
  - 支持折叠/展开（桌面端）与抽屉（移动端），按钮 #sidebarToggle
- 页头：
  - .page-head 包含 .page-title 与 .breadcrumb
  - 标题字号 20px/600；面包屑色 var(--muted)

### 22.3 表格与表单主题
- 按钮：.btn 统一圆角 10px，active 态轻微下压（translateY(1px)）
- 输入：.form-control/.form-select 统一圆角 10px，focus 态 3px 浅蓝外发光
- 卡片：统一边框色 var(--border) 与轻阴影

### 22.4 动画微交互
- 统一过渡：.15s ease，覆盖按钮/输入/菜单项
- 焦点与点击态：focus 外发光、active 轻下压

### 22.5 统一消息提示样式
- 使用 Bootstrap alert 规范，结合以下约定：
  - 成功：alert-success；错误：alert-danger；警告：alert-warning；信息：alert-info
  - 置顶全局提示区域：页面主内容首部（页头后）
  - 交互反馈尽量 3s 自动淡出（后续在 js/app.js 增加）

### 22.6 可访问性与响应式
- 颜色对比遵循 WCAG AA；交互控件支持键盘导航
- 移动端 ≤991px：侧栏默认隐藏，点击汉堡按钮展开（.sidebar.show）

> 本规范已落地到当前代码：header/sidebar/footer/login.css/js；后续新页面应遵循本规范开发。

> 以上定义确保：两模板在录入与导出时，最右列恒为“备注”，满足你提出的“所有的最后一列增加一个备注”的要求。