# 系统配置和用户管理使用指南

> 版本：v1.0 | 更新时间：2025-08-28

## 概述

本文档介绍设备资料录入管理系统中系统配置和用户管理功能的使用方法。这些功能仅限管理员用户使用。

## 功能特性

### 系统配置功能
- ✅ 系统基本信息配置（名称、版本）
- ✅ 会话超时设置
- ✅ 历史数据录入限制配置
- ✅ 录入人信息显示开关
- ✅ IP访问控制模式配置
- ✅ 配置变更审计日志

### 用户管理功能
- ✅ 用户创建、编辑、删除
- ✅ 用户状态管理（启用/禁用）
- ✅ 角色分配管理
- ✅ 密码管理
- ✅ 用户操作审计日志

### IP访问控制功能
- ✅ 白名单/黑名单模式
- ✅ 支持单个IP和CIDR网段
- ✅ IP规则管理
- ✅ 访问拒绝日志记录

## 安装和初始化

### 1. 数据库表初始化

首先需要创建系统配置相关的数据库表：

```bash
# 在项目根目录执行
php database/init_system_tables.php
```

### 2. 功能测试

运行测试脚本验证功能是否正常：

```bash
# 在项目根目录执行
php scripts/test_system_features.php
```

## 使用指南

### 访问系统配置

1. 使用管理员账号登录系统
2. 在左侧菜单中点击"系统设置" → "系统配置"
3. 进入系统配置页面

### 系统配置项说明

| 配置项 | 说明 | 默认值 | 备注 |
|--------|------|--------|------|
| 系统名称 | 显示在页面标题和登录页的系统名称 | 设备资料录入管理系统 | 修改后立即生效 |
| 系统版本 | 当前系统版本号 | 1.0.0 | 用于版本标识 |
| 会话超时（天） | 用户登录后的会话有效期 | 7 | 1-30天 |
| 历史录入限制（天） | 普通用户可录入历史数据的天数 | 2 | 管理员不受限制 |
| 显示录入人信息 | 是否在数据表格中显示录入人信息 | 是 | 包括录入人、时间等 |
| IP访问控制 | IP访问控制模式 | 禁用 | 禁用/白名单/黑名单 |

### 用户管理操作

#### 创建用户

1. 在用户管理页面点击"新增用户"按钮
2. 填写用户信息：
   - **用户名**：登录账号（必填，唯一）
   - **姓名**：用户真实姓名（必填）
   - **邮箱**：用户邮箱（可选）
   - **手机号**：用户手机号（可选）
   - **密码**：登录密码（必填）
   - **状态**：启用/禁用
   - **角色分配**：选择用户角色
3. 点击"保存"完成创建

#### 编辑用户

1. 在用户列表中点击用户行的"编辑"按钮
2. 修改用户信息
3. 密码字段留空表示不修改密码
4. 点击"保存"完成修改

#### 删除用户

1. 在用户列表中点击用户行的"删除"按钮
2. 确认删除操作
3. 注意：不能删除当前登录的用户

### IP访问控制管理

#### 启用IP访问控制

1. 在系统配置页面设置"IP访问控制"为"白名单模式"或"黑名单模式"
2. 点击"IP访问控制管理"按钮进入IP规则管理页面

#### 添加IP规则

1. 点击"新增规则"按钮
2. 选择规则类型：
   - **白名单**：仅允许列表中的IP访问
   - **黑名单**：禁止列表中的IP访问
3. 输入IP地址或网段：
   - 单个IP：`*************`
   - CIDR网段：`***********/24`
4. 填写备注信息（可选）
5. 点击"保存"完成添加

#### IP访问控制模式说明

- **禁用模式**：不进行IP限制，所有IP都可以访问
- **白名单模式**：只允许白名单中的IP地址访问系统
- **黑名单模式**：禁止黑名单中的IP地址访问系统
- **优先级**：白名单优先于黑名单

## 安全注意事项

### IP访问控制安全

1. **谨慎配置白名单**：确保管理员IP在白名单中，避免被锁定
2. **测试访问**：配置后使用其他IP测试访问控制是否生效
3. **备用访问**：保留至少一个可信IP用于紧急访问

### 用户管理安全

1. **强密码策略**：为用户设置复杂密码
2. **最小权限原则**：只分配必要的角色权限
3. **定期审查**：定期检查用户账号和权限分配
4. **及时禁用**：离职人员账号及时禁用

### 审计日志

系统会自动记录以下操作的审计日志：

- 系统配置修改
- 用户创建、编辑、删除
- IP访问控制规则变更
- 访问被拒绝的记录

## 故障排除

### 常见问题

**Q: 修改系统配置后没有生效？**
A: 检查是否有错误提示，确认配置保存成功。部分配置可能需要重新登录生效。

**Q: 用户无法登录？**
A: 检查用户状态是否为"启用"，密码是否正确，是否被IP访问控制拦截。

**Q: 被IP访问控制锁定怎么办？**
A: 可以通过数据库直接修改`system_config`表中的`ip_acl_mode`配置为`"disabled"`来临时禁用IP控制。

**Q: 忘记管理员密码？**
A: 可以通过数据库直接重置密码，或使用`database/init_admin.sql`脚本重新创建管理员账号。

### 数据库直接操作

如需紧急修改配置，可直接操作数据库：

```sql
-- 禁用IP访问控制
UPDATE system_config SET cfg_value = '"disabled"' WHERE cfg_key = 'ip_acl_mode';

-- 重置管理员密码为 admin123
UPDATE users SET password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE id = 1;
```

## 技术支持

如遇到技术问题，请：

1. 查看系统日志文件（`logs/`目录）
2. 检查审计日志记录
3. 运行测试脚本诊断问题
4. 联系系统管理员或开发团队
