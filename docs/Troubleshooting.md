# 故障排除指南

## 常见问题诊断

### 1. 数据显示问题

#### 症状：页面显示空白，但数据库有数据
**可能原因**：
- API查询条件错误
- 前端数据解析问题
- 权限验证失败

**诊断步骤**：
```bash
# 1. 检查数据库数据
mysql -u username -p -e "SELECT COUNT(*) FROM report_entries WHERE report_id=1002 AND device_id=13;"

# 2. 测试API接口
curl "http://localhost/api/entry/load?report_id=1002&device_id=13&month=2025-09"

# 3. 检查浏览器控制台错误
# 打开F12开发者工具，查看Console和Network标签
```

**解决方案**：
- 检查查询条件是否正确匹配数据库字段
- 验证API返回的JSON格式
- 确认用户权限和登录状态

#### 症状：设备配置保存后丢失原有配置
**根本原因**：前端只收集当前界面显示的设备，未包含原有配置

**解决方案**：
```javascript
// 确保保存时包含所有设备（原有+新增）
function saveDeviceConfig() {
    var items = document.querySelectorAll('#configuredDevices .d-flex');
    var devices = [];
    items.forEach(function(item) {
        var deviceId = item.getAttribute('data-device');
        var pumpId = item.getAttribute('data-pump');
        devices.push({
            device_id: parseInt(deviceId),
            pump_id: pumpId ? parseInt(pumpId) : null
        });
    });
    // 发送完整的设备列表到后端
}
```

### 2. 数据类型问题

#### 症状：JavaScript错误 "xxx.includes is not a function"
**原因**：代码期望字符串类型，但实际是数字类型

**解决方案**：
```javascript
// 添加类型检查
function processData(value) {
    // 确保是字符串类型
    if (typeof value !== 'string') {
        value = String(value || '');
    }
    
    // 现在可以安全使用字符串方法
    if (value.includes('keyword')) {
        // 处理逻辑
    }
}
```

### 3. 数据库查询问题

#### 症状：查询返回空结果
**常见原因**：
- 查询条件过于严格
- 字段名错误
- 数据类型不匹配

**诊断方法**：
```php
// 添加调试日志
error_log("Query params: reportId=$reportId, deviceId=$deviceId");

// 分步验证查询条件
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM report_entries WHERE report_id = ?");
$stmt->execute([$reportId]);
$total = $stmt->fetch()['count'];
error_log("Total entries for report: $total");

$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM report_entries WHERE report_id = ? AND device_id = ?");
$stmt->execute([$reportId, $deviceId]);
$filtered = $stmt->fetch()['count'];
error_log("Filtered entries: $filtered");
```

## 性能问题

### 1. 查询慢
**优化策略**：
```sql
-- 添加复合索引
CREATE INDEX idx_report_entries_query ON report_entries(report_id, device_id, entry_date);

-- 分析查询计划
EXPLAIN SELECT * FROM report_entries WHERE report_id = 1002 AND device_id = 13;
```

### 2. 前端加载慢
**优化方案**：
- 实现分页加载
- 使用数据缓存
- 减少不必要的API调用

```javascript
// 实现简单缓存
const dataCache = new Map();

function loadData(key) {
    if (dataCache.has(key)) {
        return Promise.resolve(dataCache.get(key));
    }
    
    return fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            dataCache.set(key, data);
            return data;
        });
}
```

## 开发环境问题

### 1. 数据库连接失败
**检查清单**：
- [ ] 数据库服务是否启动
- [ ] 连接参数是否正确
- [ ] 用户权限是否足够
- [ ] 防火墙设置

**测试连接**：
```php
try {
    $pdo = new PDO("mysql:host=127.0.0.1;port=3306;dbname=test", $username, $password);
    echo "连接成功\n";
} catch (PDOException $e) {
    echo "连接失败: " . $e->getMessage() . "\n";
}
```

### 2. 路由不工作
**常见原因**：
- URL重写未配置
- .htaccess文件缺失
- 路由注册错误

**Apache配置**：
```apache
# .htaccess
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?r=$1 [QSA,L]
```

## 日志分析

### 1. 启用详细日志
```php
// 在关键位置添加日志
error_log("EntryController::loadContinuousEntries - Start");
error_log("Parameters: " . json_encode($_GET));
error_log("Query result count: " . count($entries));
```

### 2. 前端调试
```javascript
// 启用详细控制台输出
console.log('API Request:', {
    url: apiUrl,
    params: params,
    timestamp: new Date().toISOString()
});

console.log('API Response:', result);
```

### 3. 数据库查询日志
```sql
-- 启用查询日志（MySQL）
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/var/log/mysql/query.log';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

## 紧急修复流程

### 1. 数据丢失
**立即行动**：
1. 停止相关操作
2. 检查数据库备份
3. 分析日志找出原因
4. 制定恢复计划

### 2. 系统无法访问
**检查顺序**：
1. 服务器状态
2. 数据库连接
3. 应用程序日志
4. 网络连接

### 3. 数据不一致
**处理步骤**：
1. 备份当前数据
2. 分析不一致的原因
3. 编写修复脚本
4. 在测试环境验证
5. 执行修复

## 预防措施

### 1. 代码审查清单
- [ ] 数据库查询使用预处理语句
- [ ] 前端输入验证
- [ ] 错误处理完整
- [ ] 类型检查到位
- [ ] 事务使用正确

### 2. 测试覆盖
- [ ] 单元测试
- [ ] 集成测试
- [ ] 边界条件测试
- [ ] 性能测试

### 3. 监控告警
- [ ] 数据库性能监控
- [ ] 应用程序错误监控
- [ ] 用户行为分析
- [ ] 系统资源监控

## 联系支持

如果遇到无法解决的问题，请提供以下信息：
1. 问题详细描述
2. 重现步骤
3. 错误日志
4. 系统环境信息
5. 相关截图

**日志收集命令**：
```bash
# 收集系统信息
php -v > debug_info.txt
mysql --version >> debug_info.txt

# 收集应用日志
tail -n 100 logs/error.log >> debug_info.txt
tail -n 100 logs/router.log >> debug_info.txt
```
