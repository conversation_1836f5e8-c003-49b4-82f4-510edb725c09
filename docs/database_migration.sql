-- 数据库改造：支持多种报表录入模板
-- 执行顺序：按注释编号依次执行

-- 1. 为 reports 表添加录入模板字段
ALTER TABLE reports 
ADD COLUMN template_code VARCHAR(50) NOT NULL DEFAULT 'pumps' 
COMMENT '录入模板类型：pumps=泵类, compressor=空压机, fan=风机, motor=电机, custom=自定义'
AFTER category_id;

-- 2. 规范化 report_entries 表：pump_id → object_id
-- 先添加新列
ALTER TABLE report_entries 
ADD COLUMN object_id INT NULL 
COMMENT '关联对象ID：泵类时为pump.id，空压机时为device.id'
AFTER pump_id;

-- 3. 数据迁移：将现有 pump_id 数据复制到 object_id
UPDATE report_entries SET object_id = pump_id WHERE pump_id IS NOT NULL;

-- 4. 规范化 report_devices 表：pump_id → object_id  
ALTER TABLE report_devices 
ADD COLUMN object_id INT NULL 
COMMENT '关联对象ID：泵类时为pump.id，空压机等时为device.id'
AFTER pump_id;

-- 5. 数据迁移：将现有 pump_id 数据复制到 object_id
UPDATE report_devices SET object_id = pump_id WHERE pump_id IS NOT NULL;

-- 6. 为空压机等设备类型的报表配置添加索引
ALTER TABLE report_devices 
ADD INDEX idx_report_device_object (report_id, device_id, object_id);

-- 7. 更新现有报表的模板类型（可选，根据实际情况调整）
-- UPDATE reports SET template_code = 'pumps' WHERE id IN (1001, 1002);
-- UPDATE reports SET template_code = 'compressor' WHERE name LIKE '%空压机%';

-- 8. 创建模板配置表（可选，用于后续扩展自定义模板）
CREATE TABLE IF NOT EXISTS report_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板代码',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    fields_config JSON NOT NULL COMMENT '字段配置JSON',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '报表录入模板配置';

-- 9. 插入预定义模板
INSERT INTO report_templates (code, name, fields_config) VALUES 
('pumps', '泵类设备', '{"fields":[
    {"key":"start_time","label":"开泵时间","type":"time"},
    {"key":"stop_time","label":"停泵时间","type":"time"},
    {"key":"voltage_v","label":"电压(V)","type":"number","step":"0.1"},
    {"key":"current_a","label":"电流(A)","type":"number","step":"0.1"},
    {"key":"frequency_hz","label":"频率(Hz)","type":"number","step":"0.1"},
    {"key":"flow_rate","label":"流量","type":"number","step":"0.1"},
    {"key":"pressure","label":"压力","type":"number","step":"0.01"},
    {"key":"temperature","label":"温度","type":"number","step":"0.1"},
    {"key":"remark","label":"备注","type":"text"}
]}'),
('compressor', '空压机设备', '{"fields":[
    {"key":"outlet_pressure","label":"压缩机出口压力(MPa)","type":"number","step":"0.01"},
    {"key":"outlet_temp","label":"转子出口温度(℃)","type":"number","step":"1"},
    {"key":"cooling_temp","label":"空冷器排气温度(℃)","type":"number","step":"1"},
    {"key":"filter_pressure","label":"过滤器差压(KPa)","type":"number","step":"0.1"},
    {"key":"runtime_hours","label":"运行时间(h)","type":"number","step":"0.1"},
    {"key":"maintenance_count_1","label":"保养累计时间一保(h)","type":"number","step":"1"},
    {"key":"maintenance_count_2","label":"保养累计时间二保(h)","type":"number","step":"1"},
    {"key":"total_runtime","label":"总累计时间(h)","type":"number","step":"1"},
    {"key":"operator","label":"值班人员","type":"text"},
    {"key":"remark","label":"备注","type":"text"}
]}');

-- 10. 验证数据完整性
-- SELECT COUNT(*) as total_entries FROM report_entries;
-- SELECT COUNT(*) as migrated_entries FROM report_entries WHERE object_id IS NOT NULL;
-- SELECT COUNT(*) as total_devices FROM report_devices;
-- SELECT COUNT(*) as migrated_devices FROM report_devices WHERE object_id IS NOT NULL;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 在生产环境执行时，建议分批执行大表的UPDATE操作
-- 3. 迁移完成后，可以考虑删除旧的 pump_id 列（但建议保留一段时间作为备份）
-- 4. 新的 object_id 列暂时允许 NULL，便于渐进式迁移
