<?php
// 设备资料录入管理系统 - 入口文件
// 严格类型与基础设置
declare(strict_types=1);
ini_set('display_errors', '1');
error_reporting(E_ALL);

// 时区统一为北京时间
date_default_timezone_set('Asia/Shanghai');

// Composer 本地依赖（如不存在则忽略，以便骨架可运行）
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require $autoload;
}

// 计算 BASE_URL，固定到 /bbgl/ 根
$scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
// 注意：路径大小写严格匹配，且不得包含中文
if (!defined('BASE_URL')) {
    define('BASE_URL', $scheme . '://' . $host . '/bbgl/');
}

require __DIR__ . '/config/bootstrap.php';

// 使用新的路由分发器处理前后端分离
\App\Core\RouteDispatcher::dispatch();

