// 设备资料录入管理系统 - 前端脚本

// 通用工具函数
function fetchJSON(url){ return fetch(url,{credentials:'same-origin'}).then(r=>r.json()); }
function postJSON(url, body){ return fetch(url,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(body),credentials:'same-origin'}).then(r=>r.json()); }

// 滚动位置保持功能
(function() {
    // 保存滚动位置
    function saveScrollPosition() {
        sessionStorage.setItem('scrollPosition', window.pageYOffset || document.documentElement.scrollTop);
    }

    // 恢复滚动位置
    function restoreScrollPosition() {
        const savedPosition = sessionStorage.getItem('scrollPosition');
        if (savedPosition) {
            window.scrollTo(0, parseInt(savedPosition));
            sessionStorage.removeItem('scrollPosition');
        }
    }

    // 页面加载时恢复滚动位置
    document.addEventListener('DOMContentLoaded', function() {
        restoreScrollPosition();
    });

    // 页面卸载时保存滚动位置
    window.addEventListener('beforeunload', function() {
        saveScrollPosition();
    });

    // 监听菜单链接点击
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a[href]');
        if (link && link.href && !link.href.startsWith('javascript:')) {
            saveScrollPosition();
        }
    });
})();

// 防抖函数
function debounce(func, wait) {
  var timeout;
  return function executedFunction() {
    var context = this;
    var args = arguments;
    var later = function() {
      timeout = null;
      func.apply(context, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 平台泵类页面功能
function initPumpsPage() {
  if (!window.__PUMPS_API__) {
    return;
  }

  var apis = window.__PUMPS_API__;
  var sel = document.getElementById('pumpSelect');
  var dateInput = document.getElementById('entryDate');
  var table = document.querySelector('.table');
  var btnQuery = document.getElementById('btnQuery');



  if (sel && dateInput && table) {

    function gatherRows(onlyFilled){
      var result = {};
      table.querySelectorAll('tbody tr').forEach(function(tr){
        var slot = tr.getAttribute('data-slot');
        var row = {}; var filled=false;
        tr.querySelectorAll('input').forEach(function(inp){
          var key = inp.getAttribute('name').replace(/^.*\[(.*)\]$/, '$1');
          var val = inp.value.trim();
          // 数值类型转换
          if (inp.type === 'number' && val !== '') {
            val = parseFloat(val) || 0;
          }
          row[key]=val; if (val!=='' && val!==null && val!==0) filled=true;
        });
        if (!onlyFilled || filled) result[slot]=row;
      });
      return result;
    }

    // 表单验证增强
    function validateTimeSlot(tr) {
      var inputs = tr.querySelectorAll('input');
      var startTime = tr.querySelector('input[name*="[start_time]"]');
      var stopTime = tr.querySelector('input[name*="[stop_time]"]');
      var voltage = tr.querySelector('input[name*="[voltage_v]"]');
      var current = tr.querySelector('input[name*="[current_a]"]');

      // 清除之前的错误样式
      inputs.forEach(function(inp){ inp.classList.remove('is-invalid'); });

      var hasError = false;
      // 如果填了开泵时间，建议填停泵时间
      if (startTime && startTime.value && stopTime && !stopTime.value) {
        stopTime.classList.add('is-invalid');
        hasError = true;
      }
      // 如果填了电压，建议填电流
      if (voltage && voltage.value && current && !current.value) {
        current.classList.add('is-invalid');
        hasError = true;
      }
      return !hasError;
    }

    // 为输入框添加实时验证
    table.addEventListener('blur', function(e){
      if (e.target.tagName === 'INPUT') {
        var tr = e.target.closest('tr');
        if (tr) validateTimeSlot(tr);
      }
    }, true);

    function fillRows(map){
      var rows = table.querySelectorAll('tbody tr');
      var currentDate = dateInput.value;
      var now = new Date();

      // 批量处理DOM更新以提高性能
      var fragment = document.createDocumentFragment();

      rows.forEach(function(tr){
        var slot = tr.getAttribute('data-slot');
        var data = map[slot] || {};
        var inputs = tr.querySelectorAll('input');

        // 检查时间段是否可编辑
        var isEditable = checkTimeSlotEditable(slot, currentDate, now);

        // 批量更新输入框
        inputs.forEach(function(inp){
          var key = inp.getAttribute('name').replace(/^.*\[(.*)\]$/, '$1');
          var value = (data[key]!==undefined && data[key]!==null)? data[key] : '';

          // 只在值真正改变时更新
          if (inp.value !== value) {
            inp.value = value;
          }

          // 设置禁用状态
          if (inp.disabled !== !isEditable) {
            inp.disabled = !isEditable;
          }

          // 更新样式类
          if (!isEditable) {
            inp.classList.add('disabled-slot');
          } else {
            inp.classList.remove('disabled-slot');
          }
        });

        // 设置行的视觉状态
        if (!isEditable) {
          tr.classList.add('disabled-row');
        } else {
          tr.classList.remove('disabled-row');
        }
      });
    }

    function checkTimeSlotEditable(slot, date, now) {
      var parts = slot.split('-');
      var endTime = parts[1];
      var slotEnd = new Date(date + ' ' + endTime);

      // 未来时段不可编辑
      if (slotEnd > now) {
        return false;
      }

      // 检查历史时间限制（从服务器配置获取，默认2天）
      var timeLimitDays = window.__REPORT_CONFIG__ && window.__REPORT_CONFIG__.time_limit_days
        ? parseInt(window.__REPORT_CONFIG__.time_limit_days)
        : 2;

      // 严格检查管理员权限
      var isAdmin = window.__USER_ROLES__ && Array.isArray(window.__USER_ROLES__) && window.__USER_ROLES__.includes('admin');

      // 调试信息（开发环境）
      if (window.__DEBUG_MODE__) {
        console.log('时间限制检查:', {
          slot: slot,
          date: date,
          timeLimitDays: timeLimitDays,
          isAdmin: isAdmin,
          userRoles: window.__USER_ROLES__
        });
      }

      if (!isAdmin) {
        var limitDate = new Date(now);
        limitDate.setDate(limitDate.getDate() - timeLimitDays);
        var slotStart = new Date(date + ' ' + parts[0]);

        if (slotStart < limitDate) {
          if (window.__DEBUG_MODE__) {
            console.log('时间限制阻止:', {
              slotStart: slotStart.toISOString(),
              limitDate: limitDate.toISOString(),
              blocked: true
            });
          }
          return false;
        }
      }

      return true;
    }

    function save(onlyFilled){
      if (!apis.save) return;
      var body = {pump_id: sel.value, date: dateInput.value, rows: gatherRows(onlyFilled)};
      postJSON(apis.save, body).then(function(result){
        var msg = document.createElement('div');
        msg.className='alert position-fixed end-0 me-3'; msg.style.top='70px';
        if (result.success) {
          msg.classList.add('alert-success');
          msg.innerHTML = '保存成功：' + result.saved + ' 个时段';
          if (result.errors && result.errors.length) {
            msg.innerHTML += '<br><small>' + result.errors.slice(0,3).join('<br>') + '</small>';
          }
        } else {
          msg.classList.add('alert-danger');
          msg.textContent = '保存失败：' + (result.message || '未知错误');
        }
        document.body.appendChild(msg);
        setTimeout(function(){ if(msg.parentNode) msg.remove(); }, 4000);
        loadData();
      }).catch(function(err){
        var errMsg = document.createElement('div');
        errMsg.className='alert alert-danger position-fixed end-0 me-3'; errMsg.style.top='70px';
        errMsg.textContent='保存失败：网络错误'; document.body.appendChild(errMsg);
        setTimeout(function(){ if(errMsg.parentNode) errMsg.remove(); }, 3000);
      });
    }

    function loadData(){
      if (!apis.load) return;
      var pid = sel.value; var d = dateInput.value;
      if (!pid || !d) return;

      // 显示加载状态
      showLoadingState();

      fetchJSON(apis.load+'&pump_id='+encodeURIComponent(pid)+'&date='+encodeURIComponent(d))
        .then(function(data) {
          fillRows(data);
          hideLoadingState();
        })
        .catch(function(err) {
          console.error('加载数据失败:', err);
          hideLoadingState();
          showErrorMessage('加载数据失败，请重试');
        });
    }

    // 加载状态管理
    function showLoadingState() {
      var table = document.querySelector('#dataTable tbody');
      if (table) {
        table.style.opacity = '0.6';
        table.style.pointerEvents = 'none';
      }
    }

    function hideLoadingState() {
      var table = document.querySelector('#dataTable tbody');
      if (table) {
        table.style.opacity = '1';
        table.style.pointerEvents = 'auto';
      }
    }

    function showErrorMessage(message) {
      var msg = document.createElement('div');
      msg.className = 'alert alert-warning position-fixed end-0 me-3';
      msg.style.top = '70px';
      msg.style.zIndex = '9999';
      msg.textContent = message;
      document.body.appendChild(msg);
      setTimeout(function(){ if(msg.parentNode) msg.remove(); }, 3000);
    }

    function loadPumps(){
      if (!apis.list) {
        console.error('apis.list not found');
        return;
      }

      // 显示设备选择器加载状态
      sel.innerHTML = '<option>加载中...</option>';
      sel.disabled = true;

      fetchJSON(apis.list).then(function(items){
        sel.disabled = false;
        sel.innerHTML = items.map(function(it){
          return '<option value="'+it.pump_id+'">'+it.pump_no+it.device_name+'</option>';
        }).join('');
        if (items.length){ loadData(); }
      }).catch(function(err){
        console.error('Failed to load pumps:', err);
        sel.disabled = false;
        sel.innerHTML = '<option>加载失败，请刷新重试</option>';
        showErrorMessage('设备列表加载失败');
      });
    }

    // 创建防抖版本的loadData
    var debouncedLoadData = debounce(loadData, 300);

    // 事件绑定
    btnQuery && btnQuery.addEventListener('click', loadData);
    // 日期和设备变化时重新加载数据和检查时间段状态（使用防抖）
    sel && sel.addEventListener('change', debouncedLoadData);
    dateInput && dateInput.addEventListener('change', debouncedLoadData);
    // 键盘快捷保存：Ctrl+S 仅保存已填
    window.addEventListener('keydown', function(e){ if (e.ctrlKey && e.key.toLowerCase()==='s'){ e.preventDefault(); save(true);} });
    // 保存按钮事件
    var btnSaveFilled = document.getElementById('btnSaveFilled');
    var btnSaveAll = document.getElementById('btnSaveAll');
    btnSaveFilled && btnSaveFilled.addEventListener('click', function(){ save(true); });
    btnSaveAll && btnSaveAll.addEventListener('click', function(){ save(false); });

    // 初始化
    loadPumps();
  }
}

// 确保在 DOM 加载后执行
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', function() {
    // 只在有对应API配置的页面执行相应的初始化
    if (window.__PUMPS_API__) {
      initPumpsPage();
    }
    if (window.__REPORTS_API__) {
      initReportsPage();
    }
  });
} else {
  // 只在有对应API配置的页面执行相应的初始化
  if (window.__PUMPS_API__) {
    initPumpsPage();
  }
  if (window.__REPORTS_API__) {
    initReportsPage();
  }
}

// 报表管理页面功能
function initReportsPage() {
  if (!window.__REPORTS_API__) return;

  // 自动生成报表编码
  var nameInput = document.getElementById('reportName');
  var codeInput = document.getElementById('reportCode');

  if (nameInput && codeInput) {
    nameInput.addEventListener('input', function() {
      var name = this.value.trim();
      if (name) {
        // 生成编码：中文转拼音简化版 + 时间戳
        var code = generateReportCode(name);
        codeInput.value = code;
      } else {
        codeInput.value = '';
      }
    });
  }
}

// 生成报表编码
function generateReportCode(name) {
  // 简单的中文关键词映射
  var mapping = {
    '平台': 'platform',
    '泵': 'pump',
    '设备': 'device',
    '运行': 'operation',
    '保养': 'maintenance',
    '记录': 'record',
    '测试': 'test',
    '现场': 'site',
    '资料': 'data',
    '录入': 'entry',
    '查询': 'query',
    '管理': 'manage',
    '系统': 'system',
    '报表': 'report'
  };

  var code = '';
  for (var key in mapping) {
    if (name.includes(key)) {
      code += mapping[key] + '_';
    }
  }

  // 如果没有匹配到关键词，使用默认前缀
  if (!code) {
    code = 'report_';
  }

  // 添加时间戳确保唯一性
  var timestamp = Date.now().toString().slice(-6);
  code += timestamp;

  return code;
}

// 显示报表模态框
function showReportModal(reportId = null) {
  const modal = new bootstrap.Modal(document.getElementById('reportModal'));
  const title = document.getElementById('reportModalTitle');
  const form = document.getElementById('reportForm');

  if (reportId) {
    title.textContent = '编辑报表';
    loadReportData(reportId);
  } else {
    title.textContent = '新增报表';
    form.reset();
    document.getElementById('reportId').value = '';
    document.getElementById('reportCycleType').value = 'daily';
    updateTimeLimitOptions();
  }

  modal.show();
}
  // 导出到全局，便于模板内联 onclick 调用
  window.showReportModal = showReportModal;

// 编辑报表
function editReport(reportId) {
  showReportModal(reportId);
}

// 删除报表
function deleteReport(reportId){
  if(!confirm('确定要删除该报表及其关联配置与数据吗？此操作不可撤销。')) return;
  fetch(window.__REPORTS_API__.deleteReport, {
    method: 'POST', headers: {'Content-Type':'application/json'}, credentials:'same-origin',
    body: JSON.stringify({id: reportId})
  }).then(r=>r.json()).then(result=>{
    if(result.success){ alert(result.message||'删除成功'); location.reload(); }
    else { alert(result.message||'删除失败'); }
  }).catch(err=>{ console.error('删除失败:', err); alert('网络错误，删除失败'); });
}

// 加载报表数据
function loadReportData(reportId) {
  fetch(window.__REPORTS_API__.getReport + '&id=' + reportId, {credentials: 'same-origin'})
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        const data = result.data;
        document.getElementById('reportId').value = data.id;
        document.getElementById('reportName').value = data.name;
        document.getElementById('reportCode').value = data.code;
        document.getElementById('reportCategory').value = data.category_id || '';
        // 设置模板选择，确保选项存在
        const templateSelect = document.getElementById('reportTemplate');
        const templateCode = data.template_code || 'pumps';

        // 检查选项是否存在
        let optionExists = false;
        for (let i = 0; i < templateSelect.options.length; i++) {
          if (templateSelect.options[i].value === templateCode) {
            optionExists = true;
            break;
          }
        }

        if (optionExists) {
          templateSelect.value = templateCode;
        } else {
          console.warn('模板选项不存在:', templateCode);
          // 如果选项不存在，选择第一个可用选项
          if (templateSelect.options.length > 0) {
            templateSelect.selectedIndex = 0;
          }
        }
        document.getElementById('reportCycleType').value = data.cycle_type;
        document.getElementById('reportTimeLimitType').value = data.time_limit_type;
        document.getElementById('reportTimeLimitDays').value = data.time_limit_days;
        document.getElementById('reportEnabled').checked = data.enabled == 1;

        // 加载值班人员配置
        const dutyConfig = data.duty_staff_config ? JSON.parse(data.duty_staff_config) : {};
        document.getElementById('dutyStaffEnabled').checked = dutyConfig.enabled || false;
        document.getElementById('dutyDayLabel').value = dutyConfig.day_label || '白班值班人';
        document.getElementById('dutyNightLabel').value = dutyConfig.night_label || '夜班值班人';
        document.getElementById('dutyStaffRequired').checked = dutyConfig.required || false;

        // 切换值班人员配置显示
        toggleDutyStaffConfig();

        updateTimeLimitOptions();
        updateTimeLimitDays();
      } else {
        alert('加载报表数据失败：' + result.message);
      }
    })
    .catch(error => {
      console.error('Error loading report:', error);
      alert('加载报表数据失败');
    });
}

// 更新时间限制选项
function updateTimeLimitOptions() {
  const cycleType = document.getElementById('reportCycleType').value;
  const timeLimitType = document.getElementById('reportTimeLimitType');
  const currentValue = timeLimitType.value;

  // 清空选项
  timeLimitType.innerHTML = '';

  // 根据周期类型添加对应的时间限制选项
  const options = {
    'daily': [
      {value: 'hour', text: '小时级时效控制'},
      {value: 'day', text: '日级时效控制'},
      {value: 'week', text: '周级时效控制'},
      {value: 'month', text: '月级时效控制'},
      {value: 'unlimited', text: '无时效限制'}
    ],
    'weekly': [
      {value: 'week', text: '周级时效控制（当前周期内完成）'},
      {value: 'month', text: '月级时效控制'},
      {value: 'unlimited', text: '无时效限制'}
    ],
    'shift': [
      {value: 'month', text: '月级时效控制'},
      {value: 'unlimited', text: '无时效限制'}
    ],
    'monthly': [
      {value: 'month', text: '月级时效控制'},
      {value: 'unlimited', text: '无时效限制'}
    ],
    'irregular': [
      {value: 'day', text: '日级时效控制'},
      {value: 'week', text: '周级时效控制'},
      {value: 'month', text: '月级时效控制'},
      {value: 'unlimited', text: '无时效限制'}
    ]
  };

  const availableOptions = options[cycleType] || options['daily'];
  availableOptions.forEach(option => {
    const optionElement = document.createElement('option');
    optionElement.value = option.value;
    optionElement.textContent = option.text;
    timeLimitType.appendChild(optionElement);
  });

  // 尝试保持原有选择，如果不可用则选择第一个
  if (currentValue && availableOptions.some(opt => opt.value === currentValue)) {
    timeLimitType.value = currentValue;
  } else {
    timeLimitType.value = availableOptions[0].value;
  }

  updateTimeLimitDays();
}

// 更新时间限制数值显示
function updateTimeLimitDays() {
  const timeLimitType = document.getElementById('reportTimeLimitType').value;
  const timeLimitDaysGroup = document.getElementById('timeLimitDaysGroup');
  const timeLimitDaysInput = document.getElementById('reportTimeLimitDays');
  const timeLimitDaysLabel = document.getElementById('timeLimitDaysLabel');

  if (timeLimitType === 'unlimited') {
    timeLimitDaysGroup.style.display = 'none';
    timeLimitDaysInput.required = false;
  } else {
    timeLimitDaysGroup.style.display = 'block';
    timeLimitDaysInput.required = true;

    // 设置默认值和范围
    const defaults = {
      'hour': {value: 2, max: 168, unit: '小时'}, // 默认2小时，最多7天
      'day': {value: 2, max: 30, unit: '日'},
      'week': {value: 1, max: 4, unit: '周'},
      'month': {value: 1, max: 12, unit: '月'}
    };

    const config = defaults[timeLimitType] || defaults['day'];
    if (!timeLimitDaysInput.value) {
      timeLimitDaysInput.value = config.value;
    }
    timeLimitDaysInput.max = config.max;

    // 更新标签显示
    if (timeLimitDaysLabel) {
      timeLimitDaysLabel.textContent = '时效控制数值 (' + config.unit + ')';
    }
  }
}

// 保存报表
function saveReport() {
  const form = document.getElementById('reportForm');
  const formData = new FormData(form);

  // 收集值班人员配置
  const dutyStaffConfig = {};
  if (formData.has('duty_staff_enabled')) {
    dutyStaffConfig.enabled = true;
    dutyStaffConfig.day_label = formData.get('duty_day_label') || '白班值班人';
    dutyStaffConfig.night_label = formData.get('duty_night_label') || '夜班值班人';
    dutyStaffConfig.required = formData.has('duty_staff_required');
  } else {
    dutyStaffConfig.enabled = false;
  }

  const data = {
    id: formData.get('id') || null,
    name: formData.get('name'),
    code: formData.get('code'),
    category_id: formData.get('category_id') || null,
    template_code: formData.get('template_code') || 'pumps',
    cycle_type: formData.get('cycle_type'),
    time_limit_type: formData.get('time_limit_type'),
    time_limit_days: parseInt(formData.get('time_limit_days')) || 0,
    enabled: formData.has('enabled'),
    duty_staff_config: dutyStaffConfig
  };

  // 验证必填字段
  if (!data.name || !data.code || !data.template_code) {
    alert('请填写报表名称、编码和录入模板');
    return;
  }

  fetch(window.__REPORTS_API__.saveReport, {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    credentials: 'same-origin',
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(result => {
    if (result.success) {
      alert(result.message);
      bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();
      location.reload(); // 刷新页面显示最新数据
    } else {
      alert('保存失败：' + result.message);
    }
  })
  .catch(error => {
    console.error('Error saving report:', error);
    alert('保存失败');
  });
}

// 切换值班人员配置显示
function toggleDutyStaffConfig() {
  const checkbox = document.getElementById('dutyStaffEnabled');
  const configGroup = document.getElementById('dutyStaffConfigGroup');

  if (checkbox && configGroup) {
    configGroup.style.display = checkbox.checked ? 'contents' : 'none';
  }
}





// 报表配置管理
if (window.__REPORTS_API__) {
  var apis = window.__REPORTS_API__;
  var currentReportId = null;
  var allDevices = [];

  function fetchJSON(url){ return fetch(url,{credentials:'same-origin'}).then(r=>r.json()); }
  function postJSON(url, body){ return fetch(url,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(body),credentials:'same-origin'}).then(r=>r.json()); }

  // 配置设备
  window.configDevices = function(reportId) {
    currentReportId = reportId;
    var container = document.getElementById('deviceConfig');
    container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><div>加载中...</div></div>';

    Promise.all([
      fetchJSON(apis.getAllDevices),
      fetchJSON(apis.getDevices + '&report_id=' + reportId)
    ]).then(function(results) {
      var all = Array.isArray(results[0]) ? results[0] : (results[0] && results[0].data ? results[0].data : []);
      allDevices = all;
      var currentDevices = Array.isArray(results[1]) ? results[1] : (results[1] && results[1].data ? results[1].data : []);
      renderDeviceConfig(currentDevices);
    });
  };

  function renderDeviceConfig(currentDevices) {
    var container = document.getElementById('deviceConfig');
    var html = '<h6>报表 ID: ' + currentReportId + ' 的设备配置</h6>';
    html += '<div class="row">';

    // 左侧：可用设备
    html += '<div class="col-md-6">';
    html += '<div class="d-flex justify-content-between align-items-center mb-2">';
    html += '<h6 class="mb-0">可用设备</h6>';
    html += '<button class="btn btn-sm btn-outline-success" onclick="showQuickAddPump()">+ 快速添加泵号</button>';
    html += '</div>';
    html += '<div class="border rounded p-3" style="max-height:400px;overflow-y:auto">';
    allDevices.forEach(function(device) {
      html += '<div class="mb-2">';
      html += '<strong>' + device.name + '</strong>';
      if (device.pump_list && device.pump_list.length) {
        device.pump_list.forEach(function(pump) {
          html += '<div class="ms-3">';
          html += '<button class="btn btn-sm btn-outline-primary me-1" onclick="addDevice(' + device.id + ',' + pump.id + ')">';
          html += '+ ' + device.name + '-' + pump.pump_no;
          html += '</button>';
          html += '</div>';
        });
      } else {
        html += '<div class="ms-3">';
        html += '<button class="btn btn-sm btn-outline-primary" onclick="addDevice(' + device.id + ',null)">';
        html += '+ ' + device.name;
        html += '</button>';
        html += '</div>';
      }
      html += '</div>';
    });
    html += '</div>';
    html += '</div>';

    // 右侧：已配置设备
    html += '<div class="col-md-6">';
    html += '<h6>已配置设备</h6>';
    html += '<div id="configuredDevices" class="border rounded p-3" style="max-height:400px;overflow-y:auto">';
    // 确保 currentDevices 是数组
    var devices = currentDevices && currentDevices.data ? currentDevices.data : (Array.isArray(currentDevices) ? currentDevices : []);
    if (devices.length > 0) {
      devices.forEach(function(item) {
        html += '<div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded" data-device="' + item.device_id + '" data-pump="' + (item.pump_id || '') + '">';
        // 修改显示格式：从"热媒泵-1#"改为"1#热媒泵"
        var displayName = item.device_name;
        if (item.pump_no) {
          displayName = item.pump_no + item.device_name;
        }
        html += '<span>' + displayName + '</span>';
        html += '<button class="btn btn-sm btn-outline-danger" onclick="removeDevice(this)">移除</button>';
        html += '</div>';
      });
    } else {
      html += '<div class="text-muted text-center">暂无配置设备</div>';
    }
    html += '</div>';
    html += '<div class="mt-3">';
    html += '<button class="btn btn-primary" onclick="saveDeviceConfig()">保存配置</button>';
    html += '<button class="btn btn-secondary ms-2" onclick="cancelDeviceConfig()">取消</button>';
    html += '</div>';
    html += '</div>';

    html += '</div>';
    container.innerHTML = html;
  }

  window.addDevice = function(deviceId, pumpId) {
    var container = document.getElementById('configuredDevices');
    var device = allDevices.find(function(d) { return d.id == deviceId; });
    if (!device) return;

    var pump = pumpId ? device.pump_list.find(function(p) { return p.id == pumpId; }) : null;
    var name = device.name + (pump ? '-' + pump.pump_no : '');

    // 检查是否已存在
    var existing = container.querySelector('[data-device="' + deviceId + '"][data-pump="' + (pumpId || '') + '"]');
    if (existing) return;

    var div = document.createElement('div');
    div.className = 'd-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded';
    div.setAttribute('data-device', deviceId);
    div.setAttribute('data-pump', pumpId || '');
    div.innerHTML = '<span>' + name + '</span><button class="btn btn-sm btn-outline-danger" onclick="removeDevice(this)">移除</button>';
    container.appendChild(div);
  };

  window.removeDevice = function(btn) {
    var deviceElement = btn.closest('.d-flex');
    var deviceName = deviceElement.querySelector('span').textContent;

    if (confirm('确定要移除设备 "' + deviceName + '" 吗？')) {
      deviceElement.remove();
    }
  };

  window.saveDeviceConfig = function() {
    var items = document.querySelectorAll('#configuredDevices .d-flex');
    var devices = [];
    items.forEach(function(item) {
      var deviceId = item.getAttribute('data-device');
      var pumpId = item.getAttribute('data-pump');
      devices.push({
        device_id: parseInt(deviceId),
        pump_id: pumpId ? parseInt(pumpId) : null
      });
    });

    postJSON(apis.saveDevices, {
      report_id: currentReportId,
      devices: devices
    }).then(function(result) {
      if (result.success) {
        alert('保存成功');
        cancelDeviceConfig();
      } else {
        alert('保存失败：' + result.message);
      }
    });
  };

  window.cancelDeviceConfig = function() {
    document.getElementById('deviceConfig').innerHTML = '<p class="text-muted">请从上方表格点击"设备配置"来配置报表关联的设备。</p>';
  };
}

// 设备管理功能
window.showDeviceManagement = function() {
  const modal = new bootstrap.Modal(document.getElementById('deviceManagementModal'));
  modal.show();
  loadDeviceList();
};

function loadDeviceList() {
  const deviceList = document.getElementById('deviceList');
  deviceList.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><span class="ms-2">加载中...</span></div>';

  fetch(window.__REPORTS_API__.listDevices, {
    credentials: 'same-origin'
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderDeviceList(data.data);
    } else {
      deviceList.innerHTML = '<div class="alert alert-danger">' + (data.message || '加载设备列表失败') + '</div>';
    }
  })
  .catch(error => {
    console.error('Error loading devices:', error);
    deviceList.innerHTML = '<div class="alert alert-danger">网络错误，请稍后重试</div>';
  });
}

function renderDeviceList(devices) {
  const deviceList = document.getElementById('deviceList');

  if (devices.length === 0) {
    deviceList.innerHTML = '<div class="alert alert-info">暂无设备，请添加新设备</div>';
    return;
  }

  let html = '<div class="table-responsive">';
  html += '<table class="table table-striped table-hover">';
  html += '<thead><tr>';
  html += '<th>ID</th><th>名称</th><th>编码</th><th>泵数量</th><th>关联报表</th><th>创建时间</th><th>操作</th>';
  html += '</tr></thead><tbody>';

  devices.forEach(device => {
    html += '<tr>';
    html += '<td>' + device.id + '</td>';
    html += '<td>' + escapeHtml(device.name) + '</td>';
    html += '<td><code>' + escapeHtml(device.code) + '</code></td>';
    html += '<td>' + device.pump_list.length + '</td>';
    html += '<td>' + device.report_count + '</td>';
    html += '<td>' + formatDateTime(device.created_at) + '</td>';
    html += '<td>';
    html += '<button class="btn btn-sm btn-outline-primary me-1" onclick="editDevice(' + device.id + ')">编辑</button>';
    if (device.report_count === 0) {
      html += '<button class="btn btn-sm btn-outline-danger" onclick="deleteDevice(' + device.id + ', \'' + escapeHtml(device.name) + '\')">删除</button>';
    } else {
      html += '<button class="btn btn-sm btn-outline-secondary" disabled title="已关联报表，无法删除">删除</button>';
    }
    html += '</td>';
    html += '</tr>';
  });

  html += '</tbody></table></div>';
  deviceList.innerHTML = html;
}

// —— 泵号管理区域（在设备管理模态框中扩展 UI） ——
function renderPumpManager(device) {
  const pumps = device.pump_list || [];
  let html = '<div class="device-pumps">';
  // 添加行：使用 input-group 紧凑布局
  html += '<div class="add-row mb-2">';
  html += '<div class="input-group input-group-sm">';
  html += '<span class="input-group-text">泵号</span>';
  html += '<input type="text" class="form-control" placeholder="如 1# / A" id="newPump_'+device.id+'">';
  html += '<button class="btn btn-primary" type="button" title="添加泵号" onclick="createPump('+device.id+')"><i class="fa fa-plus"></i><span class="d-none d-md-inline ms-1">添加</span></button>';
  html += '</div>';
  html += '</div>';
  // 泵号chips：自动换行
  html += '<div class="pump-badges">';
  if (pumps.length) {
    pumps.forEach(function(p){
      html += '<span class="chip">'+escapeHtml(p.pump_no)+'<button type="button" class="chip-close" onclick="deletePump('+p.id+','+device.id+')" aria-label="删除"><i class="fa fa-times"></i></button></span>';
    });
  } else {
    html += '<span class="text-muted small">暂无泵号</span>';
  }
  html += '</div>';
  html += '</div>';
  return html;
}

// 扩展设备列表渲染，追加泵号管理区（不影响表头）
(function(){
  const oldRender = renderDeviceList;
  renderDeviceList = function(devices){
    const container = document.getElementById('deviceList');
    let html = '<div class="table-responsive">'
      + '<table class="table table-striped table-hover">'
      + '<thead><tr>'
      + '<th>ID</th><th>名称</th><th>编码</th><th>泵数量</th><th>关联报表</th><th>创建时间</th><th>操作</th>'
      + '</tr></thead><tbody>';
    devices.forEach(function(device){
      html += '<tr>'
           + '<td>'+device.id+'</td>'
           + '<td>'+escapeHtml(device.name)+'<div>'+renderPumpManager(device)+'</div></td>'
           + '<td><code>'+escapeHtml(device.code)+'</code></td>'
           + '<td>'+device.pump_list.length+'</td>'
           + '<td>'+device.report_count+'</td>'
           + '<td>'+formatDateTime(device.created_at)+'</td>'
           + '<td><button class="btn btn-sm btn-outline-primary me-1" onclick="editDevice('+device.id+')">编辑</button>'
           + (device.report_count===0?'<button class="btn btn-sm btn-outline-danger" onclick="deleteDevice('+device.id+',\''+escapeHtml(device.name)+'\')">删除</button>':'<button class="btn btn-sm btn-outline-secondary" disabled title="已关联报表，无法删除">删除</button>')
           + '</td>'
           + '</tr>';
    });
    html += '</tbody></table></div>';
    container.innerHTML = html;
  }
})();

// 创建泵号
function createPump(deviceId){
  const input = document.getElementById('newPump_'+deviceId);
  const val = (input && input.value || '').trim();
  if(!val){ alert('请输入泵号'); return; }
  fetch(window.__REPORTS_API__.createPump, {
    method:'POST', headers:{'Content-Type':'application/json'}, credentials:'same-origin',
    body: JSON.stringify({device_id: deviceId, pump_no: val})
  }).then(r=>r.json()).then(data=>{
    if(data.success){
      input.value='';
      loadDeviceList();
      // 如果当前打开了报表设备配置，则刷新左侧可用设备列表
      if (typeof configDevices === 'function' && typeof currentReportId !== 'undefined' && currentReportId){
        configDevices(currentReportId);
      }
    }else{ alert(data.message||'添加失败'); }
  }).catch(err=>{ console.error(err); alert('网络错误'); });
}

// 删除泵号
function deletePump(pumpId, deviceId){
  if(!confirm('确定删除该泵号吗？')) return;
  fetch(window.__REPORTS_API__.deletePump, {
    method:'POST', headers:{'Content-Type':'application/json'}, credentials:'same-origin',
    body: JSON.stringify({id:pumpId})
  }).then(r=>r.json()).then(data=>{
    if(data.success){
      loadDeviceList();
      if (typeof configDevices === 'function' && typeof currentReportId !== 'undefined' && currentReportId){
        configDevices(currentReportId);
      }
    }else{ alert(data.message||'删除失败'); }
  }).catch(err=>{ console.error(err); alert('网络错误'); });
}


function editDevice(deviceId) {
  fetch(window.__REPORTS_API__.listDevices, {credentials: 'same-origin'})
    .then(r => r.json())
    .then(data => {
      if (data.success) {
        const device = data.data.find(d => d.id === deviceId);
        if (device) {
          document.getElementById('deviceId').value = device.id;
          document.getElementById('deviceName').value = device.name;
          document.getElementById('deviceCode').value = device.code;
          document.querySelector('#deviceForm button[type="submit"]').textContent = '更新设备';
        }
      }
    });
}

function deleteDevice(deviceId, deviceName) {
  if (!confirm('确定要删除设备 "' + deviceName + '" 吗？此操作不可恢复。')) return;
  fetch(window.__REPORTS_API__.deleteDevice, {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    credentials: 'same-origin',
    body: JSON.stringify({id: deviceId})
  })
  .then(r => r.json())
  .then(data => {
    if (data.success) {
      alert('删除成功');
      loadDeviceList();
    } else {
      alert(data.message || '删除失败');
    }
  })
  .catch(err => {
    console.error(err);

// —— 设备编码自动生成 ——
function pad3(n){ n=parseInt(n,10); if(isNaN(n)) return ''; return (n<10?'00':(n<100?'0':''))+n; }
function mapChineseNumeralToDigit(ch){
  const map={'零':0,'一':1,'二':2,'两':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9,'十':10};
  return map[ch]!==undefined? map[ch] : null;
}
function extractNumberFromName(name){
  // 先找阿拉伯数字
  const m1 = name.match(/(\d{1,3})\s*#?/);
  if (m1) return parseInt(m1[1],10);
  // 再找中文数字（最多十几以内的常见表达）
  // 如 “一#泵”“二号泵”
  const m2 = name.match(/[零一二两三四五六七八九十]{1,3}/);
  if (m2){
    const s=m2[0];
    if (s.length===1){ return mapChineseNumeralToDigit(s); }
    // 简单处理“十”“十一”“二十”“二十三”
    let total=0; let tenIndex=s.indexOf('十');
    if (tenIndex>=0){
      const left = s.slice(0,tenIndex);
      const right = s.slice(tenIndex+1);
      const tens = left? mapChineseNumeralToDigit(left):1;
      total += tens*10;
      if (right){ total += mapChineseNumeralToDigit(right)||0; }
      return total;
    }
  }
  return null;
}
function extractLetterToken(name){
  const m = name.match(/([A-Za-z]+)/);
  return m? m[1].toUpperCase(): '';
}
function generateDeviceCode(name){
  const n = extractNumberFromName(name||'');
  const letters = extractLetterToken(name||'');
  let suffix = '';
  if (n!==null) suffix = pad3(n);
  else if (letters) suffix = letters;
  else suffix = 'GEN';
  return 'DEV_' + suffix;
}
function setupAutoDeviceCode(){
  const nameEl = document.getElementById('deviceName');
  const codeEl = document.getElementById('deviceCode');
  const idEl = document.getElementById('deviceId');
  if(!nameEl || !codeEl) return;
  // 用户手动改动后，不再覆盖
  codeEl.addEventListener('input', function(){ this.dataset.userEdited = '1'; });
  function maybeFill(){
    if (idEl && idEl.value) return; // 编辑模式不覆盖
    if (codeEl.dataset.userEdited === '1' && codeEl.value) return;
    const code = generateDeviceCode(nameEl.value.trim());
    if (code) { codeEl.value = code; codeEl.dataset.userEdited = '0'; }
  }
  nameEl.addEventListener('input', maybeFill);
  // 初始触发一次
  maybeFill();
}
// DOMContentLoaded 时启用自动编码
document.addEventListener('DOMContentLoaded', setupAutoDeviceCode);

    alert('网络错误，请稍后重试');
  });
}

// 提交设备表单
document.addEventListener('DOMContentLoaded', function() {
  const deviceForm = document.getElementById('deviceForm');
  if (!deviceForm) return;
  deviceForm.addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(deviceForm);
    const id = formData.get('device_id');
    const payload = {
      name: formData.get('name'),
      code: formData.get('code'),
      description: formData.get('description') || ''
    };
    const url = id ? window.__REPORTS_API__.updateDevice : window.__REPORTS_API__.createDevice;
    if (id) payload.id = parseInt(id);
    fetch(url, {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      credentials: 'same-origin',
      body: JSON.stringify(payload)
    })
    .then(r => r.json())
    .then(data => {
      if (data.success) {
        alert(id ? '设备更新成功' : '设备创建成功');
        deviceForm.reset();
        document.getElementById('deviceId').value = '';
        document.querySelector('#deviceForm button[type="submit"]').textContent = '保存设备';
        loadDeviceList();
      } else {
        alert(data.message || (id ? '更新失败' : '创建失败'));
      }
    })
    .catch(err => { console.error(err); alert('网络错误，请稍后重试'); });
  });
});

// 快速添加泵号功能
function showQuickAddPump() {
  const html = `
    <div class="modal fade" id="quickAddPumpModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">快速添加泵号</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="quickAddPumpForm">
              <div class="mb-3">
                <label class="form-label">选择设备 <span class="text-danger">*</span></label>
                <select class="form-select" id="quickDeviceSelect" required>
                  <option value="">请选择设备</option>
                </select>
              </div>
              <div class="mb-3">
                <label class="form-label">泵号 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="quickPumpNo" placeholder="例如：A、B、1#、2#" required>
                <div class="form-text">建议使用简短的标识符，如A、B或1#、2#</div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitQuickAddPump()">添加泵号</button>
          </div>
        </div>
      </div>
    </div>
  `;

  // 移除已存在的模态框
  const existingModal = document.getElementById('quickAddPumpModal');
  if (existingModal) {
    existingModal.remove();
  }

  // 添加新模态框
  document.body.insertAdjacentHTML('beforeend', html);

  // 填充设备选项
  const deviceSelect = document.getElementById('quickDeviceSelect');
  allDevices.forEach(device => {
    const option = document.createElement('option');
    option.value = device.id;
    option.textContent = device.name;
    deviceSelect.appendChild(option);
  });

  // 显示模态框
  const modal = new bootstrap.Modal(document.getElementById('quickAddPumpModal'));
  modal.show();
}

function submitQuickAddPump() {
  const deviceId = document.getElementById('quickDeviceSelect').value;
  const pumpNo = document.getElementById('quickPumpNo').value.trim();

  if (!deviceId || !pumpNo) {
    alert('请选择设备并输入泵号');
    return;
  }

  const submitBtn = document.querySelector('#quickAddPumpModal .btn-primary');
  const originalText = submitBtn.textContent;
  submitBtn.disabled = true;
  submitBtn.textContent = '添加中...';

  fetch(window.__REPORTS_API__.createPump, {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    credentials: 'same-origin',
    body: JSON.stringify({device_id: parseInt(deviceId), pump_no: pumpNo})
  })
  .then(r => r.json())
  .then(data => {
    if (data.success) {
      alert('泵号添加成功');
      // 关闭模态框
      const modal = bootstrap.Modal.getInstance(document.getElementById('quickAddPumpModal'));
      modal.hide();
      // 刷新设备配置
      if (typeof configDevices === 'function' && typeof currentReportId !== 'undefined' && currentReportId) {
        configDevices(currentReportId);
      }
    } else {
      alert(data.message || '添加失败');
    }
  })
  .catch(err => {
    console.error(err);
    alert('网络错误，请稍后重试');
  })
  .finally(() => {
    submitBtn.disabled = false;
    submitBtn.textContent = originalText;
  });
}

// 辅助函数
function resetDeviceForm() {
  const form = document.getElementById('deviceForm');
  if (form) {
    form.reset();
    document.getElementById('deviceId').value = '';
    document.querySelector('#deviceForm button[type="submit"]').textContent = '保存设备';
  }
}

function escapeHtml(text) {
  if (!text) return '';
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatDateTime(dateTime) {
  if (!dateTime) return '';
  return new Date(dateTime).toLocaleString('zh-CN');
}
