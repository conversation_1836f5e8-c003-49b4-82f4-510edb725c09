// 连续报表录入页面 JavaScript
// 用于处理间歇运行的设备，每次运行记录一行数据

(function() {
    'use strict';

    // 全局变量
    let entryReportId = 0;
    let entryDeviceId = 0;
    let currentMonth = '';
    let currentRange = 'month';
    let entryData = [];
    let templateFields = [];
    let isQueryMode = false; // 查询模式标志

    // 检查记录是否可编辑（基于时间限制配置）
    function isEntryEditable(entryDate) {
        if (!window.__REPORT_CONFIG__ || !entryDate) return true;

        const config = window.__REPORT_CONFIG__;
        const timeLimitDays = config.time_limit_days || 2;
        const timeLimitType = config.time_limit_type || 'day';

        // 获取用户角色
        const userRoles = window.__USER_ROLES__ || [];
        const isAdmin = userRoles.includes('admin');
        const isManager = userRoles.includes('mod'); // 普通管理员角色代码是 'mod'
        const isRegularUser = !isAdmin && !isManager;

        // 解析记录日期
        const recordDate = new Date(entryDate);
        recordDate.setHours(0, 0, 0, 0);
        const now = new Date();
        now.setHours(0, 0, 0, 0);

        // 管理员不受任何限制
        if (isAdmin) return true;

        // 所有用户（包括普通管理员）都不能编辑未来日期
        if (recordDate > now) return false;

        // 普通管理员不受时间限制，但不能编辑未来日期
        if (isManager) return true;

        // 普通用户受时间限制
        if (timeLimitType === 'unlimited') return true;

        // 按自然日计算时间限制
        if (timeLimitType === 'day') {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const earliestDate = new Date(today);
            earliestDate.setDate(today.getDate() - timeLimitDays + 1);

            return recordDate >= earliestDate;
        }

        // 其他时间限制类型
        let limitDate = new Date(now);
        switch (timeLimitType) {
            case 'hour':
                limitDate.setHours(limitDate.getHours() - timeLimitDays);
                break;
            case 'week':
                limitDate.setDate(limitDate.getDate() - (timeLimitDays * 7));
                break;
            case 'month':
                limitDate.setMonth(limitDate.getMonth() - timeLimitDays);
                break;
        }

        return recordDate >= limitDate;
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行，确保entry-generic.js先执行
        setTimeout(function() {
            if (!window.__REPORT_API__ || window.__REPORT_API__.reportType !== 'continuous') {
                return; // 只在连续报表页面运行
            }

            entryReportId = window.__REPORT_API__.reportId;

            // 检查是否为查询模式（通过API配置判断）
            isQueryMode = window.__REPORT_API__.isQueryMode || false;

            initializeElements();
            loadDevices();

            // 添加窗口大小变化监听器（防抖处理）
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    // 如果有数据，重新渲染表格
                    if (entryData && entryData.length > 0) {
                        renderEntryTable();
                    }
                }, 300); // 300ms 防抖
            });

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', function() {
                // 清理全局变量
                entryData = [];
                templateFields = [];
                currentPage = 1;
                totalPages = 1;

                // 清理事件监听器
                document.removeEventListener('click', arguments.callee);
                document.removeEventListener('change', arguments.callee);
            });

            // 定期内存清理（每5分钟）
            setInterval(function() {
                if (performance.memory) {
                    const used = Math.round(performance.memory.usedJSHeapSize / 1048576);
                    if (used > 200) { // 200MB阈值
                        console.warn('内存使用过高:', used + 'MB');
                        // 强制垃圾回收（如果可用）
                        if (window.gc) {
                            window.gc();
                        }
                    }
                }
            }, 300000);
        }, 100);
    });

    // 初始化页面元素 - 使用事件委托优化性能
    function initializeElements() {
        // 使用事件委托处理按钮和选择框事件
        document.addEventListener('click', function(e) {
            const target = e.target.closest('button');
            if (!target) return;

            const id = target.id;
            if (id === 'btnQuery') {
                loadEntryData();
            } else if (id === 'btnAddEntry') {
                addNewRow();
            }
        });

        document.addEventListener('change', function(e) {
            const target = e.target;
            const id = target.id;

            if (id === 'deviceSelect' || id === 'viewMonth' || id === 'viewRange' || id === 'sortOrder') {
                loadEntryData();
            }
        });
    }

    // 加载设备列表
    function loadDevices() {
        if (!window.__REPORT_API__.getDevices) return;

        fetch(window.__REPORT_API__.getDevices, { credentials: 'same-origin' })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    renderDeviceOptions(result.data, result.auto_select);
                    // 自动选择设备的逻辑已经在renderDeviceOptions中处理
                } else {
                    showMessage('加载设备列表失败: ' + (result.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                showMessage('网络错误，加载设备列表失败', 'error');
            });
    }

    // 渲染设备选项
    function renderDeviceOptions(devices, autoSelectId) {
        const deviceSelect = document.getElementById('deviceSelect');
        if (!deviceSelect) return;

        // 清空现有选项，保留第一个默认选项
        deviceSelect.innerHTML = '<option value="">请选择设备...</option>';

        if (!Array.isArray(devices) || devices.length === 0) {
            deviceSelect.innerHTML = '<option value="">该报表未配置设备</option>';
            return;
        }

        devices.forEach((device, index) => {
            const option = document.createElement('option');

            // 统一使用device_id作为option.value，简化逻辑
            option.value = device.device_id || device.id;

            // 存储pump_id用于后端保存时使用
            if (device.pump_id) {
                option.dataset.pumpId = device.pump_id;
            }

            // 处理设备名称显示
            let deviceName = device.device_name || device.name || '';
            if (device.pump_no) {
                deviceName = `${device.pump_no}${deviceName}`;
            }
            option.textContent = deviceName;

            deviceSelect.appendChild(option);
        });

        // 自动选择设备逻辑
        let selectedDeviceId = null;

        if (autoSelectId) {
            // API返回的auto_select可能是pump_id，需要找到对应的device_id
            // 先尝试直接匹配device_id
            const directMatch = devices.find(d => (d.device_id || d.id) == autoSelectId);
            if (directMatch) {
                selectedDeviceId = directMatch.device_id || directMatch.id;
            } else {
                // 如果直接匹配失败，尝试通过pump_id匹配
                const pumpMatch = devices.find(d => d.pump_id == autoSelectId);
                if (pumpMatch) {
                    selectedDeviceId = pumpMatch.device_id || pumpMatch.id;
                }
            }
        } else if (devices.length > 0) {
            // 如果没有auto_select，自动选择第一个设备
            const firstDevice = devices[0];
            selectedDeviceId = firstDevice.device_id || firstDevice.id;
        }

        if (selectedDeviceId) {
            deviceSelect.value = selectedDeviceId;

            // 设置默认月份为当前月份
            const viewMonth = document.getElementById('viewMonth');
            if (viewMonth && !viewMonth.value) {
                viewMonth.value = new Date().toISOString().slice(0, 7);
            }

            // 自动加载数据
            loadEntryData();
        }
    }

    // 加载录入数据
    function loadEntryData() {
        const deviceSelect = document.getElementById('deviceSelect');
        const viewMonth = document.getElementById('viewMonth');
        const viewRange = document.getElementById('viewRange');
        const sortOrder = document.getElementById('sortOrder');

        if (!deviceSelect || !viewMonth || !viewRange) {
            showMessage('页面元素缺失', 'error');
            return;
        }

        entryDeviceId = parseInt(deviceSelect.value) || 0;
        currentMonth = viewMonth.value;
        currentRange = viewRange.value;
        const currentSort = sortOrder ? sortOrder.value : 'desc';

        // 如果没有选择月份，自动设置为当前月份
        if (!currentMonth) {
            currentMonth = new Date().toISOString().slice(0, 7);
            viewMonth.value = currentMonth;
        }

        if (!entryDeviceId) {
            showMessage('请选择设备', 'warning');
            return;
        }

        // 获取实际的设备ID（对于泵设备使用pump_id）
        const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
        const actualDeviceId = selectedOption.dataset.pumpId || entryDeviceId;

        // 构建查询参数
        const params = new URLSearchParams({
            report_id: entryReportId,
            device_id: actualDeviceId,
            month: currentMonth,
            range: currentRange,
            sort: currentSort
        });

        const baseUrl = window.__REPORT_API__.loadData;
        const url = baseUrl + (baseUrl.includes('?') ? '&' : '?') + params.toString();

        fetch(url, { credentials: 'same-origin' })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    entryData = result.data || [];
                    templateFields = result.template_fields || [];
                    renderEntryTable();
                } else {
                    showMessage('加载数据失败: ' + (result.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                showMessage('网络错误，加载数据失败', 'error');
            });
    }

    // 计算字段宽度的辅助函数，避免重复计算
    function calculateFieldWidth(field, screenSize) {
        let fieldWidth = 70; // 小屏默认

        if (field.type === 'time') {
            fieldWidth = screenSize.isLarge ? 100 : (screenSize.isMedium ? 85 : 75);
        } else if (field.type === 'text') {
            if (field.key === 'maintenance_status') {
                fieldWidth = screenSize.isLarge ? 200 : (screenSize.isMedium ? 150 : 120);
            } else if (field.key === 'duty_staff') {
                fieldWidth = screenSize.isLarge ? 100 : (screenSize.isMedium ? 85 : 70);
            } else {
                fieldWidth = screenSize.isLarge ? 120 : (screenSize.isMedium ? 100 : 80);
            }
        } else if (field.type === 'number') {
            if (field.key.includes('runtime') || field.key.includes('bearing')) {
                fieldWidth = screenSize.isLarge ? 100 : (screenSize.isMedium ? 85 : 75);
            } else {
                fieldWidth = screenSize.isLarge ? 85 : (screenSize.isMedium ? 75 : 65);
            }
        }

        return fieldWidth;
    }

    // 分页配置
    const ITEMS_PER_PAGE = 50; // 每页显示50条记录
    let currentPage = 1;
    let totalPages = 1;

    // 渲染录入表格（优化版本 - 支持分页和虚拟滚动）
    function renderEntryTable() {
        const container = document.getElementById('entryTableContainer');
        if (!container) return;

        // 计算分页
        totalPages = Math.ceil(entryData.length / ITEMS_PER_PAGE);
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, entryData.length);
        const pageData = entryData.slice(startIndex, endIndex);

        // 添加自定义CSS样式（恢复基本功能）
        let html = `
            <style>
                .continuous-table {
                    table-layout: fixed !important;
                    width: 100% !important;
                }
                .continuous-table th,
                .continuous-table td {
                    white-space: nowrap !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                }
                .editing-row {
                    background-color: #fff3cd !important;
                }
                .editing-row input,
                .editing-row select {
                    width: 100% !important;
                    font-size: 0.75rem !important;
                    padding: 0.2rem 0.3rem !important;
                    border: 1px solid #ced4da !important;
                    border-radius: 0.25rem !important;
                }
                .pagination-info {
                    font-size: 0.875rem;
                    color: #6c757d;
                }
            </style>
        `;

        // 预计算屏幕尺寸，避免在循环中重复计算
        const screenSize = {
            isLarge: window.innerWidth >= 1600,
            isMedium: window.innerWidth >= 1200
        };

        // 渲染记录列表（优化版本 - 添加分页信息）
        html += `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h6 class="mb-0">${isQueryMode ? '查询结果' : '运行记录'} (${entryData.length} 条)</h6>
                    <div class="pagination-info">
                        第 ${currentPage} 页，共 ${totalPages} 页 | 显示 ${startIndex + 1}-${endIndex} 条
                    </div>
                </div>
                <div class="btn-group btn-group-sm">`;

        // 录入模式显示新增按钮，查询模式显示导出按钮
        if (!isQueryMode) {
            html += `
                    <button class="btn btn-primary" onclick="addNewRow()">
                        <i class="fa fa-plus"></i> 新增记录
                    </button>`;
        } else {
            html += `
                    <button class="btn btn-outline-success" onclick="exportData()" style="border-radius: 4px;">
                        <i class="fa fa-file-excel-o" style="color: #28a745;"></i> 导出Excel
                    </button>`;
        }

        html += `
                </div>
            </div>
        `;

        // 添加分页控件
        if (totalPages > 1) {
            html += `
                <div class="d-flex justify-content-center mb-3">
                    <nav aria-label="分页导航">
                        <ul class="pagination pagination-sm">
                            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                <button class="page-link" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                                    <i class="fa fa-chevron-left"></i>
                                </button>
                            </li>`;

            // 显示页码（最多显示5页）
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                html += `
                            <li class="page-item ${i === currentPage ? 'active' : ''}">
                                <button class="page-link" onclick="changePage(${i})">${i}</button>
                            </li>`;
            }

            html += `
                            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                <button class="page-link" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                                    <i class="fa fa-chevron-right"></i>
                                </button>
                            </li>
                        </ul>
                    </nav>
                </div>
            `;
        }

        // 始终显示表格，即使没有数据
        if (templateFields && templateFields.length > 0) {
            html += `
                <div class="table-responsive" style="max-height: 70vh;">
                    <table class="table table-sm table-hover table-bordered continuous-table">
                        <thead class="table-light sticky-top">
            `;

            // 按分组整理字段
            const groups = {};
            const ungrouped = [];

            templateFields.forEach(field => {
                if (field.group) {
                    if (!groups[field.group]) groups[field.group] = [];
                    groups[field.group].push(field);
                } else {
                    ungrouped.push(field);
                }
            });

            // 如果有分组，生成分组表头
            if (Object.keys(groups).length > 0) {
                html += `<tr class="table-secondary">`;
                html += `<th rowspan="2" class="text-center align-middle" style="width:80px !important; min-width:80px !important; max-width:80px !important; border-right: 2px solid #dee2e6; padding: 0.3rem 0.2rem !important;">日期</th>`;

                // 先输出分组表头，添加分组间的分隔线
                const groupNames = Object.keys(groups);
                groupNames.forEach((groupName, index) => {
                    const isLastGroup = index === groupNames.length - 1 && ungrouped.length === 0;
                    const borderStyle = isLastGroup ? '' : ' border-right: 2px solid #dee2e6;';
                    html += `<th colspan="${groups[groupName].length}" class="text-center" style="font-weight: bold;${borderStyle}">${escapeHtml(groupName)}</th>`;
                });

                // 未分组字段
                if (ungrouped.length > 0) {
                    html += `<th colspan="${ungrouped.length}" class="text-center" style="font-weight: bold; border-right: 2px solid #dee2e6;">其他</th>`;
                }

                // 查询模式下添加录入信息和修改信息列
                if (isQueryMode && window.__SYSTEM_CONFIG__) {
                    if (window.__SYSTEM_CONFIG__.show_entry_info) {
                        html += `<th colspan="2" class="text-center" style="border-right: 2px solid #dee2e6;">录入信息</th>`;
                    }
                    if (window.__SYSTEM_CONFIG__.show_modify_info) {
                        html += `<th colspan="2" class="text-center" style="border-right: 2px solid #dee2e6;">修改信息</th>`;
                    }
                }

                // 录入模式下显示操作列
                if (!isQueryMode) {
                    html += `<th rowspan="2" class="text-center align-middle" style="padding: 0.3rem 0.2rem;">操作</th>`;
                }
                html += `</tr>`;

                // 字段名表头，使用预计算的响应式列宽
                html += `<tr class="table-light">`;

                // 不再需要屏幕尺寸计算，使用CSS响应式

                groupNames.forEach((groupName, groupIndex) => {
                    groups[groupName].forEach((field, fieldIndex) => {
                        const isLastFieldInGroup = fieldIndex === groups[groupName].length - 1;
                        const isLastGroup = groupIndex === groupNames.length - 1 && ungrouped.length === 0;
                        const borderStyle = (isLastFieldInGroup && !isLastGroup) ? ' border-right: 2px solid #dee2e6;' : '';
                        const label = field.unit ? `${field.label}(${field.unit})` : field.label;
                        const requiredMark = field.required ? '<span style="color:red;">*</span>' : '';

                        // 计算响应式列宽
                        const fieldWidth = calculateFieldWidth(field, screenSize);

                        html += `<th class="text-center" style="width:${fieldWidth}px !important; min-width:${fieldWidth}px !important; max-width:${fieldWidth}px !important; font-size: 0.75rem !important; padding: 0.3rem 0.2rem !important;${borderStyle}">${escapeHtml(label)}${requiredMark}</th>`;
                    });
                });
                ungrouped.forEach((field, index) => {
                    const isLastField = index === ungrouped.length - 1;
                    const borderStyle = isLastField ? ' border-right: 2px solid #dee2e6;' : '';
                    const label = field.unit ? `${field.label}(${field.unit})` : field.label;
                    const requiredMark = field.required ? '<span style="color:red;">*</span>' : '';

                    // 简单的三档列宽设置
                    let fieldWidth = 70; // 小屏默认

                    if (field.type === 'time') {
                        fieldWidth = isLargeScreen ? 100 : (isMediumScreen ? 85 : 75);
                    } else if (field.type === 'text') {
                        if (field.key === 'maintenance_status') {
                            fieldWidth = isLargeScreen ? 200 : (isMediumScreen ? 150 : 120);
                        } else if (field.key === 'duty_staff') {
                            fieldWidth = isLargeScreen ? 100 : (isMediumScreen ? 85 : 70);
                        } else {
                            fieldWidth = isLargeScreen ? 120 : (isMediumScreen ? 100 : 80);
                        }
                    } else if (field.type === 'number') {
                        if (field.key.includes('runtime') || field.key.includes('bearing')) {
                            fieldWidth = isLargeScreen ? 100 : (isMediumScreen ? 85 : 75);
                        } else {
                            fieldWidth = isLargeScreen ? 85 : (isMediumScreen ? 75 : 65);
                        }
                    }

                    html += `<th class="text-center" style="width:${fieldWidth}px !important; min-width:${fieldWidth}px !important; max-width:${fieldWidth}px !important; font-size: 0.75rem !important; padding: 0.3rem 0.2rem !important;${borderStyle}">${escapeHtml(label)}${requiredMark}</th>`;
                });

                // 查询模式下添加录入信息和修改信息的子表头
                if (isQueryMode && window.__SYSTEM_CONFIG__) {
                    if (window.__SYSTEM_CONFIG__.show_entry_info) {
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">录入人</th>`;
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem; border-right: 2px solid #dee2e6;">录入时间</th>`;
                    }
                    if (window.__SYSTEM_CONFIG__.show_modify_info) {
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">修改人</th>`;
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem; border-right: 2px solid #dee2e6;">修改时间</th>`;
                    }
                }

                html += `</tr>`;
            } else {
                // 无分组时的简单表头，使用CSS响应式

                html += `<tr><th class="text-center" style="padding: 0.3rem 0.2rem;">日期</th>`;
                templateFields.forEach(field => {
                    const label = field.unit ? `${field.label}(${field.unit})` : field.label;
                    const requiredMark = field.required ? '<span style="color:red;">*</span>' : '';

                    // 不再需要固定列宽，使用CSS响应式

                    html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">${escapeHtml(label)}${requiredMark}</th>`;
                });

                // 查询模式下添加录入信息和修改信息列
                if (isQueryMode && window.__SYSTEM_CONFIG__) {
                    if (window.__SYSTEM_CONFIG__.show_entry_info) {
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">录入人</th>`;
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">录入时间</th>`;
                    }
                    if (window.__SYSTEM_CONFIG__.show_modify_info) {
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">修改人</th>`;
                        html += `<th class="text-center" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">修改时间</th>`;
                    }
                }

                // 录入模式下显示操作列
                if (!isQueryMode) {
                    html += `<th class="text-center" style="padding: 0.3rem 0.2rem;">操作</th>`;
                }
                html += `</tr>`;
            }

            html += `
                        </thead>
                        <tbody>
            `;

            // 渲染数据行（仅渲染当前页数据）
            pageData.forEach((entry, index) => {
                // 不再需要屏幕尺寸计算，使用CSS响应式

                // 检查是否为编辑状态
                const isEditing = entry.isEditing || false;
                const rowClass = isEditing ? 'editing-row' : '';

                html += `
                    <tr class="${rowClass}" id="row-${entry.id || 'new'}">
                        <td class="text-center align-middle" style="width:80px !important; min-width:80px !important; max-width:80px !important; border-right: 2px solid #dee2e6; padding: 0.3rem 0.2rem !important; font-size: 0.75rem !important;">
                            ${isEditing ? `<input type="date" value="${entry.entry_date || new Date().toISOString().slice(0, 10)}" id="date-${entry.id || 'new'}" style="width: 100% !important;">` : (entry.entry_date || '')}
                        </td>
                `;

                // 按分组顺序输出字段数据，与表头保持一致
                const groups = {};
                const ungrouped = [];

                templateFields.forEach(field => {
                    if (field.group) {
                        if (!groups[field.group]) groups[field.group] = [];
                        groups[field.group].push(field);
                    } else {
                        ungrouped.push(field);
                    }
                });

                // 按分组顺序输出字段，添加分组分隔线
                const groupNames = Object.keys(groups);
                groupNames.forEach((groupName, groupIndex) => {
                    groups[groupName].forEach((field, fieldIndex) => {
                        const isLastFieldInGroup = fieldIndex === groups[groupName].length - 1;
                        const isLastGroup = groupIndex === groupNames.length - 1 && ungrouped.length === 0;
                        const borderStyle = (isLastFieldInGroup && !isLastGroup) ? ' border-right: 2px solid #dee2e6;' : '';
                        const value = entry[field.key] || '';

                        // 计算列宽（与表头保持一致）
                        const fieldWidth = calculateFieldWidth(field, screenSize);

                        let cellStyle = `width:${fieldWidth}px !important; min-width:${fieldWidth}px !important; max-width:${fieldWidth}px !important; font-size: 0.75rem !important; padding: 0.3rem 0.2rem !important;${borderStyle}`;

                        // 根据编辑状态渲染不同内容
                        let cellContent = '';
                        if (isEditing) {
                            // 编辑状态：显示输入框
                            const fieldId = `field-${field.key}-${entry.id || 'new'}`;
                            const requiredAttr = field.required ? ' required' : '';

                            if (field.type === 'select' && field.options) {
                                cellContent = `<select id="${fieldId}" ${requiredAttr}>`;
                                cellContent += `<option value="">请选择...</option>`;
                                field.options.forEach(option => {
                                    const selected = value === option.value ? ' selected' : '';
                                    cellContent += `<option value="${escapeHtml(option.value)}"${selected}>${escapeHtml(option.label)}</option>`;
                                });
                                cellContent += `</select>`;
                            } else if (field.type === 'text') {
                                cellContent = `<input type="text" id="${fieldId}" value="${escapeHtml(value)}" ${requiredAttr}>`;
                            } else if (field.type === 'time') {
                                cellContent = `<input type="time" id="${fieldId}" value="${escapeHtml(value)}" ${requiredAttr}>`;
                            } else if (field.type === 'date') {
                                cellContent = `<input type="date" id="${fieldId}" value="${escapeHtml(value)}" ${requiredAttr}>`;
                            } else {
                                // 数字类型 - 移除step属性以隐藏步进控件
                                const min = field.min !== undefined ? ` min="${field.min}"` : '';
                                const max = field.max !== undefined ? ` max="${field.max}"` : '';
                                cellContent = `<input type="number" id="${fieldId}" value="${escapeHtml(value)}" ${requiredAttr}${min}${max} style="appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;">`;
                            }
                        } else {
                            // 只读状态：显示文本
                            const maxLength = fieldWidth > 150 ? 20 : (fieldWidth > 100 ? 15 : 10);
                            if (field.type === 'text' && value.length > maxLength) {
                                cellContent = `<span title="${escapeHtml(value)}" style="cursor: help;">${escapeHtml(value.substring(0, maxLength))}...</span>`;
                            } else {
                                cellContent = escapeHtml(value);
                            }
                        }

                        html += `<td class="text-center align-middle" style="${cellStyle}">${cellContent}</td>`;
                    });
                });

                ungrouped.forEach((field, index) => {
                    const isLastField = index === ungrouped.length - 1;
                    const borderStyle = isLastField ? ' border-right: 2px solid #dee2e6;' : '';
                    const value = entry[field.key] || '';

                    // 为长文本字段添加省略号和提示，使用简单的响应式列宽
                    let displayValue = escapeHtml(value);

                    // 计算列宽（与表头保持一致）
                    const fieldWidth = calculateFieldWidth(field, screenSize);

                    let cellStyle = `width:${fieldWidth}px !important; min-width:${fieldWidth}px !important; max-width:${fieldWidth}px !important; font-size: 0.75rem !important; padding: 0.3rem 0.2rem !important;${borderStyle}`;

                    // 使用CSS处理文本溢出，不需要JavaScript截断

                    html += `<td class="text-center align-middle" style="${cellStyle}">${displayValue}</td>`;
                });

                // 查询模式下显示录入信息和修改信息
                if (isQueryMode && window.__SYSTEM_CONFIG__) {
                    if (window.__SYSTEM_CONFIG__.show_entry_info) {
                        html += `<td class="text-center align-middle text-muted" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">${escapeHtml(entry.created_by_name || '-')}</td>`;
                        html += `<td class="text-center align-middle text-muted" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">${entry.created_at ? formatDateTimeMultiLine(entry.created_at) : '-'}</td>`;
                    }
                    if (window.__SYSTEM_CONFIG__.show_modify_info) {
                        html += `<td class="text-center align-middle text-muted" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">${escapeHtml(entry.updated_by_name || '-')}</td>`;
                        html += `<td class="text-center align-middle text-muted" style="font-size: 0.75rem; padding: 0.3rem 0.2rem;">${entry.updated_at ? formatDateTimeMultiLine(entry.updated_at) : '-'}</td>`;
                    }
                }

                // 录入模式下显示操作列
                if (!isQueryMode) {
                    // 操作按钮
                    html += `
                            <td class="text-center align-middle">
                                <div class="btn-group btn-group-sm">`;

                if (isEditing) {
                    // 编辑状态：显示保存和取消按钮（查询模式下不显示保存按钮）
                    if (entry.id) {
                        // 编辑现有记录
                        if (!isQueryMode) {
                            html += `
                                <button class="btn btn-success" onclick="saveEditEntry(${entry.id})" title="保存">
                                    <i class="fa fa-save"></i>
                                </button>`;
                        }
                        html += `
                                <button class="btn btn-secondary" onclick="cancelEditEntry(${entry.id})" title="取消">
                                    <i class="fa fa-times"></i>
                                </button>`;
                    } else {
                        // 新增记录
                        if (!isQueryMode) {
                            html += `
                                <button class="btn btn-success" onclick="saveNewEntry()" title="保存">
                                    <i class="fa fa-save"></i>
                                </button>`;
                        }
                        html += `
                                <button class="btn btn-secondary" onclick="cancelNewEntry()" title="取消">
                                    <i class="fa fa-times"></i>
                                </button>`;
                    }
                } else {
                    // 只读状态：查询模式下不显示编辑删除按钮
                    if (isQueryMode) {
                        html += `<span class="text-muted small">仅供查看</span>`;
                    } else {
                        // 根据时间限制显示编辑和删除按钮
                        const canEdit = isEntryEditable(entry.entry_date);
                        if (canEdit) {
                            html += `
                                <button class="btn btn-outline-primary" onclick="editEntry(${entry.id})" title="编辑">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteEntry(${entry.id})" title="删除">
                                    <i class="fa fa-trash"></i>
                                </button>`;
                        } else {
                            html += `
                                <span class="text-muted small">不可编辑</span>`;
                        }
                    }
                }

                    html += `
                                </div>
                            </td>`;
                }

                html += `
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        container.innerHTML = html;

        // 注意：const变量会在函数结束时自动被垃圾回收，无需手动清理
    }

    // 分页切换函数
    window.changePage = function(page) {
        if (page < 1 || page > totalPages || page === currentPage) return;
        currentPage = page;
        renderEntryTable();
    };

    // 添加新行
    function addNewRow() {
        // 检查是否已有编辑中的行
        if (entryData.some(entry => entry.isEditing)) {
            showMessage('请先完成当前编辑的记录', 'warning');
            return;
        }

        // 在数据开头添加新的编辑行
        entryData.unshift({
            id: null,
            entry_date: new Date().toISOString().slice(0, 10),
            isEditing: true,
            ...templateFields.reduce((acc, field) => {
                acc[field.key] = '';
                return acc;
            }, {})
        });

        // 重新渲染表格
        renderEntryTable();
    }

    // 取消新增记录
    function cancelNewEntry() {
        // 移除新增的编辑行
        entryData = entryData.filter(entry => entry.id !== null);
        // 重新渲染表格
        renderEntryTable();
    }

    // 编辑记录
    function editEntry(entryId) {
        // 检查是否已有编辑中的行
        if (entryData.some(entry => entry.isEditing)) {
            showMessage('请先完成当前编辑的记录', 'warning');
            return;
        }

        // 查找记录
        const entry = entryData.find(e => e.id === entryId);
        if (!entry) {
            showMessage('记录不存在', 'error');
            return;
        }

        // 检查时间限制
        if (!isEntryEditable(entry.entry_date)) {
            showMessage('该记录不可编辑，超出时间限制', 'warning');
            return;
        }

        // 设置编辑状态
        entry.isEditing = true;
        // 重新渲染表格
        renderEntryTable();
    }

    // 取消编辑记录
    function cancelEditEntry(entryId) {
        const entry = entryData.find(e => e.id === entryId);
        if (entry) {
            entry.isEditing = false;
            // 重新渲染表格
            renderEntryTable();
        }
    }

    // 保存编辑的记录
    function saveEditEntry(entryId) {
        // 查询模式下禁用保存功能
        if (isQueryMode) {
            showMessage('查询模式下不允许保存数据', 'warning');
            return;
        }

        const entry = entryData.find(e => e.id === entryId);
        if (!entry) return;

        // 获取日期
        const entryDate = document.getElementById(`date-${entryId}`).value;

        // 检查时间限制
        if (!isEntryEditable(entryDate)) {
            showMessage('该日期不可录入，超出时间限制或为未来日期', 'warning');
            return;
        }
        if (!entryDate) {
            showMessage('请选择运行日期', 'warning');
            return;
        }

        // 收集字段数据并验证必填项
        const fieldData = {};
        let hasRequiredData = false;
        const missingRequired = [];

        templateFields.forEach(field => {
            const element = document.getElementById(`field-${field.key}-${entryId}`);
            if (element) {
                const value = element.value.trim();
                fieldData[field.key] = value;

                // 检查必填字段
                if (field.required && value === '') {
                    missingRequired.push(field.label);
                }

                if (value !== '') {
                    hasRequiredData = true;
                }
            }
        });

        // 检查必填字段
        if (missingRequired.length > 0) {
            showMessage(`请填写必填字段：${missingRequired.join('、')}`, 'warning');
            return;
        }

        if (!hasRequiredData) {
            showMessage('请至少填写一个字段', 'warning');
            return;
        }

        // 构建保存数据
        const entryDataToSave = {
            entry_date: entryDate,
            field_data: fieldData,
            action: 'update',
            id: entryId
        };

        // 获取当前选中设备的pump_id
        const deviceSelect = document.getElementById('deviceSelect');
        const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
        const pumpId = selectedOption.dataset.pumpId;

        const saveData = {
            report_id: entryReportId,
            device_id: pumpId || entryDeviceId, // 如果有pump_id就用pump_id，否则用device_id
            date: entryDate,
            entries: [entryDataToSave]
        };

        // 发送保存请求
        fetch(window.__REPORT_API__.saveData, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(saveData)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('记录更新成功', 'success');
                // 重新加载数据
                loadEntryData();
            } else {
                showMessage(result.message || '更新失败', 'error');
            }
        })
        .catch(error => {
            showMessage('网络错误，更新失败', 'error');
        });
    }

    // 保存新记录
    function saveNewEntry() {
        // 查询模式下禁用保存功能
        if (isQueryMode) {
            showMessage('查询模式下不允许保存数据', 'warning');
            return;
        }

        // 获取日期
        const entryDate = document.getElementById('date-new').value;
        if (!entryDate) {
            showMessage('请选择运行日期', 'warning');
            return;
        }

        // 检查时间限制
        if (!isEntryEditable(entryDate)) {
            showMessage('该日期不可录入，超出时间限制或为未来日期', 'warning');
            return;
        }

        // 收集字段数据并验证必填项
        const fieldData = {};
        let hasRequiredData = false;
        const missingRequired = [];

        templateFields.forEach(field => {
            const element = document.getElementById(`field-${field.key}-new`);
            if (element) {
                const value = element.value.trim();
                fieldData[field.key] = value;

                // 检查必填字段
                if (field.required && value === '') {
                    missingRequired.push(field.label);
                }

                if (value !== '') {
                    hasRequiredData = true;
                }
            }
        });

        // 检查必填字段
        if (missingRequired.length > 0) {
            showMessage(`请填写必填字段：${missingRequired.join('、')}`, 'warning');
            return;
        }

        if (!hasRequiredData) {
            showMessage('请至少填写一个字段', 'warning');
            return;
        }

        // 构建保存数据
        const entryDataToSave = {
            entry_date: entryDate,
            field_data: fieldData,
            action: 'add'
        };

        // 获取当前选中设备的pump_id
        const deviceSelect = document.getElementById('deviceSelect');
        const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
        const pumpId = selectedOption.dataset.pumpId;

        const saveData = {
            report_id: entryReportId,
            device_id: pumpId || entryDeviceId, // 如果有pump_id就用pump_id，否则用device_id
            date: entryDate,
            entries: [entryDataToSave]
        };

        // 发送保存请求
        fetch(window.__REPORT_API__.saveData, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(saveData)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('记录保存成功', 'success');
                // 重新加载数据
                loadEntryData();
            } else {
                showMessage(result.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            showMessage('网络错误，保存失败', 'error');
        });
    }



    // 保存记录
    function saveEntry(modal) {
        // 查询模式下禁用保存功能
        if (isQueryMode) {
            showMessage('查询模式下不允许保存数据', 'warning');
            return;
        }

        const form = document.getElementById('entryForm');
        if (!form) return;

        const formData = new FormData(form);
        const data = {
            report_id: entryReportId,
            device_id: entryDeviceId
        };

        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 验证必填字段
        const entryDate = data.entry_date;

        if (!entryDate) {
            showMessage('请填写日期', 'error');
            return;
        }

        // 发送保存请求
        fetch(window.__REPORT_API__.saveData, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage(data.id ? '记录更新成功' : '记录保存成功', 'success');
                modal.hide();
                loadEntryData(); // 重新加载数据
            } else {
                showMessage('保存失败: ' + (result.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            showMessage('网络错误，保存失败', 'error');
        });
    }

    // 编辑记录
    window.editEntry = function(entryId) {
        const entry = entryData.find(e => e.id == entryId);
        if (!entry) {
            showMessage('记录不存在', 'error');
            return;
        }

        // 检查时间限制
        if (!isEntryEditable(entry.entry_date)) {
            showMessage('该记录不可编辑，超出时间限制', 'warning');
            return;
        }

        // 创建编辑模态框
        const modalHtml = createEntryModal('编辑运行记录', entry);

        // 添加到页面
        let modalContainer = document.getElementById('entryModalContainer');
        if (!modalContainer) {
            modalContainer = document.createElement('div');
            modalContainer.id = 'entryModalContainer';
            document.body.appendChild(modalContainer);
        }
        modalContainer.innerHTML = modalHtml;

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('entryModal'));
        modal.show();

        // 绑定保存事件
        const saveBtn = document.getElementById('saveEntryBtn');
        if (saveBtn) {
            saveBtn.onclick = function() {
                saveEntry(modal);
            };
        }
    };

    // 删除记录
    window.deleteEntry = function(entryId) {
        // 查询模式下禁用删除功能
        if (isQueryMode) {
            showMessage('查询模式下不允许删除数据', 'warning');
            return;
        }

        // 查找记录
        const entry = entryData.find(e => e.id == entryId);
        if (!entry) {
            showMessage('记录不存在', 'error');
            return;
        }

        // 检查时间限制
        if (!isEntryEditable(entry.entry_date)) {
            showMessage('该记录不可删除，超出时间限制', 'warning');
            return;
        }

        if (!confirm('确定要删除这条记录吗？此操作不可撤销。')) return;

        const data = {
            report_id: entryReportId,
            device_id: entryDeviceId,
            id: entryId,
            action: 'delete'
        };

        fetch(window.__REPORT_API__.saveData, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('记录删除成功', 'success');
                loadEntryData(); // 重新加载数据
            } else {
                showMessage('删除失败: ' + (result.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            showMessage('网络错误，删除失败', 'error');
        });
    };

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateTimeStr;
        }
    }

    // 格式化日期时间为两行显示（日期在上，时间在下）
    function formatDateTimeMultiLine(dateTimeStr) {
        if (!dateTimeStr) return '-';
        try {
            const date = new Date(dateTimeStr);
            const dateStr = date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
            const timeStr = date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            return `<div style="line-height: 1.2;"><div>${dateStr}</div><div class="text-muted" style="font-size: 0.85em;">${timeStr}</div></div>`;
        } catch (e) {
            return dateTimeStr;
        }
    }

    // 导出数据（仅查询模式使用）
    window.exportData = function() {
        // TODO: 实现导出功能
        showMessage('导出功能开发中...', 'info');
    };

    // 创建录入模态框
    function createEntryModal(title, entryData) {
        let html = `
            <div class="modal fade" id="entryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${escapeHtml(title)}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="entryForm">
                                <input type="hidden" id="entryId" name="id" value="${entryData ? entryData.id : ''}">

                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label class="form-label">日期 *</label>
                                        <input type="date" class="form-control" id="entryDate" name="entry_date"
                                               value="${entryData ? entryData.entry_date : new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row g-3 mt-2">
        `;

        // 动态添加模板字段
        templateFields.forEach(field => {
            const fieldValue = entryData ? (entryData[field.key] || '') : '';
            const required = field.required ? 'required' : '';
            const min = field.min !== undefined ? `min="${field.min}"` : '';
            const max = field.max !== undefined ? `max="${field.max}"` : '';

            html += `<div class="col-md-6">`;
            html += `<label class="form-label">${escapeHtml(field.label)}${field.required ? ' *' : ''}</label>`;

            if (field.type === 'select' && field.options) {
                html += `<select class="form-select" name="${field.key}" ${required}>`;
                html += `<option value="">请选择...</option>`;
                field.options.forEach(option => {
                    const selected = fieldValue === option.value ? 'selected' : '';
                    html += `<option value="${escapeHtml(option.value)}" ${selected}>${escapeHtml(option.label)}</option>`;
                });
                html += `</select>`;
            } else if (field.type === 'textarea') {
                html += `<textarea class="form-control" name="${field.key}" rows="2" ${required}>${escapeHtml(fieldValue)}</textarea>`;
            } else {
                const inputType = field.type === 'number' ? 'number' : 'text';
                const numberStyle = field.type === 'number' ? ' style="appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;"' : '';
                html += `<input type="${inputType}" class="form-control" name="${field.key}"
                                value="${escapeHtml(fieldValue)}" ${required} ${min} ${max}${numberStyle}>`;
            }

            html += `</div>`;
        });

        html += `
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveEntryBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    // 工具函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDateTime(dateTimeStr) {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 格式化日期时间为两行显示（日期在上，时间在下）
    function formatDateTimeMultiLine(dateTimeStr) {
        if (!dateTimeStr) return '-';
        try {
            const date = new Date(dateTimeStr);
            const dateStr = date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
            const timeStr = date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            return `<div style="line-height: 1.2;"><div>${dateStr}</div><div class="text-muted" style="font-size: 0.85em;">${timeStr}</div></div>`;
        } catch (e) {
            return dateTimeStr;
        }
    }

    function showMessage(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        // 查找或创建消息容器
        let container = document.getElementById('messageContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'messageContainer';
            container.style.cssText = 'position:fixed;top:80px;right:20px;z-index:9999;max-width:400px;';
            document.body.appendChild(container);
        }

        // 简单替换消息内容
        container.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show">
                ${message.replace(/\n/g, '<br>')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 自动隐藏（除了错误消息）
        if (type !== 'error') {
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }
    }

    // 导出全局函数供HTML调用
    window.addNewRow = addNewRow;
    window.saveNewEntry = saveNewEntry;
    window.cancelNewEntry = cancelNewEntry;
    window.editEntry = editEntry;
    window.saveEditEntry = saveEditEntry;
    window.cancelEditEntry = cancelEditEntry;
    window.deleteEntry = deleteEntry;
    window.exportData = exportData;

})();
