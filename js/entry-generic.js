/**
 * 通用多模板录入页面交互逻辑
 * 支持泵类、空压机、风机、电机、自定义等多种设备模板
 */

// 全局变量
let entryReportId = 0;
let entryTemplateCode = '';
let entryDeviceId = 0;
let entryObjectId = 0;
let entryDate = '';
let editingSlots = new Set(); // 记录正在编辑的时间段
let tableData = {}; // 存储表格数据
let dutyStaffData = {}; // 存储值班人员数据
let currentTemplate = null; // 存储当前模板信息
let isQueryMode = false; // 是否为查询模式

// 优化的内存检查和清理
function checkMemoryUsage() {
    if (performance.memory) {
        const used = Math.round(performance.memory.usedJSHeapSize / 1048576);

        // 提高阈值到200MB，减少频繁清理
        if (used > 200) {
            console.warn('内存使用过高，开始清理:', used + 'MB');

            // 清理缓存
            if (deviceListCache) {
                deviceListCache.clear();
            }

            // 清理表格数据缓存
            if (tableData) {
                Object.keys(tableData).forEach(key => {
                    if (tableData[key] && typeof tableData[key] === 'object') {
                        tableData[key] = null;
                    }
                });
            }

            // 清理DOM中的大量数据
            const tables = document.querySelectorAll('table tbody');
            tables.forEach(tbody => {
                if (tbody.children.length > 100) {
                    // 只保留前50行，删除其余
                    while (tbody.children.length > 50) {
                        tbody.removeChild(tbody.lastChild);
                    }
                }
            });

            // 强制垃圾回收（如果可用）
            if (window.gc) {
                window.gc();
            }

            console.log('内存清理完成');
        }
    }
}

// 页面初始化（优化加载流程）
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.__REPORT_API__ !== 'undefined') {
        // 如果是连续报表，跳过entry-generic.js的初始化，让entry-continuous.js处理
        if (window.__REPORT_API__.reportType === 'continuous') {
            return;
        }

        entryReportId = window.__REPORT_API__.reportId;
        entryTemplateCode = window.__REPORT_API__.templateCode;
        isQueryMode = window.__REPORT_API__.isQueryMode || false;

        // 清除可能的旧缓存（防止不同页面间的缓存冲突）
        deviceListCache.clear();

        // 绑定事件
        bindEvents();

        // 初始化设备下拉列表，自动选择逻辑已在populateDeviceSelect中处理
        loadEntryDeviceList().catch(error => {
            showMessage('设备列表加载失败', 'error');
        });

        // 定期检查内存使用情况（降低频率）
        setInterval(checkMemoryUsage, 600000); // 每10分钟检查一次

        // 页面可见性变化时清理内存
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时清理内存
                setTimeout(checkMemoryUsage, 1000);
            }
        });
    }
});

// 检查 URL 参数并自动选中设备（优化：去除不必要的延迟）
function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const deviceId = urlParams.get('device_id');

    if (deviceId) {
        // 如果URL中有device_id参数，说明是从左侧菜单点击进入的
        // 此时设备列表应该已经被过滤了，直接选中对应设备
        const deviceSelect = document.getElementById('deviceSelect');
        if (deviceSelect && deviceSelect.options.length > 0) {
            // 查找匹配的设备选项
            let found = false;
            for (let i = 0; i < deviceSelect.options.length; i++) {
                if (deviceSelect.options[i].value === deviceId) {
                    deviceSelect.value = deviceId;
                    onDeviceChange();
                    // 如果有设备ID，自动触发查询
                    loadEntryData();
                    found = true;
                    break;
                }
            }

            // 如果没有找到匹配的设备，可能是过滤有问题，显示提示
            if (!found && deviceSelect.options.length === 1) {
                showMessage('未找到指定的设备，请检查设备配置', 'warning');
            }
        }
    }
}

// 绑定页面事件 - 简化版本
function bindEvents() {
    // 使用事件委托处理按钮点击
    document.addEventListener('click', function(e) {
        const target = e.target.closest('button');
        if (!target) return;

        const id = target.id;
        if (id === 'btnQuery') {
            loadEntryData();
        } else if (id === 'btnSave') {
            saveEntryData();
        }
    });

    // 设备选择变化
    const deviceSelect = document.getElementById('deviceSelect');
    if (deviceSelect) {
        deviceSelect.addEventListener('change', onDeviceChange);
    }

    // 日期变化时自动加载数据
    const dateInput = document.getElementById('entryDate');
    if (dateInput) {
        dateInput.addEventListener('change', function() {
            // 如果已选择设备，自动加载数据
            if (entryDeviceId > 0) {
                loadEntryData();
            }
        });
    }

    // 快捷键支持
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveEntryData();
        }
    });

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', function() {
        if (deviceListCache) {
            deviceListCache.clear();
        }
    });
}



// 设备列表缓存
let deviceListCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
const MAX_CACHE_SIZE = 5; // 最大5条缓存

// 加载设备列表（只显示配置给当前报表的设备）- 返回Promise以支持链式调用
function loadEntryDeviceList() {
    if (!window.__REPORT_API__ || !window.__REPORT_API__.getDevices) {
        return Promise.reject('API配置缺失');
    }

    // 检查缓存 - 按报表ID缓存
    const cacheKey = entryReportId.toString();
    const now = Date.now();
    const cachedData = deviceListCache.get(cacheKey);

    if (cachedData && (now - cachedData.timestamp) < CACHE_DURATION) {
        populateDeviceSelect(cachedData.devices, cachedData.autoSelect);
        return Promise.resolve(cachedData.devices);
    }

    // API URL已经包含了report_id参数，直接使用
    const apiUrl = window.__REPORT_API__.getDevices;

    return fetch(apiUrl)
        .then(response => response.json())
        .then(result => {
            // 检查API返回格式
            const devices = result.success ? result.data : (Array.isArray(result) ? result : []);
            const autoSelect = result.success ? result.auto_select : null;

            // 简单的缓存控制
            if (deviceListCache.size >= MAX_CACHE_SIZE) {
                deviceListCache.clear(); // 直接清空，更简单
            }

            deviceListCache.set(cacheKey, {
                devices: devices,
                autoSelect: autoSelect,
                timestamp: now
            });

            populateDeviceSelect(devices, autoSelect);
            return devices;
        })
        .catch(error => {
            showMessage('加载设备列表失败', 'error');

            const deviceSelect = document.getElementById('deviceSelect');
            if (deviceSelect) {
                deviceSelect.innerHTML = '<option value="">加载设备失败</option>';
            }
            throw error;
        });
}

// 填充设备选择下拉框
function populateDeviceSelect(devices, autoSelect) {
    const deviceSelect = document.getElementById('deviceSelect');
    if (!deviceSelect) return;

    // 清空现有选项
    deviceSelect.innerHTML = '<option value="">请选择设备...</option>';

    if (!Array.isArray(devices) || devices.length === 0) {
        deviceSelect.innerHTML = '<option value="">该报表未配置设备</option>';
        return;
    }

    // 添加设备选项
    devices.forEach((device, index) => {
        const option = document.createElement('option');
        option.value = device.device_id || device.id;
        option.dataset.objectId = device.object_id || device.pump_id || device.device_id || device.id;

        // 存储pump_id用于保存数据时使用
        if (device.pump_id) {
            option.dataset.pumpId = device.pump_id;
        }

        option.textContent = device.device_name || device.name;

        // 如果是泵类设备，显示泵号（格式：1#热媒泵）
        if (device.pump_no) {
            const deviceName = device.device_name || device.name || '';
            option.textContent = `${device.pump_no}${deviceName}`;
        }

        deviceSelect.appendChild(option);
    });

    // 自动选择设备逻辑
    let selectedDeviceId = null;

    if (autoSelect) {
        // 如果API返回了auto_select，优先使用它（这是正确的pump_id或device_id）
        selectedDeviceId = autoSelect;
    } else if (devices.length > 0) {
        // 如果没有auto_select，自动选择第一个设备
        const firstDevice = devices[0];
        selectedDeviceId = firstDevice.device_id || firstDevice.id;
    }

    if (selectedDeviceId) {
        deviceSelect.value = selectedDeviceId;
        // 触发设备变化事件，加载数据
        onDeviceChange();
    }
}

// 设备选择变化处理
function onDeviceChange() {
    const deviceSelect = document.getElementById('deviceSelect');
    if (!deviceSelect) return;

    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    entryDeviceId = parseInt(deviceSelect.value) || 0;
    entryObjectId = parseInt(selectedOption.dataset.objectId) || entryDeviceId;

    // 如果选择了设备，自动加载数据
    if (entryDeviceId > 0) {
        // 确保日期已设置，如果没有则设置为今天
        const dateInput = document.getElementById('entryDate');
        if (dateInput && !dateInput.value) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }

        // 自动加载数据
        loadEntryData();
    } else {
        // 清空表格容器
        const container = document.getElementById('entryTableContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fa fa-info-circle fa-2x mb-2"></i>
                    <p>请先选择设备和日期，然后点击"查询数据"加载录入表格</p>
                    <p class="small">模板类型：${entryTemplateCode}</p>
                </div>
            `;
        }
    }
}

// 加载录入数据
function loadEntryData() {
    const deviceSelect = document.getElementById('deviceSelect');
    const dateInput = document.getElementById('entryDate');

    if (!deviceSelect || !dateInput) {
        showMessage('页面元素缺失', 'error');
        return;
    }

    entryDeviceId = parseInt(deviceSelect.value) || 0;
    entryDate = dateInput.value;

    if (!entryDeviceId || !entryDate) {
        showMessage('请选择设备和日期', 'warning');
        return;
    }

    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    entryObjectId = parseInt(selectedOption.dataset.objectId) || entryDeviceId;

    // 显示加载状态
    const container = document.getElementById('entryTableContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">正在加载数据...</p>
            </div>
        `;
    }

    // 构建API URL
    const params = new URLSearchParams({
        report_id: entryReportId,
        device_id: entryDeviceId,
        object_id: entryObjectId,
        date: entryDate
    });

    const baseUrl = window.__REPORT_API__.loadData;
    const apiUrl = baseUrl + (baseUrl.includes('?') ? '&' : '?') + params.toString();

    fetch(apiUrl)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 保存数据到全局变量
                tableData = result.data || {};
                dutyStaffData = result.duty_staff || {};
                currentTemplate = result.template || null;
                generateEntryTable(tableData, currentTemplate);
                // 显示值班人员信息
                showDutyStaffForm(dutyStaffData);
            } else {
                showMessage(result.message || '数据加载失败', 'error');
                if (container) {
                    container.innerHTML = `
                        <div class="text-center text-danger py-4">
                            <i class="fa fa-exclamation-triangle fa-2x mb-2"></i>
                            <p>${result.message || '数据加载失败'}</p>
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            showMessage('数据加载失败', 'error');
            if (container) {
                container.innerHTML = `
                    <div class="text-center text-danger py-4">
                        <i class="fa fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>网络错误，请稍后重试</p>
                    </div>
                `;
            }
        });
}

// 生成录入表格
function generateEntryTable(data, template) {
    const container = document.getElementById('entryTableContainer');
    if (!container) return;

    // 根据模板类型生成表格结构
    let tableHtml = '';

    if (template && (template.fields_config || typeof template === 'string' || template.fields)) {
        tableHtml = generateTableFromTemplate(template, data);
    } else {
        tableHtml = generateGenericTable(data);
    }

    // 当无模板时给出轻量提示
    const noTemplateHint = (!template || (!template.fields_config && !template.fields && typeof template !== 'string'))
        ? '<div class="alert alert-warning mb-2"><i class="fa fa-info-circle me-1"></i>该报表未配置模板，将使用通用表格</div>'
        : '';

    // 生成操作按钮组
    const editingCount = editingSlots.size;
    let actionButtons = '';

    if (isQueryMode) {
        // 查询模式：显示导出Excel按钮
        actionButtons = `
            <div class="btn-group">
                <button class="btn btn-outline-success btn-sm" onclick="exportToExcel()" title="导出Excel">
                    <i class="fa fa-file-excel me-1"></i>导出Excel
                </button>
            </div>
        `;
    } else {
        // 录入模式：显示编辑和保存按钮
        if (editingCount > 1) {
            // 有多个时间段在编辑时，显示取消全部编辑按钮
            actionButtons = `
                <div class="btn-group">
                    <button class="btn btn-outline-danger btn-sm" onclick="cancelAllEdits()" title="取消所有编辑">
                        <i class="fa fa-times me-1"></i>取消全部编辑 (${editingCount})
                    </button>
                    <button class="btn btn-success btn-sm" onclick="saveAllData()" title="保存所有数据和值班人员">
                        <i class="fa fa-save me-1"></i>保存全部数据
                    </button>
                </div>
            `;
        } else {
            // 正常状态，显示编辑全部按钮
            actionButtons = `
                <div class="btn-group">
                    <button class="btn btn-outline-warning btn-sm" onclick="editAllSlots()" title="编辑所有时间段">
                        <i class="fa fa-edit me-1"></i>编辑全部
                    </button>
                    <button class="btn btn-success btn-sm" onclick="saveAllData()" title="保存所有数据和值班人员">
                        <i class="fa fa-save me-1"></i>保存全部数据
                    </button>
                </div>
            `;
        }
    }

    container.innerHTML = `
        ${noTemplateHint}
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted small">
                ${isQueryMode ? '数据仅供查看' :
                  (editingCount > 0 ? `正在编辑 ${editingCount} 个时间段` : '提示：点击每行的编辑按钮进行数据录入和修改')}
            </div>
            ${actionButtons}
        </div>
        <div class="table-responsive">
            ${tableHtml}
        </div>
    `;

    // --- 基于模板配置动态生成表格 ---
    function generateTableFromTemplate(template, data){
        let fields = [];
        try {
            const cfg = typeof template === 'string' ? JSON.parse(template||'{"fields":[]}')
                        : (template.fields_config ? JSON.parse(template.fields_config||'{"fields":[]}') : (template.fields||{fields:[]}));
            fields = (cfg && Array.isArray(cfg.fields)) ? cfg.fields : [];
        } catch (e) { fields = []; }
        if (!fields.length) {
            return generateGenericTable(data);
        }

        // 按分组整理字段
        const groups = {};
        const ungrouped = [];
        fields.forEach(f => {
            if (f.group) {
                if (!groups[f.group]) groups[f.group] = [];
                groups[f.group].push(f);
            } else {
                ungrouped.push(f);
            }
        });

        // 生成表头
        let html = `<table class="table table-hover align-middle"><thead>`;

        // 如果有分组，生成分组表头
        if (Object.keys(groups).length > 0) {
            html += `<tr class="table-secondary">`;
            html += `<th rowspan="2" style="width:90px" class="text-center">时间段</th>`;

            // 先输出分组表头
            Object.keys(groups).forEach(groupName => {
                html += `<th colspan="${groups[groupName].length}" class="text-center">${escapeHtml(groupName)}</th>`;
            });

            // 未分组字段
            if (ungrouped.length > 0) {
                html += `<th colspan="${ungrouped.length}" class="text-center">其他</th>`;
            }

            html += `<th rowspan="2" style="width:120px" class="text-center">备注</th>`;

            // 查询模式下添加录入信息和修改信息列
            if (isQueryMode && window.__SYSTEM_CONFIG__) {
                if (window.__SYSTEM_CONFIG__.show_entry_info) {
                    html += `<th colspan="2" style="width:200px" class="text-center">录入信息</th>`;
                }
                if (window.__SYSTEM_CONFIG__.show_modify_info) {
                    html += `<th colspan="2" style="width:200px" class="text-center">修改信息</th>`;
                }
            }

            // 查询模式下不显示操作列
            if (!isQueryMode) {
                html += `<th rowspan="2" style="width:100px" class="text-center">操作</th>`;
            }
            html += `</tr>`;

            // 字段名表头
            html += `<tr class="table-light">`;
            Object.keys(groups).forEach(groupName => {
                groups[groupName].forEach(f => {
                    const width = f.width ? ` style="width:${f.width}"` : '';
                    const label = f.unit ? `${f.label}(${f.unit})` : f.label;
                    const requiredMark = f.required ? '<span style="color:red;">*</span>' : '';
                    html += `<th${width} class="text-center">${escapeHtml(label || f.key)}${requiredMark}</th>`;
                });
            });
            ungrouped.forEach(f => {
                const width = f.width ? ` style="width:${f.width}"` : '';
                const label = f.unit ? `${f.label}(${f.unit})` : f.label;
                const requiredMark = f.required ? '<span style="color:red;">*</span>' : '';
                html += `<th${width} class="text-center">${escapeHtml(label || f.key)}${requiredMark}</th>`;
            });

            // 查询模式下添加录入信息和修改信息的子表头
            if (isQueryMode && window.__SYSTEM_CONFIG__) {
                if (window.__SYSTEM_CONFIG__.show_entry_info) {
                    html += `<th style="width:100px" class="text-center">录入人</th>`;
                    html += `<th style="width:100px" class="text-center">录入时间</th>`;
                }
                if (window.__SYSTEM_CONFIG__.show_modify_info) {
                    html += `<th style="width:100px" class="text-center">修改人</th>`;
                    html += `<th style="width:100px" class="text-center">修改时间</th>`;
                }
            }

            html += `</tr>`;
        } else {
            // 无分组时的简单表头
            html += `<tr><th style="width:90px" class="text-center">时间段</th>`;
            fields.forEach(f => {
                const width = f.width ? ` style="width:${f.width}"` : '';
                const label = f.unit ? `${f.label}(${f.unit})` : f.label;
                const requiredMark = f.required ? '<span style="color:red;">*</span>' : '';
                html += `<th${width} class="text-center">${escapeHtml(label || f.key)}${requiredMark}</th>`;
            });
            html += `<th style="width:120px" class="text-center">备注</th>`;

            // 查询模式下添加录入信息和修改信息列
            if (isQueryMode && window.__SYSTEM_CONFIG__) {
                if (window.__SYSTEM_CONFIG__.show_entry_info) {
                    html += `<th style="width:100px" class="text-center">录入人</th>`;
                    html += `<th style="width:100px" class="text-center">录入时间</th>`;
                }
                if (window.__SYSTEM_CONFIG__.show_modify_info) {
                    html += `<th style="width:100px" class="text-center">修改人</th>`;
                    html += `<th style="width:100px" class="text-center">修改时间</th>`;
                }
            }

            // 查询模式下不显示操作列
            if (!isQueryMode) {
                html += `<th style="width:100px" class="text-center">操作</th>`;
            }
            html += `</tr>`;
        }

        html += `</thead><tbody>`;

        // 生成数据行
        for (let h=0; h<24; h+=2){
            const start = String(h).padStart(2,'0')+':00';
            const end = String(h+2).padStart(2,'0')+':00';
            const slot = `${start}-${end}`;
            const rowData = data[slot] || {};

            // 检查时间段是否可录入和编辑状态
            const isDisabled = isTimeSlotDisabled(slot);
            const isEditing = editingSlots.has(slot);
            const hasData = Object.keys(rowData).some(key => key !== 'remark' && rowData[key]);
            const disabledAttr = isDisabled ? ' disabled' : '';
            let rowClass = '';
            if (isEditing) rowClass += ' editing-row';
            if (isDisabled) rowClass += ' disabled-row';

            html += `<tr data-slot="${slot}" class="${rowClass}">`;
            html += `<td class="text-center">${start}–${end}</td>`;

            // 按分组顺序输出字段
            const allFields = [];
            Object.keys(groups).forEach(groupName => {
                allFields.push(...groups[groupName]);
            });
            allFields.push(...ungrouped);

            allFields.forEach(f=>{
                const key = f.key;
                const type = (f.type||'number');
                const required = f.required ? 'required' : '';
                const min = (typeof f.min!== 'undefined' && f.min !== '') ? ` min="${f.min}"` : '';
                const max = (typeof f.max!== 'undefined' && f.max !== '') ? ` max="${f.max}"` : '';
                const val = rowData[key] || '';
                let cell = '';

                if (isEditing && !isDisabled) {
                    // 编辑状态：显示输入框
                    if (type === 'select') {
                        const opts = Array.isArray(f.options) ? f.options : [];
                        cell = `<select name="${slot}[${key}]" class="form-select form-select-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" ${required}>`
                             + `<option value=""></option>`
                             + opts.map(o=>{
                                 const v = String(o.value ?? ''); const lbl = String(o.label ?? v);
                                 const sel = (String(val) === v) ? ' selected' : '';
                                 return `<option value="${escapeHtml(v)}"${sel}>${escapeHtml(lbl)}</option>`;
                               }).join('')
                             + `</select>`;
                    } else if (type === 'text') {
                        cell = `<input name="${slot}[${key}]" type="text" class="form-control form-control-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" value="${escapeHtml(String(val))}" ${required}>`;
                    } else if (type === 'time' || type === 'date') {
                        cell = `<input name="${slot}[${key}]" type="${type}" class="form-control form-control-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" value="${escapeHtml(String(val))}" ${required}>`;
                    } else { // number 默认
                        cell = `<input name="${slot}[${key}]" type="number" class="form-control form-control-sm text-center"${min}${max} value="${escapeHtml(String(val))}" ${required} style="font-size: 0.75rem; padding: 0.25rem 0.5rem; appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;">`;
                    }
                } else {
                    // 只读状态：显示文本
                    if (type === 'select' && f.options) {
                        const option = f.options.find(o => o.value === val);
                        cell = escapeHtml(option ? option.label : val);
                    } else {
                        cell = escapeHtml(String(val));
                    }
                }
                html += `<td class="text-center">${cell}</td>`;
            });

            // 备注字段
            const remarkVal = rowData.remark || '';
            if (isEditing && !isDisabled) {
                html += `<td class="text-center"><input name="${slot}[remark]" type="text" class="form-control form-control-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" value="${escapeHtml(String(remarkVal))}"></td>`;
            } else {
                html += `<td class="text-center">${escapeHtml(String(remarkVal))}</td>`;
            }

            // 查询模式下显示录入信息和修改信息
            if (isQueryMode && window.__SYSTEM_CONFIG__) {
                const meta = rowData._meta || {};

                // 调试信息（仅在开发模式下显示）
                if (window.__DEBUG_MODE__) {
                    console.log('Row data for slot', slot, ':', rowData);
                    console.log('Meta data:', meta);
                }

                if (window.__SYSTEM_CONFIG__.show_entry_info) {
                    html += `<td class="text-center text-muted small">${escapeHtml(meta.created_by_name || '-')}</td>`;
                    html += `<td class="text-center text-muted small">${meta.created_at ? formatDateTimeMultiLine(meta.created_at) : '-'}</td>`;
                }

                if (window.__SYSTEM_CONFIG__.show_modify_info) {
                    html += `<td class="text-center text-muted small">${escapeHtml(meta.updated_by_name || '-')}</td>`;
                    html += `<td class="text-center text-muted small">${meta.updated_at ? formatDateTimeMultiLine(meta.updated_at) : '-'}</td>`;
                }
            }

            // 操作按钮（查询模式下不显示操作列）
            if (!isQueryMode) {
                html += `<td class="text-center" style="width: 100px;">`;
                if (!isDisabled) {
                    if (isEditing) {
                        // 编辑状态：显示保存和取消按钮
                        html += `
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-success btn-sm" onclick="saveSlot('${slot}')" title="保存">
                                    <i class="fa fa-save"></i>
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="cancelEditSlot('${slot}')" title="取消">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        `;
                    } else {
                        // 只读状态：显示编辑按钮
                        html += `
                            <button class="btn btn-outline-primary btn-sm" onclick="editSlot('${slot}')" title="编辑">
                                <i class="fa fa-edit"></i>
                            </button>
                        `;
                    }
                }
                html += `</td>`;
            }
            html += `</tr>`;
        }
        html += '</tbody></table>';
        return html;
    }

    // 简单 HTML 转义
    function escapeHtml(s){ const d=document.createElement('div'); d.textContent=String(s||''); return d.innerHTML; }

}


// 生成通用表格
function generateGenericTable(data) {
    let html = `
        <table class="table table-hover align-middle">
            <thead>
                <tr>
                    <th style="width:90px">时间段</th>
                    <th style="width:80px">开始时间</th><th style="width:80px">结束时间</th>
                    <th style="width:100px">数值1</th><th style="width:100px">数值2</th>
                    <th style="width:100px">数值3</th><th style="width:100px">数值4</th>
                    <th style="width:120px">备注</th>
                </tr>
            </thead>
            <tbody>
    `;

    for (let h = 0; h < 24; h += 2) {
        const start = String(h).padStart(2, '0') + ':00';
        const end = String(h + 2).padStart(2, '0') + ':00';
        const slot = `${start}-${end}`;
        const rowData = data[slot] || {};

        // 检查时间段是否可录入
        const isDisabled = isTimeSlotDisabled(slot);
        const disabledAttr = isDisabled ? ' disabled' : '';
        const rowClass = isDisabled ? ' disabled-row' : '';

        html += `<tr data-slot="${slot}" class="${rowClass}">`;
        html += `<td class="text-center">${start}–${end}${isDisabled ? ' <small class="text-muted">(禁用)</small>' : ''}</td>`;
        html += `<td class="text-center"><input name="${slot}[start_time]" type="time" class="form-control form-control-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" value="${rowData.start_time || ''}"${disabledAttr}></td>`;
        html += `<td class="text-center"><input name="${slot}[end_time]" type="time" class="form-control form-control-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" value="${rowData.end_time || ''}"${disabledAttr}></td>`;
        html += `<td class="text-center"><input name="${slot}[value1]" type="number" class="form-control form-control-sm text-center" value="${rowData.value1 || ''}"${disabledAttr} style="font-size: 0.75rem; padding: 0.25rem 0.5rem; appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;"></td>`;
        html += `<td class="text-center"><input name="${slot}[value2]" type="number" class="form-control form-control-sm text-center" value="${rowData.value2 || ''}"${disabledAttr} style="font-size: 0.75rem; padding: 0.25rem 0.5rem; appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;"></td>`;
        html += `<td class="text-center"><input name="${slot}[value3]" type="number" class="form-control form-control-sm text-center" value="${rowData.value3 || ''}"${disabledAttr} style="font-size: 0.75rem; padding: 0.25rem 0.5rem; appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;"></td>`;
        html += `<td class="text-center"><input name="${slot}[value4]" type="number" class="form-control form-control-sm text-center" value="${rowData.value4 || ''}"${disabledAttr} style="font-size: 0.75rem; padding: 0.25rem 0.5rem; appearance: textfield; -moz-appearance: textfield; -webkit-appearance: none;"></td>`;
        html += `<td class="text-center"><input name="${slot}[remark]" type="text" class="form-control form-control-sm text-center" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" value="${rowData.remark || ''}"${disabledAttr}></td>`;
        html += `</tr>`;
    }

    html += '</tbody></table>';
    return html;
}

// 编辑时间段 - 简化版本
function editSlot(slot) {
    // 查询模式下禁用编辑
    if (isQueryMode) {
        showMessage('查询模式下不允许编辑数据', 'warning');
        return;
    }

    // 检查该时间段是否已在编辑
    if (editingSlots.has(slot)) {
        showMessage('该时间段已在编辑状态', 'info');
        return;
    }

    // 检查时间段是否可编辑
    if (isTimeSlotDisabled(slot)) {
        showMessage('该时间段不可编辑', 'warning');
        return;
    }

    editingSlots.add(slot);
    // 重新生成表格（简单但有效）
    generateEntryTable(tableData, currentTemplate);
}

// 取消编辑时间段 - 简化版本
function cancelEditSlot(slot) {
    editingSlots.delete(slot);
    generateEntryTable(tableData, currentTemplate);
}



// HTML转义函数（如果不存在的话）
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = String(text || '');
    return div.innerHTML;
}

// 编辑全部时间段
function editAllSlots() {
    if (isQueryMode) {
        showMessage('查询模式下不允许编辑数据', 'warning');
        return;
    }

    for (let h = 0; h < 24; h += 2) {
        const start = String(h).padStart(2, '0') + ':00';
        const end = String(h + 2).padStart(2, '0') + ':00';
        const slot = `${start}-${end}`;

        if (!isTimeSlotDisabled(slot)) {
            editingSlots.add(slot);
        }
    }

    generateEntryTable(tableData, currentTemplate);
}

// 取消全部编辑
function cancelAllEdits() {
    editingSlots.clear();
    generateEntryTable(tableData, currentTemplate);
}



// 保存单个时间段
function saveSlot(slot) {
    // 查询模式下禁用保存
    if (isQueryMode) {
        showMessage('查询模式下不允许保存数据', 'warning');
        return;
    }

    if (!entryReportId || !entryDeviceId || !entryDate) {
        showMessage('请先查询数据', 'warning');
        return;
    }

    // 收集该时间段的数据
    const row = document.querySelector(`tr[data-slot="${slot}"]`);
    if (!row) return;

    const inputs = row.querySelectorAll('input, select');
    const rowData = {};
    let hasData = false;
    const missingRequired = [];

    // 获取模板字段配置用于验证必填项
    let templateFields = [];
    if (currentTemplate) {
        try {
            const cfg = typeof currentTemplate === 'string' ? JSON.parse(currentTemplate||'{"fields":[]}')
                        : (currentTemplate.fields_config ? JSON.parse(currentTemplate.fields_config||'{"fields":[]}') : (currentTemplate.fields||{fields:[]}));
            templateFields = (cfg && Array.isArray(cfg.fields)) ? cfg.fields : [];
        } catch (e) { templateFields = []; }
    }

    inputs.forEach(input => {
        const fieldName = input.name.replace(`${slot}[`, '').replace(']', '');
        const value = input.value.trim();
        rowData[fieldName] = value;
        if (value !== '') hasData = true;

        // 检查必填项
        const field = templateFields.find(f => f.key === fieldName);
        if (field && field.required && value === '') {
            missingRequired.push(field.label || fieldName);
        }
    });

    // 验证必填项
    if (missingRequired.length > 0) {
        showMessage(`请填写必填字段：${missingRequired.join('、')}`, 'warning');
        return;
    }

    // 获取当前选中设备的pump_id
    const deviceSelect = document.getElementById('deviceSelect');
    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    const pumpId = selectedOption.dataset.pumpId;

    // 构建保存数据 - 单行保存时保持现有的值班人员数据
    const saveData = {
        report_id: entryReportId,
        device_id: pumpId || entryDeviceId, // 如果有pump_id就用pump_id，否则用device_id
        object_id: entryObjectId,
        date: entryDate,
        rows: { [slot]: rowData },
        duty_staff: dutyStaffData // 使用全局保存的值班人员数据，而不是重新收集
    };

    // 发送保存请求
    fetch(window.__REPORT_API__.saveData, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('保存成功', 'success');
            // 退出编辑状态
            editingSlots.delete(slot);
            // 更新本地数据
            tableData[slot] = rowData;
            // 只重新渲染表格
            generateEntryTable(tableData, currentTemplate);
        } else {
            showMessage(result.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        showMessage('网络错误，保存失败', 'error');
    });
}

// 保存录入数据
function saveEntryData(saveAll = false) {
    if (!entryReportId || !entryDeviceId || !entryDate) {
        showMessage('请先查询数据', 'warning');
        return;
    }

    // 收集表单数据
    const rows = {};
    const tableRows = document.querySelectorAll('#entryTableContainer tr[data-slot]');
    const allMissingRequired = [];

    // 获取模板字段配置用于验证必填项
    let templateFields = [];
    if (currentTemplate) {
        try {
            const cfg = typeof currentTemplate === 'string' ? JSON.parse(currentTemplate||'{"fields":[]}')
                        : (currentTemplate.fields_config ? JSON.parse(currentTemplate.fields_config||'{"fields":[]}') : (currentTemplate.fields||{fields:[]}));
            templateFields = (cfg && Array.isArray(cfg.fields)) ? cfg.fields : [];
        } catch (e) { templateFields = []; }
    }

    tableRows.forEach(row => {
        const slot = row.dataset.slot;
        const inputs = row.querySelectorAll('input, select');
        const rowData = {};
        let hasData = false;
        const missingRequired = [];

        inputs.forEach(input => {
            const fieldName = input.name.replace(`${slot}[`, '').replace(']', '');
            const value = input.value.trim();
            rowData[fieldName] = value;
            if (value !== '') hasData = true;

            // 检查必填项
            const field = templateFields.find(f => f.key === fieldName);
            if (field && field.required && value === '') {
                missingRequired.push(`${slot} - ${field.label || fieldName}`);
            }
        });

        // 如果是保存全部或者该行有数据，则包含该行
        if (saveAll || hasData) {
            rows[slot] = rowData;
            // 只有在保存该行时才检查必填项
            allMissingRequired.push(...missingRequired);
        }
    });

    // 验证必填项
    if (allMissingRequired.length > 0) {
        showMessage(`请填写必填字段：\n${allMissingRequired.join('\n')}`, 'warning');
        return;
    }

    if (Object.keys(rows).length === 0) {
        showMessage('没有数据需要保存', 'warning');
        return;
    }

    // 收集值班人员信息
    const dutyStaff = collectDutyStaffData();

    // 获取当前选中设备的pump_id
    const deviceSelect = document.getElementById('deviceSelect');
    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    const pumpId = selectedOption.dataset.pumpId;

    // 准备保存数据
    const saveData = {
        report_id: entryReportId,
        device_id: pumpId || entryDeviceId, // 如果有pump_id就用pump_id，否则用device_id
        object_id: entryObjectId,
        date: entryDate,
        rows: rows,
        duty_staff: dutyStaff
    };

    // 显示保存状态
    showMessage('正在保存数据...', 'info');

    fetch(window.__REPORT_API__.saveData, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            let message = `保存成功：${result.saved || 0} 条记录`;
            if (result.errors && result.errors.length > 0) {
                message += `\n警告：${result.errors.join('; ')}`;
            }
            showMessage(message, 'success');
        } else {
            showMessage(result.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        showMessage('网络错误，保存失败', 'error');
    });
}

// 显示消息提示 - 优化版本，防止消息累积
function showMessage(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    // 查找或创建消息容器
    let container = document.getElementById('messageContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'messageContainer';
        container.style.cssText = 'position:fixed;top:80px;right:20px;z-index:9999;max-width:400px;';
        document.body.appendChild(container);
    }

    // 简单替换消息内容
    container.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show">
            ${message.replace(/\n/g, '<br>')}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 自动隐藏（除了错误消息）
    if (type !== 'error') {
        setTimeout(() => {
            container.innerHTML = '';
        }, 3000);
    }
}

// 检查时间段是否禁用（基于时间限制配置）
function isTimeSlotDisabled(slot) {
    if (!window.__REPORT_CONFIG__ || !entryDate) return false;

    const config = window.__REPORT_CONFIG__;
    const timeLimitDays = config.time_limit_days || 2;
    const timeLimitType = config.time_limit_type || 'day';

    // 获取用户角色
    const userRoles = window.__USER_ROLES__ || [];
    const isAdmin = userRoles.includes('admin');
    const isManager = userRoles.includes('mod'); // 普通管理员角色代码是 'mod'
    const isRegularUser = !isAdmin && !isManager;

    // 解析时间段
    const [, endTime] = slot.split('-');
    const slotDateTime = new Date(entryDate + ' ' + endTime);
    const now = new Date();

    // 管理员不受任何限制
    if (isAdmin) return false;

    // 所有用户（包括普通管理员）都不能填写未来时间
    if (slotDateTime > now) return true;

    // 普通管理员不受时间限制，但不能填写未来时间
    if (isManager) return false;

    // 普通用户受时间限制
    if (timeLimitType === 'unlimited') return false;

    // 按自然日计算时间限制
    if (timeLimitType === 'day') {
        // 获取当前日期（不包含时间）
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // 获取数据日期（不包含时间）
        const dataDate = new Date(entryDate);
        dataDate.setHours(0, 0, 0, 0);

        // 计算允许编辑的最早日期
        const earliestDate = new Date(today);
        earliestDate.setDate(today.getDate() - timeLimitDays + 1); // +1是因为包含今天

        // 如果数据日期早于允许编辑的最早日期，则禁用
        return dataDate < earliestDate;
    }

    // 其他时间限制类型保持原有逻辑
    let limitDate = new Date(now);
    switch (timeLimitType) {
        case 'hour':
            limitDate.setHours(limitDate.getHours() - timeLimitDays);
            break;
        case 'week':
            limitDate.setDate(limitDate.getDate() - (timeLimitDays * 7));
            break;
        case 'month':
            limitDate.setMonth(limitDate.getMonth() - timeLimitDays);
            break;
    }

    return slotDateTime < limitDate;
}

// 检查值班人员是否可编辑
function isDutyStaffEditable() {
    if (!window.__REPORT_CONFIG__ || !entryDate) return true;

    const config = window.__REPORT_CONFIG__;
    const timeLimitDays = config.time_limit_days || 2;
    const timeLimitType = config.time_limit_type || 'day';

    // 获取用户角色
    const userRoles = window.__USER_ROLES__ || [];
    const isAdmin = userRoles.includes('admin');
    const isManager = userRoles.includes('mod'); // 普通管理员角色代码是 'mod'
    const isRegularUser = !isAdmin && !isManager;

    // 检查日期是否在允许范围内
    const selectedDate = new Date(entryDate);
    selectedDate.setHours(0, 0, 0, 0);
    const now = new Date();
    now.setHours(0, 0, 0, 0);

    // 管理员不受任何限制
    if (isAdmin) return true;

    // 所有用户（包括普通管理员）都不能编辑未来日期
    if (selectedDate > now) return false;

    // 普通管理员不受时间限制，但不能编辑未来日期
    if (isManager) return true;

    // 普通用户受时间限制
    if (timeLimitType === 'unlimited') return true;

    // 按自然日计算时间限制（与时间段逻辑保持一致）
    if (timeLimitType === 'day') {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const earliestDate = new Date(today);
        earliestDate.setDate(today.getDate() - timeLimitDays + 1);

        return selectedDate >= earliestDate;
    }

    // 其他时间限制类型
    let limitDate = new Date(now);
    switch (timeLimitType) {
        case 'hour':
            limitDate.setHours(limitDate.getHours() - timeLimitDays);
            break;
        case 'week':
            limitDate.setDate(limitDate.getDate() - (timeLimitDays * 7));
            break;
        case 'month':
            limitDate.setMonth(limitDate.getMonth() - timeLimitDays);
            break;
    }

    return selectedDate >= limitDate;
}

// 值班人员编辑状态
let isDutyStaffEditing = false;

// 显示值班人员表单
function showDutyStaffForm(dutyStaff) {
    const container = document.getElementById('dutyStaffContainer');
    if (!container) return;

    const config = window.__REPORT_CONFIG__ || {};
    const dutyConfig = config.duty_staff || {};

    // 如果未启用值班人功能，隐藏容器
    if (!dutyConfig.enabled) {
        container.style.display = 'none';
        return;
    }

    const dayLabel = dutyConfig.day_label || '白班值班人';
    const nightLabel = dutyConfig.night_label || '夜班值班人';
    const required = dutyConfig.required ? ' required' : '';

    // 检查值班人员编辑权限
    const dayStaff = dutyStaff.day_shift_staff || '';
    const nightStaff = dutyStaff.night_shift_staff || '';
    const canEdit = isDutyStaffEditable();

    if (canEdit && isDutyStaffEditing) {
        // 编辑状态：显示输入框和保存/取消按钮
        container.innerHTML = `
            <div class="mt-3 mb-3" style="padding: 15px; border-top: 1px solid #dee2e6; background-color: #f8f9fa;">
                <div class="d-flex align-items-center justify-content-between flex-wrap">
                    <div class="d-flex align-items-center mb-2">
                        <strong class="me-3">值班人员信息</strong>
                    </div>
                    <div class="d-flex align-items-center gap-3 flex-wrap">
                        <div class="d-flex align-items-center">
                            <span class="me-2 text-nowrap">${dayLabel}：</span>
                            <input id="dayShiftStaff" type="text" class="form-control form-control-sm" style="width: 120px;"
                                   value="${dayStaff}"
                                   placeholder="请输入${dayLabel}"${required}>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="me-2 text-nowrap">${nightLabel}：</span>
                            <input id="nightShiftStaff" type="text" class="form-control form-control-sm" style="width: 120px;"
                                   value="${nightStaff}"
                                   placeholder="请输入${nightLabel}"${required}>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <button class="btn btn-primary btn-sm" onclick="saveDutyStaff()" title="保存值班人员">
                                <i class="fa fa-save me-1"></i>保存
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="cancelEditDutyStaff()" title="取消编辑">
                                <i class="fa fa-times me-1"></i>取消
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        // 只读状态：显示文本和编辑按钮
        container.innerHTML = `
            <div class="mt-3 mb-3" style="padding: 15px; border-top: 1px solid #dee2e6; background-color: #f8f9fa;">
                <div class="d-flex align-items-center justify-content-between flex-wrap">
                    <div class="d-flex align-items-center mb-2">
                        <strong class="me-3">值班人员信息</strong>
                    </div>
                    <div class="d-flex align-items-center gap-4 flex-wrap">
                        <div class="d-flex align-items-center">
                            <span class="me-2 text-nowrap">${dayLabel}：</span>
                            <span class="text-muted">${dayStaff || '未填写'}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="me-2 text-nowrap">${nightLabel}：</span>
                            <span class="text-muted">${nightStaff || '未填写'}</span>
                        </div>
                        ${canEdit && !isQueryMode ? `
                            <button class="btn btn-outline-primary btn-sm" onclick="editDutyStaff()" title="编辑值班人员">
                                <i class="fa fa-edit me-1"></i>编辑值班人员
                            </button>
                        ` : `
                            <small class="text-muted">${isQueryMode ? '数据仅供查看' : '该日期不可编辑值班人员'}</small>
                        `}
                    </div>
                </div>
            </div>
        `;
    }

    container.style.display = 'block';
}

// 收集值班人员数据
function collectDutyStaffData() {
    const config = window.__REPORT_CONFIG__ || {};
    const dutyConfig = config.duty_staff || {};

    if (!dutyConfig.enabled) return null;

    const dayShiftInput = document.getElementById('dayShiftStaff');
    const nightShiftInput = document.getElementById('nightShiftStaff');

    return {
        day_shift_staff: dayShiftInput ? dayShiftInput.value.trim() : '',
        night_shift_staff: nightShiftInput ? nightShiftInput.value.trim() : ''
    };
}

// 编辑值班人员
function editDutyStaff() {
    // 查询模式下禁用编辑
    if (isQueryMode) {
        showMessage('查询模式下不允许编辑值班人员', 'warning');
        return;
    }

    isDutyStaffEditing = true;
    // 只重新渲染值班人员表单，不刷新整个页面
    showDutyStaffForm(dutyStaffData);
}

// 取消编辑值班人员
function cancelEditDutyStaff() {
    isDutyStaffEditing = false;
    // 只重新渲染值班人员表单，不刷新整个页面
    showDutyStaffForm(dutyStaffData);
}

// 保存值班人员信息
function saveDutyStaff() {
    // 查询模式下禁用保存
    if (isQueryMode) {
        showMessage('查询模式下不允许保存数据', 'warning');
        return;
    }

    if (!entryReportId || !entryDeviceId || !entryDate) {
        showMessage('请先查询数据', 'warning');
        return;
    }

    // 检查是否可编辑值班人员
    if (!isDutyStaffEditable()) {
        showMessage('该日期不可编辑值班人员信息', 'warning');
        return;
    }

    const currentDutyStaffData = collectDutyStaffData();
    if (!currentDutyStaffData) {
        showMessage('值班人员功能未启用', 'warning');
        return;
    }

    // 检查必填字段 - 修改为只需要填写其中一个即可
    const config = window.__REPORT_CONFIG__ || {};
    const dutyConfig = config.duty_staff || {};

    if (dutyConfig.required) {
        const dayStaff = currentDutyStaffData.day_shift_staff?.trim() || '';
        const nightStaff = currentDutyStaffData.night_shift_staff?.trim() || '';

        // 只要填写了白班或夜班其中一个即可
        if (!dayStaff && !nightStaff) {
            showMessage('请至少填写白班或夜班值班人员信息', 'warning');
            return;
        }
    }

    // 获取当前选中设备的pump_id
    const deviceSelect = document.getElementById('deviceSelect');
    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    const pumpId = selectedOption.dataset.pumpId;

    // 构建保存数据
    const saveData = {
        report_id: entryReportId,
        device_id: pumpId || entryDeviceId, // 如果有pump_id就用pump_id，否则用device_id
        object_id: entryObjectId,
        date: entryDate,
        rows: {}, // 空的行数据，只保存值班人员
        duty_staff: currentDutyStaffData
    };

    // 发送保存请求
    fetch(window.__REPORT_API__.saveData, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('值班人员信息保存成功', 'success');
            // 保存成功后退出编辑状态
            isDutyStaffEditing = false;
            // 更新全局值班人员数据
            dutyStaffData.day_shift_staff = currentDutyStaffData.day_shift_staff;
            dutyStaffData.night_shift_staff = currentDutyStaffData.night_shift_staff;
            // 只重新渲染值班人员表单
            showDutyStaffForm(dutyStaffData);
        } else {
            showMessage(result.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        showMessage('网络错误，保存失败', 'error');
    });
}

// 保存所有数据（包括值班人员）
function saveAllData() {
    // 查询模式下禁用保存
    if (isQueryMode) {
        showMessage('查询模式下不允许保存数据', 'warning');
        return;
    }

    if (!entryReportId || !entryDeviceId || !entryDate) {
        showMessage('请先查询数据', 'warning');
        return;
    }

    // 如果有正在编辑的行，提示用户
    if (editingSlots.size > 0) {
        if (!confirm(`当前有 ${editingSlots.size} 个时间段正在编辑中。\n\n点击确定将保存所有数据（包括正在编辑的），点击取消返回继续编辑。`)) {
            return;
        }
    }

    // 收集所有表格数据
    const rows = {};
    const tableRows = document.querySelectorAll('#entryTableContainer tr[data-slot]');
    const allMissingRequired = [];

    // 获取模板字段配置用于验证必填项
    let templateFields = [];
    if (currentTemplate) {
        try {
            const cfg = typeof currentTemplate === 'string' ? JSON.parse(currentTemplate||'{"fields":[]}')
                        : (currentTemplate.fields_config ? JSON.parse(currentTemplate.fields_config||'{"fields":[]}') : (currentTemplate.fields||{fields:[]}));
            templateFields = (cfg && Array.isArray(cfg.fields)) ? cfg.fields : [];
        } catch (e) { templateFields = []; }
    }

    tableRows.forEach(row => {
        const slot = row.dataset.slot;
        let rowData = {};
        const missingRequired = [];

        // 如果该行正在编辑，从输入框收集数据
        if (editingSlots.has(slot)) {
            const inputs = row.querySelectorAll('input, select');
            inputs.forEach(input => {
                const fieldName = input.name.replace(`${slot}[`, '').replace(']', '');
                const value = input.value.trim();
                rowData[fieldName] = value;

                // 检查必填项
                const field = templateFields.find(f => f.key === fieldName);
                if (field && field.required && value === '') {
                    missingRequired.push(`${slot} - ${field.label || fieldName}`);
                }
            });
        } else {
            // 否则使用已保存的数据
            rowData = tableData[slot] || {};
        }

        // 只保存有数据的行
        const hasData = Object.keys(rowData).some(key => key !== 'remark' && rowData[key]);
        if (hasData || rowData.remark) {
            rows[slot] = rowData;
            // 只有在保存该行时才检查必填项
            allMissingRequired.push(...missingRequired);
        }
    });

    // 验证必填项
    if (allMissingRequired.length > 0) {
        showMessage(`请填写必填字段：\n${allMissingRequired.join('\n')}`, 'warning');
        return;
    }

    // 使用全局保存的值班人员数据
    const currentDutyStaffData = dutyStaffData;

    // 检查值班人员必填 - 修改为只需要填写其中一个即可
    const config = window.__REPORT_CONFIG__ || {};
    const dutyConfig = config.duty_staff || {};

    if (dutyConfig.enabled && dutyConfig.required && currentDutyStaffData) {
        const dayStaff = currentDutyStaffData.day_shift_staff?.trim() || '';
        const nightStaff = currentDutyStaffData.night_shift_staff?.trim() || '';

        // 只要填写了白班或夜班其中一个即可
        if (!dayStaff && !nightStaff) {
            showMessage('请至少填写白班或夜班值班人员信息', 'warning');
            return;
        }
    }

    // 获取当前选中设备的pump_id
    const deviceSelect = document.getElementById('deviceSelect');
    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    const pumpId = selectedOption.dataset.pumpId;

    // 构建保存数据
    const saveData = {
        report_id: entryReportId,
        device_id: pumpId || entryDeviceId, // 如果有pump_id就用pump_id，否则用device_id
        object_id: entryObjectId,
        date: entryDate,
        rows: rows,
        duty_staff: currentDutyStaffData
    };

    // 发送保存请求
    fetch(window.__REPORT_API__.saveData, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('所有数据保存成功', 'success');
            // 清空所有编辑状态
            editingSlots.clear();
            // 更新本地数据
            Object.keys(rows).forEach(slot => {
                tableData[slot] = rows[slot];
            });
            // 如果保存了值班人员数据，也更新本地值班人员数据
            if (currentDutyStaffData) {
                dutyStaffData.day_shift_staff = currentDutyStaffData.day_shift_staff;
                dutyStaffData.night_shift_staff = currentDutyStaffData.night_shift_staff;
            }
            // 重新渲染表格
            generateEntryTable(tableData, currentTemplate);
        } else {
            showMessage(result.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        showMessage('网络错误，保存失败', 'error');
    });
}



// 编辑所有时间段
function editAllSlots() {
    // 查询模式下禁用编辑
    if (isQueryMode) {
        showMessage('查询模式下不允许编辑数据', 'warning');
        return;
    }

    // 获取所有时间段
    const tableRows = document.querySelectorAll('#entryTableContainer tr[data-slot]');
    const availableSlots = [];

    tableRows.forEach(row => {
        const slot = row.dataset.slot;
        // 检查时间段是否可编辑（不是禁用状态且未在编辑中）
        if (!isTimeSlotDisabled(slot) && !editingSlots.has(slot)) {
            availableSlots.push(slot);
        }
    });

    if (availableSlots.length === 0) {
        showMessage('没有可编辑的时间段', 'info');
        return;
    }

    // 确认操作
    if (!confirm(`确定要编辑所有 ${availableSlots.length} 个时间段吗？\n\n这将使所有时间段进入编辑状态，您可以同时编辑多个时间段。`)) {
        return;
    }

    // 将所有可编辑的时间段设为编辑状态
    availableSlots.forEach(slot => {
        editingSlots.add(slot);
    });

    // 重新渲染表格，但不刷新整个页面
    generateEntryTable(tableData, currentTemplate);
    showDutyStaffForm(dutyStaffData);

    showMessage(`已开启 ${availableSlots.length} 个时间段的编辑模式`, 'success');
}

// 取消所有编辑
function cancelAllEdits() {
    if (editingSlots.size === 0) {
        showMessage('当前没有正在编辑的时间段', 'info');
        return;
    }

    const editingCount = editingSlots.size;

    if (!confirm(`确定要取消所有 ${editingCount} 个时间段的编辑吗？\n\n注意：未保存的修改将会丢失。`)) {
        return;
    }

    // 清空所有编辑状态
    editingSlots.clear();

    // 重新渲染表格，但不刷新整个页面
    generateEntryTable(tableData, currentTemplate);
    showDutyStaffForm(dutyStaffData);

    showMessage(`已取消 ${editingCount} 个时间段的编辑`, 'info');
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateTimeStr;
    }
}

// 格式化日期时间为两行显示（日期在上，时间在下）
function formatDateTimeMultiLine(dateTimeStr) {
    if (!dateTimeStr) return '-';
    try {
        const date = new Date(dateTimeStr);
        const dateStr = date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
        const timeStr = date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        return `<div style="line-height: 1.2;"><div>${dateStr}</div><div class="text-muted" style="font-size: 0.85em;">${timeStr}</div></div>`;
    } catch (e) {
        return dateTimeStr;
    }
}

// 导出Excel功能（占位函数，后续实现）
function exportToExcel() {
    if (!entryReportId || !entryDeviceId || !entryDate) {
        showMessage('请先查询数据', 'warning');
        return;
    }

    // TODO: 实现Excel导出功能
    showMessage('Excel导出功能开发中...', 'info');
}

// 导出全局函数
window.editSlot = editSlot;
window.cancelEditSlot = cancelEditSlot;
window.saveSlot = saveSlot;
window.editDutyStaff = editDutyStaff;
window.cancelEditDutyStaff = cancelEditDutyStaff;
window.saveDutyStaff = saveDutyStaff;
window.saveAllData = saveAllData;
window.editAllSlots = editAllSlots;
window.cancelAllEdits = cancelAllEdits;
window.exportToExcel = exportToExcel;
