/**
 * 前端菜单管理器
 * 负责动态加载菜单、识别静态页面、处理导航
 */
class FrontendMenuManager {
    constructor() {
        this.menuData = null;
        this.staticPages = [];
        this.currentPage = null;
        this.baseUrl = this.getBaseUrl();
    }

    /**
     * 获取基础URL
     */
    getBaseUrl() {
        const scripts = document.getElementsByTagName('script');
        for (let script of scripts) {
            if (script.src && script.src.includes('frontend-menu.js')) {
                return script.src.replace('/js/frontend-menu.js', '/');
            }
        }
        return '/bbgl/';
    }

    /**
     * 初始化菜单系统
     */
    async init() {
        try {
            await this.loadMenuData();
            await this.loadStaticPages();
            this.renderMenu();
            this.bindEvents();
            this.detectCurrentPage();
        } catch (error) {
            console.error('菜单初始化失败:', error);
            this.showError('菜单加载失败');
        }
    }

    /**
     * 加载菜单数据
     */
    async loadMenuData() {
        const response = await fetch(this.baseUrl + 'index.php?r=frontend/getMenuData');
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || '菜单数据加载失败');
        }
        
        this.menuData = result.data;
    }

    /**
     * 加载静态页面列表
     */
    async loadStaticPages() {
        const response = await fetch(this.baseUrl + 'index.php?r=frontend/getStaticPages');
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || '静态页面数据加载失败');
        }
        
        this.staticPages = result.data;
    }

    /**
     * 渲染菜单
     */
    renderMenu() {
        const container = document.getElementById('menuContainer');
        if (!container) return;

        let html = '';

        // 首页链接
        html += this.renderMenuItem({
            icon: 'fa-home',
            text: '首页',
            url: this.baseUrl,
            active: this.isCurrentPage('/')
        });

        // 录入菜单
        if (this.menuData.entry && this.menuData.entry.length > 0) {
            html += '<div class="menu-group">数据录入</div>';
            this.menuData.entry.forEach(group => {
                html += this.renderMenuGroup(group, 'entry');
            });
        }

        // 查询菜单（只显示有静态页面的报表）
        if (this.menuData.query && this.menuData.query.length > 0) {
            html += '<div class="menu-group">数据查询</div>';
            this.menuData.query.forEach(group => {
                html += this.renderMenuGroup(group, 'query');
            });
        }

        // 系统管理功能已移至右上角按钮，不再在左侧菜单显示

        container.innerHTML = html;
    }

    /**
     * 渲染菜单组 - 新结构：分组 → 报表 → 设备
     */
    renderMenuGroup(group, type) {
        let html = '';
        const groupId = `${type}-${group.group}`;

        // 分组标题（如：现场资料录入）
        html += `<div class="menu-item collapsible" data-group="${groupId}" onclick="frontendMenu.toggleGroup('${groupId}')">`;
        html += `<i class="fa fa-${type === 'entry' ? 'edit' : 'search'} me-2"></i>`;
        html += `<span>${group.name}</span>`;
        html += `<i class="fa fa-chevron-down float-end"></i>`;
        html += `</div>`;

        html += `<div class="menu-submenu" id="submenu-${groupId}" style="display: none;">`;

        // 遍历报表
        group.reports.forEach(report => {
            if (report.devices && report.devices.length > 0) {
                // 有设备的报表，显示报表名称作为子分组
                const reportId = `${groupId}-report-${report.id}`;

                html += `<div class="menu-item collapsible" data-group="${reportId}" onclick="frontendMenu.toggleGroup('${reportId}')" style="padding-left: 20px;">`;
                html += `<i class="fa fa-folder me-2"></i>`;
                html += `<span>${report.name}</span>`;
                html += `<i class="fa fa-chevron-down float-end"></i>`;
                html += `</div>`;

                html += `<div class="menu-submenu" id="submenu-${reportId}" style="display: none;">`;

                // 显示设备列表
                report.devices.forEach(device => {
                    const routeParam = type === 'entry'
                        ? `entry/report&id=${report.id}&device_id=${device.id}`
                        : `query/generic&report_id=${report.id}&device_id=${device.id}`;

                    // 查找对应类型的静态页面
                    const staticPage = this.findStaticPage(type, report.id, device.id);

                    let url;
                    if (staticPage) {
                        // 有静态页面，使用embed模式（不需要登录验证）
                        url = this.baseUrl + 'index.php?r=static/embed&file=' + encodeURIComponent(staticPage.filename);
                    } else {
                        // 没有静态页面，链接到动态页面
                        url = this.baseUrl + 'index.php?r=' + routeParam;
                    }

                    html += this.renderMenuItem({
                        text: device.name,
                        url: url,
                        isStatic: !!staticPage,
                        active: this.isCurrentPage(routeParam),
                        level: 3
                    });
                });

                html += '</div>';
            } else {
                // 无设备的报表，直接显示报表
                const routeParam = type === 'entry'
                    ? `entry/report&id=${report.id}`
                    : `query/generic&report_id=${report.id}`;

                // 查找对应类型的静态页面
                const staticPage = this.findStaticPage(type, report.id);

                let url;
                if (staticPage) {
                    // 有静态页面，使用embed模式（不需要登录验证）
                    url = this.baseUrl + 'index.php?r=static/embed&file=' + encodeURIComponent(staticPage.filename);
                } else {
                    // 没有静态页面，链接到动态页面
                    url = this.baseUrl + 'index.php?r=' + routeParam;
                }

                html += this.renderMenuItem({
                    text: report.name,
                    url: url,
                    isStatic: !!staticPage,
                    active: this.isCurrentPage(routeParam),
                    level: 2
                });
            }
        });

        html += '</div>';
        return html;
    }

    /**
     * 渲染静态页面菜单
     */
    renderStaticPagesMenu() {
        let html = '';
        
        // 按类型分组
        const entryPages = this.staticPages.filter(p => p.type === 'entry');
        const queryPages = this.staticPages.filter(p => p.type === 'query');
        
        if (entryPages.length > 0) {
            html += `<div class="menu-item collapsible" data-group="static-entry" onclick="frontendMenu.toggleGroup('static-entry')">`;
            html += `<i class="fa fa-file-code me-2"></i>`;
            html += `<span>静态录入页面</span>`;
            html += `<i class="fa fa-chevron-down float-end"></i>`;
            html += `</div>`;
            
            html += `<div class="menu-submenu" id="submenu-static-entry" style="display: none;">`;
            entryPages.forEach(page => {
                html += this.renderMenuItem({
                    text: page.title,
                    url: this.baseUrl + 'index.php?r=static/show&file=' + encodeURIComponent(page.filename),
                    isStatic: true,
                    level: 2
                });
            });
            html += '</div>';
        }
        
        if (queryPages.length > 0) {
            html += `<div class="menu-item collapsible" data-group="static-query" onclick="frontendMenu.toggleGroup('static-query')">`;
            html += `<i class="fa fa-file-code me-2"></i>`;
            html += `<span>静态查询页面</span>`;
            html += `<i class="fa fa-chevron-down float-end"></i>`;
            html += `</div>`;
            
            html += `<div class="menu-submenu" id="submenu-static-query" style="display: none;">`;
            queryPages.forEach(page => {
                html += this.renderMenuItem({
                    text: page.title,
                    url: this.baseUrl + 'index.php?r=static/show&file=' + encodeURIComponent(page.filename),
                    isStatic: true,
                    level: 2
                });
            });
            html += '</div>';
        }
        
        return html;
    }

    /**
     * 渲染单个菜单项
     */
    renderMenuItem(options) {
        const {
            icon = '',
            text,
            url,
            isStatic = false,
            active = false,
            target = '',
            level = 1
        } = options;

        const iconHtml = icon ? `<i class="fa ${icon} me-2"></i>` : '';
        const activeClass = active ? ' active' : '';
        const levelClass = level > 1 ? ` depth-${level}` : '';

        // 如果是静态页面，使用JavaScript加载到iframe中
        if (isStatic) {
            return `<a href="javascript:void(0)" class="menu-item${activeClass}${levelClass}" onclick="frontendMenu.loadInIframe('${url}', '${text}')">
                        ${iconHtml}<span>${text}</span>
                    </a>`;
        } else {
            // 动态页面使用普通链接
            const targetAttr = target ? ` target="${target}"` : '';
            return `<a href="${url}" class="menu-item${activeClass}${levelClass}"${targetAttr}>
                        ${iconHtml}<span>${text}</span>
                    </a>`;
        }
    }

    /**
     * 查找对应的静态页面
     */
    findStaticPage(type, reportId, deviceId = null) {
        const pattern = deviceId 
            ? `${type}_report_${reportId}_device_${deviceId}.html`
            : `${type}_report_${reportId}.html`;
        
        return this.staticPages.find(page => page.filename === pattern);
    }

    /**
     * 检查是否为当前页面
     */
    isCurrentPage(route) {
        const currentUrl = window.location.href;
        return currentUrl.includes(route);
    }

    /**
     * 切换菜单组显示/隐藏
     */
    toggleGroup(groupId) {
        const submenu = document.getElementById(`submenu-${groupId}`);
        const toggle = document.querySelector(`[data-group="${groupId}"] .fa-chevron-down, [data-group="${groupId}"] .fa-chevron-up`);
        
        if (submenu) {
            const isVisible = submenu.style.display !== 'none';
            submenu.style.display = isVisible ? 'none' : 'block';
            
            if (toggle) {
                toggle.className = toggle.className.replace(
                    isVisible ? 'fa-chevron-up' : 'fa-chevron-down',
                    isVisible ? 'fa-chevron-down' : 'fa-chevron-up'
                );
            }
            
            // 保存状态到localStorage
            this.saveGroupState(groupId, !isVisible);
        }
    }

    /**
     * 保存菜单组状态
     */
    saveGroupState(groupId, isOpen) {
        try {
            const menuState = JSON.parse(localStorage.getItem('frontendMenuState') || '{}');
            menuState[groupId] = isOpen;
            localStorage.setItem('frontendMenuState', JSON.stringify(menuState));
        } catch (e) {
            console.warn('无法保存菜单状态:', e);
        }
    }

    /**
     * 恢复菜单组状态
     */
    restoreGroupStates() {
        try {
            const menuState = JSON.parse(localStorage.getItem('frontendMenuState') || '{}');
            Object.keys(menuState).forEach(groupId => {
                if (menuState[groupId]) {
                    this.toggleGroup(groupId);
                }
            });
        } catch (e) {
            console.warn('无法恢复菜单状态:', e);
        }
    }

    /**
     * 检测当前页面
     */
    detectCurrentPage() {
        // 根据当前URL高亮对应菜单项
        const currentUrl = window.location.href;
        const menuItems = document.querySelectorAll('.menu-item');
        
        menuItems.forEach(item => {
            if (item.href && currentUrl.includes(item.href)) {
                item.classList.add('active');
            }
        });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 恢复菜单状态
        setTimeout(() => {
            this.restoreGroupStates();
        }, 100);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const container = document.getElementById('menuContainer');
        if (container) {
            container.innerHTML = `<div class="p-3 text-center text-danger">${message}</div>`;
        }
    }

    /**
     * 刷新菜单
     */
    async refresh() {
        await this.init();
    }

    /**
     * 在iframe中加载内容
     */
    loadInIframe(url, title) {
        // 检查是否在iframe中
        if (window.parent !== window) {
            // 在iframe中，通过postMessage通知父页面加载内容
            window.parent.postMessage({
                type: 'load_content',
                url: url,
                title: title
            }, '*');
        } else {
            // 在主页面中，直接调用函数
            if (typeof window.loadContentInArea === 'function') {
                window.loadContentInArea(url, title);
            } else {
                // 如果没有，直接跳转
                window.location.href = url;
            }
        }
    }
}

// 创建全局实例
const frontendMenu = new FrontendMenuManager();
