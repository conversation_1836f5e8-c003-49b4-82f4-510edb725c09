/**
 * 低内存模式优化器
 * 专为老旧计算机设计的内存优化方案
 */

(function() {
    'use strict';

    // 低内存模式配置
    const LOW_MEMORY_CONFIG = {
        MAX_MEMORY_MB: 100, // 最大内存使用限制 (MB)
        AGGRESSIVE_CLEANUP: true, // 激进清理模式
        DISABLE_ANIMATIONS: true, // 禁用动画
        REDUCE_DOM_COMPLEXITY: true, // 减少DOM复杂度
        VIRTUAL_SCROLLING: true, // 启用虚拟滚动
        LAZY_LOADING: true, // 延迟加载
        CHECK_INTERVAL: 60000 // 1分钟检查一次
    };

    // 内存使用监控
    let memoryMonitor = {
        isLowMemoryMode: false,
        lastCleanup: 0,
        cleanupCount: 0
    };

    /**
     * 检测是否需要启用低内存模式
     */
    function detectLowMemoryDevice() {
        // 检测设备内存
        if (navigator.deviceMemory && navigator.deviceMemory <= 2) {
            return true;
        }

        // 检测当前内存使用
        if (performance.memory) {
            const totalMemory = performance.memory.jsHeapSizeLimit / 1048576; // MB
            if (totalMemory < 200) {
                return true;
            }
        }

        // 检测用户代理（老旧浏览器）
        const userAgent = navigator.userAgent.toLowerCase();
        if (userAgent.includes('chrome/') && parseInt(userAgent.match(/chrome\/(\d+)/)[1]) < 80) {
            return true;
        }

        return false;
    }

    /**
     * 启用低内存模式
     */
    function enableLowMemoryMode() {
        if (memoryMonitor.isLowMemoryMode) return;

        console.log('启用低内存模式');
        memoryMonitor.isLowMemoryMode = true;

        // 禁用动画
        if (LOW_MEMORY_CONFIG.DISABLE_ANIMATIONS) {
            disableAnimations();
        }

        // 减少DOM复杂度
        if (LOW_MEMORY_CONFIG.REDUCE_DOM_COMPLEXITY) {
            reduceDOMComplexity();
        }

        // 启用虚拟滚动
        if (LOW_MEMORY_CONFIG.VIRTUAL_SCROLLING) {
            enableVirtualScrolling();
        }

        // 启用延迟加载
        if (LOW_MEMORY_CONFIG.LAZY_LOADING) {
            enableLazyLoading();
        }

        // 更频繁的内存检查
        setInterval(aggressiveMemoryCleanup, LOW_MEMORY_CONFIG.CHECK_INTERVAL);
    }

    /**
     * 禁用动画效果
     */
    function disableAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-delay: -0.01ms !important;
                transition-duration: 0.01ms !important;
                transition-delay: -0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 减少DOM复杂度
     */
    function reduceDOMComplexity() {
        // 移除不必要的装饰元素
        const decorativeElements = document.querySelectorAll('.decoration, .shadow, .gradient');
        decorativeElements.forEach(el => el.remove());

        // 简化表格结构
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            // 移除表格的复杂样式
            table.style.borderCollapse = 'collapse';
            table.style.tableLayout = 'fixed';
        });
    }

    /**
     * 启用虚拟滚动
     */
    function enableVirtualScrolling() {
        const tables = document.querySelectorAll('table tbody');
        tables.forEach(tbody => {
            if (tbody.children.length > 20) {
                implementVirtualScrolling(tbody);
            }
        });
    }

    /**
     * 实现虚拟滚动
     */
    function implementVirtualScrolling(tbody) {
        const rows = Array.from(tbody.children);
        const visibleRows = 15; // 只显示15行
        let startIndex = 0;

        function renderVisibleRows() {
            // 清空当前显示
            tbody.innerHTML = '';
            
            // 只渲染可见行
            const endIndex = Math.min(startIndex + visibleRows, rows.length);
            for (let i = startIndex; i < endIndex; i++) {
                tbody.appendChild(rows[i]);
            }
        }

        // 添加滚动控制
        const scrollContainer = tbody.closest('.table-responsive') || tbody.parentElement;
        if (scrollContainer) {
            scrollContainer.addEventListener('scroll', function() {
                const scrollTop = this.scrollTop;
                const rowHeight = 40; // 假设每行40px
                const newStartIndex = Math.floor(scrollTop / rowHeight);
                
                if (newStartIndex !== startIndex) {
                    startIndex = newStartIndex;
                    renderVisibleRows();
                }
            });
        }

        renderVisibleRows();
    }

    /**
     * 启用延迟加载
     */
    function enableLazyLoading() {
        // 延迟加载图片
        const images = document.querySelectorAll('img[src]');
        images.forEach(img => {
            const src = img.src;
            img.removeAttribute('src');
            img.dataset.src = src;
            
            // 使用Intersection Observer
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            observer.unobserve(img);
                        }
                    });
                });
                observer.observe(img);
            }
        });
    }

    /**
     * 激进内存清理
     */
    function aggressiveMemoryCleanup() {
        const now = Date.now();
        if (now - memoryMonitor.lastCleanup < 30000) return; // 30秒内不重复清理

        console.log('执行激进内存清理');
        
        // 清理所有可能的内存泄漏
        cleanupStringIterators();
        cleanupAsyncFunctions();
        cleanupDetachedElements();
        cleanupWebAssemblyObjects();
        
        // 强制垃圾回收
        if (window.gc) {
            window.gc();
        }

        memoryMonitor.lastCleanup = now;
        memoryMonitor.cleanupCount++;
    }

    /**
     * 清理字符串迭代器
     */
    function cleanupStringIterators() {
        // 清理可能的字符串迭代器泄漏
        if (window.stringIterators) {
            window.stringIterators = null;
        }
    }

    /**
     * 清理异步函数
     */
    function cleanupAsyncFunctions() {
        // 取消未完成的异步操作
        if (window.pendingPromises) {
            window.pendingPromises.forEach(promise => {
                if (promise && typeof promise.cancel === 'function') {
                    promise.cancel();
                }
            });
            window.pendingPromises = [];
        }
    }

    /**
     * 清理分离的DOM元素
     */
    function cleanupDetachedElements() {
        // 清理分离的元素
        const detachedElements = document.querySelectorAll('[data-detached="true"]');
        detachedElements.forEach(el => {
            if (el.parentNode) {
                el.parentNode.removeChild(el);
            }
        });
    }

    /**
     * 清理WebAssembly对象
     */
    function cleanupWebAssemblyObjects() {
        // 清理WebAssembly相关对象
        if (window.wasmInstances) {
            window.wasmInstances.forEach(instance => {
                if (instance && typeof instance.exports === 'object') {
                    // 清理WebAssembly实例
                    Object.keys(instance.exports).forEach(key => {
                        instance.exports[key] = null;
                    });
                }
            });
            window.wasmInstances = [];
        }
    }

    /**
     * 获取内存使用统计
     */
    function getMemoryStats() {
        const memory = performance.memory;
        if (!memory) return null;

        return {
            used: Math.round(memory.usedJSHeapSize / 1048576),
            total: Math.round(memory.totalJSHeapSize / 1048576),
            limit: Math.round(memory.jsHeapSizeLimit / 1048576),
            isLowMemoryMode: memoryMonitor.isLowMemoryMode,
            cleanupCount: memoryMonitor.cleanupCount
        };
    }

    /**
     * 初始化低内存模式
     */
    function initLowMemoryMode() {
        console.log('低内存模式检测器已启动');

        // 检测是否需要启用低内存模式
        if (detectLowMemoryDevice()) {
            enableLowMemoryMode();
        }

        // 监听内存压力事件
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = memory.usedJSHeapSize / 1048576;
                
                if (usedMB > LOW_MEMORY_CONFIG.MAX_MEMORY_MB && !memoryMonitor.isLowMemoryMode) {
                    enableLowMemoryMode();
                }
            }, 30000); // 30秒检查一次
        }

        // 暴露全局接口
        window.LowMemoryMode = {
            enable: enableLowMemoryMode,
            cleanup: aggressiveMemoryCleanup,
            stats: getMemoryStats,
            config: LOW_MEMORY_CONFIG
        };
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLowMemoryMode);
    } else {
        initLowMemoryMode();
    }

})();
