/**
 * 内存监控面板
 * 提供实时内存使用情况显示和控制
 */

(function() {
    'use strict';

    let monitorPanel = null;
    let isMonitoring = false;
    let monitorInterval = null;

    /**
     * 创建监控面板
     */
    function createMonitorPanel() {
        if (monitorPanel) return monitorPanel;

        const panel = document.createElement('div');
        panel.id = 'memory-monitor-panel';
        panel.innerHTML = `
            <div class="memory-monitor-header">
                <h6>内存监控</h6>
                <button class="btn-close" onclick="MemoryMonitor.hide()">×</button>
            </div>
            <div class="memory-monitor-content">
                <div class="memory-stats">
                    <div class="stat-item">
                        <label>已用内存:</label>
                        <span id="used-memory">-</span>
                    </div>
                    <div class="stat-item">
                        <label>总内存:</label>
                        <span id="total-memory">-</span>
                    </div>
                    <div class="stat-item">
                        <label>内存限制:</label>
                        <span id="memory-limit">-</span>
                    </div>
                    <div class="stat-item">
                        <label>清理次数:</label>
                        <span id="cleanup-count">-</span>
                    </div>
                </div>
                <div class="memory-progress">
                    <div class="progress-bar" id="memory-progress-bar"></div>
                </div>
                <div class="memory-controls">
                    <button class="btn btn-sm btn-primary" onclick="MemoryMonitor.cleanup()">清理内存</button>
                    <button class="btn btn-sm btn-warning" onclick="MemoryMonitor.deepCleanup()">深度清理</button>
                    <button class="btn btn-sm btn-info" onclick="MemoryMonitor.toggleLowMemoryMode()">低内存模式</button>
                </div>
                <div class="memory-info">
                    <div class="info-item">
                        <label>表格优化:</label>
                        <span id="table-stats">-</span>
                    </div>
                    <div class="info-item">
                        <label>低内存模式:</label>
                        <span id="low-memory-status">-</span>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #memory-monitor-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 12px;
                display: none;
            }
            .memory-monitor-header {
                padding: 10px 15px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #f8f9fa;
                border-radius: 8px 8px 0 0;
            }
            .memory-monitor-header h6 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
            }
            .btn-close {
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .memory-monitor-content {
                padding: 15px;
            }
            .memory-stats {
                margin-bottom: 15px;
            }
            .stat-item, .info-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
            }
            .stat-item label, .info-item label {
                font-weight: 500;
                color: #666;
            }
            .memory-progress {
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
                margin-bottom: 15px;
                overflow: hidden;
            }
            .progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
                transition: width 0.3s ease;
                width: 0%;
            }
            .memory-controls {
                display: flex;
                gap: 5px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }
            .memory-controls .btn {
                flex: 1;
                min-width: 80px;
                padding: 4px 8px;
                font-size: 11px;
            }
            .memory-info {
                border-top: 1px solid #eee;
                padding-top: 10px;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(panel);
        monitorPanel = panel;
        return panel;
    }

    /**
     * 更新监控数据
     */
    function updateMonitorData() {
        if (!monitorPanel || monitorPanel.style.display === 'none') return;

        // 获取内存统计
        const memoryStats = window.MemoryOptimizer?.stats();
        const lowMemoryStats = window.LowMemoryMode?.stats();
        const tableStats = window.TableOptimizer?.stats();

        if (memoryStats && memoryStats.current) {
            const { used, total, limit } = memoryStats.current;
            const { cleanupCount } = memoryStats.stats;

            // 更新内存数据
            document.getElementById('used-memory').textContent = `${used}MB`;
            document.getElementById('total-memory').textContent = `${total}MB`;
            document.getElementById('memory-limit').textContent = `${limit}MB`;
            document.getElementById('cleanup-count').textContent = cleanupCount;

            // 更新进度条
            const percentage = (used / limit) * 100;
            const progressBar = document.getElementById('memory-progress-bar');
            progressBar.style.width = `${Math.min(percentage, 100)}%`;

            // 根据使用率改变颜色
            if (percentage > 80) {
                progressBar.style.background = '#dc3545';
            } else if (percentage > 60) {
                progressBar.style.background = '#ffc107';
            } else {
                progressBar.style.background = '#28a745';
            }
        }

        // 更新表格统计
        if (tableStats) {
            document.getElementById('table-stats').textContent = 
                `${tableStats.optimizedTables}/${tableStats.totalTables} (${tableStats.optimizationRate})`;
        }

        // 更新低内存模式状态
        if (lowMemoryStats) {
            const status = lowMemoryStats.isLowMemoryMode ? '已启用' : '未启用';
            document.getElementById('low-memory-status').textContent = status;
        }
    }

    /**
     * 显示监控面板
     */
    function showMonitor() {
        createMonitorPanel();
        monitorPanel.style.display = 'block';
        
        if (!isMonitoring) {
            isMonitoring = true;
            monitorInterval = setInterval(updateMonitorData, 2000); // 每2秒更新
        }
        
        updateMonitorData();
    }

    /**
     * 隐藏监控面板
     */
    function hideMonitor() {
        if (monitorPanel) {
            monitorPanel.style.display = 'none';
        }
        
        if (isMonitoring) {
            isMonitoring = false;
            if (monitorInterval) {
                clearInterval(monitorInterval);
                monitorInterval = null;
            }
        }
    }

    /**
     * 切换监控面板显示
     */
    function toggleMonitor() {
        if (monitorPanel && monitorPanel.style.display !== 'none') {
            hideMonitor();
        } else {
            showMonitor();
        }
    }

    /**
     * 执行内存清理
     */
    function performCleanup() {
        if (window.MemoryOptimizer) {
            window.MemoryOptimizer.cleanup('normal');
        }
        updateMonitorData();
    }

    /**
     * 执行深度清理
     */
    function performDeepCleanup() {
        if (window.MemoryOptimizer) {
            window.MemoryOptimizer.cleanup('deep');
        }
        if (window.LowMemoryMode) {
            window.LowMemoryMode.cleanup();
        }
        updateMonitorData();
    }

    /**
     * 切换低内存模式
     */
    function toggleLowMemoryMode() {
        if (window.LowMemoryMode) {
            const stats = window.LowMemoryMode.stats();
            if (stats && stats.isLowMemoryMode) {
                // 当前已启用，这里可以添加禁用逻辑
                console.log('低内存模式已启用，无法禁用');
            } else {
                window.LowMemoryMode.enable();
            }
        }
        updateMonitorData();
    }

    /**
     * 添加快捷键支持
     */
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+M: 切换监控面板
            if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                e.preventDefault();
                toggleMonitor();
            }
            
            // Ctrl+Shift+C: 清理内存
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                performCleanup();
            }
        });
    }

    /**
     * 初始化内存监控器
     */
    function initMemoryMonitor() {
        console.log('内存监控器已启动');
        
        // 添加快捷键支持
        addKeyboardShortcuts();

        // 暴露全局接口
        window.MemoryMonitor = {
            show: showMonitor,
            hide: hideMonitor,
            toggle: toggleMonitor,
            cleanup: performCleanup,
            deepCleanup: performDeepCleanup,
            toggleLowMemoryMode: toggleLowMemoryMode,
            update: updateMonitorData
        };

        // 在开发环境下自动显示
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            setTimeout(showMonitor, 1000);
        }
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMemoryMonitor);
    } else {
        initMemoryMonitor();
    }

})();
