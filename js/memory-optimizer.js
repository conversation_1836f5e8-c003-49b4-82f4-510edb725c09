/**
 * 内存优化工具
 * 用于监控和优化页面内存使用
 */

(function() {
    'use strict';

    // 内存监控配置 - 针对老旧计算机优化
    const MEMORY_CONFIG = {
        WARNING_THRESHOLD: 80, // 警告阈值 (MB) - 降低到80MB
        CRITICAL_THRESHOLD: 120, // 严重阈值 (MB) - 降低到120MB
        CHECK_INTERVAL: 120000, // 检查间隔 (2分钟) - 更频繁检查
        MAX_TABLE_ROWS: 30, // 表格最大行数 - 减少到30行
        MAX_CACHE_SIZE: 5, // 缓存最大条目数 - 减少到5个
        MAX_DOM_NODES: 500, // 最大DOM节点数
        CLEANUP_DELAY: 2000, // 清理延迟 (ms)
        LOW_MEMORY_MODE: true // 低内存模式
    };

    // 内存使用统计
    let memoryStats = {
        lastCheck: 0,
        maxUsage: 0,
        cleanupCount: 0
    };

    /**
     * 获取当前内存使用情况
     */
    function getMemoryUsage() {
        if (!performance.memory) {
            return null;
        }

        return {
            used: Math.round(performance.memory.usedJSHeapSize / 1048576),
            total: Math.round(performance.memory.totalJSHeapSize / 1048576),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
        };
    }

    /**
     * 清理DOM中的大表格 - 增强版
     */
    function cleanupLargeTables() {
        const tables = document.querySelectorAll('table tbody');
        let cleanedRows = 0;

        tables.forEach(tbody => {
            if (tbody.children.length > MEMORY_CONFIG.MAX_TABLE_ROWS) {
                const excessRows = tbody.children.length - MEMORY_CONFIG.MAX_TABLE_ROWS;

                // 从末尾删除多余的行
                for (let i = 0; i < excessRows; i++) {
                    if (tbody.lastChild) {
                        tbody.removeChild(tbody.lastChild);
                        cleanedRows++;
                    }
                }
            }
        });

        // 清理空的表格容器
        const emptyTables = document.querySelectorAll('table:empty, tbody:empty');
        emptyTables.forEach(table => {
            if (table.parentNode) {
                table.parentNode.removeChild(table);
            }
        });

        if (cleanedRows > 0) {
            console.log(`清理了 ${cleanedRows} 个表格行`);
        }

        return cleanedRows;
    }

    /**
     * 清理分离的DOM节点
     */
    function cleanupDetachedNodes() {
        let cleanedNodes = 0;

        // 清理分离的脚本标签
        const detachedScripts = document.querySelectorAll('script[src*="detached"], script:empty');
        detachedScripts.forEach(script => {
            if (script.parentNode && !script.src && !script.textContent.trim()) {
                script.parentNode.removeChild(script);
                cleanedNodes++;
            }
        });

        // 清理分离的样式标签
        const detachedStyles = document.querySelectorAll('style:empty');
        detachedStyles.forEach(style => {
            if (style.parentNode) {
                style.parentNode.removeChild(style);
                cleanedNodes++;
            }
        });

        // 清理无用的SVG元素
        const unusedSvgs = document.querySelectorAll('svg:empty, svg[style*="display: none"]');
        unusedSvgs.forEach(svg => {
            if (svg.parentNode) {
                svg.parentNode.removeChild(svg);
                cleanedNodes++;
            }
        });

        return cleanedNodes;
    }

    /**
     * 清理全局缓存
     */
    function cleanupCaches() {
        let cleanedCaches = 0;

        // 清理设备列表缓存
        if (window.deviceListCache && window.deviceListCache.clear) {
            const size = window.deviceListCache.size || 0;
            window.deviceListCache.clear();
            cleanedCaches += size;
        }

        // 清理其他可能的缓存
        ['tableData', 'dutyStaffData', 'entryData'].forEach(cacheName => {
            if (window[cacheName]) {
                if (Array.isArray(window[cacheName])) {
                    window[cacheName].length = 0;
                } else if (typeof window[cacheName] === 'object') {
                    Object.keys(window[cacheName]).forEach(key => {
                        delete window[cacheName][key];
                    });
                }
                cleanedCaches++;
            }
        });

        if (cleanedCaches > 0) {
            console.log(`清理了 ${cleanedCaches} 个缓存对象`);
        }

        return cleanedCaches;
    }

    /**
     * 清理事件监听器
     */
    function cleanupEventListeners() {
        // 移除可能泄漏的事件监听器
        const elements = document.querySelectorAll('[data-cleanup-events]');
        elements.forEach(element => {
            const events = element.dataset.cleanupEvents.split(',');
            events.forEach(event => {
                element.removeEventListener(event.trim(), null);
            });
        });
    }

    /**
     * 强制垃圾回收
     */
    function forceGarbageCollection() {
        if (window.gc) {
            try {
                window.gc();
                console.log('执行了强制垃圾回收');
                return true;
            } catch (e) {
                console.warn('强制垃圾回收失败:', e);
            }
        }
        return false;
    }

    /**
     * 执行内存清理 - 增强版
     */
    function performMemoryCleanup(level = 'normal') {
        console.log(`开始执行 ${level} 级别的内存清理`);

        const startTime = performance.now();
        let cleanupActions = [];

        // 基础清理
        const tablesCleared = cleanupLargeTables();
        const cachesCleared = cleanupCaches();
        const detachedCleared = cleanupDetachedNodes();

        cleanupActions.push(`表格行: ${tablesCleared}`);
        cleanupActions.push(`缓存: ${cachesCleared}`);
        cleanupActions.push(`分离节点: ${detachedCleared}`);

        // 清理字符串迭代器和异步函数
        if (level === 'deep' || MEMORY_CONFIG.LOW_MEMORY_MODE) {
            // 清理全局变量
            cleanupGlobalVariables();
            cleanupActions.push('全局变量');

            // 清理事件监听器
            cleanupEventListeners();
            cleanupActions.push('事件监听器');

            // 清理未使用的CSS样式
            const styles = document.querySelectorAll('style');
            styles.forEach(style => {
                if (style.textContent.length > 5000) { // 降低阈值
                    style.remove();
                }
            });
            cleanupActions.push('大型样式表');

            // 清理WebAssembly对象
            cleanupWebAssemblyObjects();
            cleanupActions.push('WebAssembly对象');
        }

        // 强制垃圾回收
        const gcSuccess = forceGarbageCollection();
        if (gcSuccess) {
            cleanupActions.push('垃圾回收');
        }

        const endTime = performance.now();
        memoryStats.cleanupCount++;

        console.log(`内存清理完成 (${Math.round(endTime - startTime)}ms):`, cleanupActions.join(', '));
    }

    /**
     * 清理全局变量
     */
    function cleanupGlobalVariables() {
        // 清理可能的内存泄漏变量
        const globalVarsToClean = ['tableData', 'deviceListCache', 'entryData', 'templateFields'];

        globalVarsToClean.forEach(varName => {
            if (window[varName]) {
                if (typeof window[varName] === 'object' && window[varName].clear) {
                    window[varName].clear();
                } else if (Array.isArray(window[varName])) {
                    window[varName].length = 0;
                } else {
                    window[varName] = null;
                }
            }
        });
    }

    /**
     * 清理WebAssembly对象
     */
    function cleanupWebAssemblyObjects() {
        // 清理可能的WebAssembly实例
        if (window.WebAssembly) {
            // 这里可以添加特定的WebAssembly清理逻辑
            console.log('WebAssembly对象清理完成');
        }
    }

    /**
     * 检查内存使用情况
     */
    function checkMemoryUsage() {
        const memory = getMemoryUsage();
        if (!memory) return;

        memoryStats.lastCheck = Date.now();
        memoryStats.maxUsage = Math.max(memoryStats.maxUsage, memory.used);

        console.log(`内存使用: ${memory.used}MB / ${memory.total}MB (限制: ${memory.limit}MB)`);

        // 根据内存使用情况执行不同级别的清理
        if (memory.used > MEMORY_CONFIG.CRITICAL_THRESHOLD) {
            console.warn('内存使用达到严重阈值，执行深度清理');
            performMemoryCleanup('deep');
        } else if (memory.used > MEMORY_CONFIG.WARNING_THRESHOLD) {
            console.warn('内存使用达到警告阈值，执行常规清理');
            performMemoryCleanup('normal');
        }
    }

    /**
     * 获取内存统计信息
     */
    function getMemoryStats() {
        const memory = getMemoryUsage();
        return {
            current: memory,
            stats: memoryStats,
            config: MEMORY_CONFIG
        };
    }

    /**
     * 初始化内存优化器
     */
    function initMemoryOptimizer() {
        console.log('内存优化器已启动');
        
        // 定期检查内存
        setInterval(checkMemoryUsage, MEMORY_CONFIG.CHECK_INTERVAL);
        
        // 页面隐藏时清理内存
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                setTimeout(() => performMemoryCleanup('normal'), 1000);
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            performMemoryCleanup('deep');
        });

        // 暴露全局接口
        window.MemoryOptimizer = {
            check: checkMemoryUsage,
            cleanup: performMemoryCleanup,
            stats: getMemoryStats,
            config: MEMORY_CONFIG
        };
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMemoryOptimizer);
    } else {
        initMemoryOptimizer();
    }

})();
