/**
 * 性能优化工具 - 从根本上优化代码性能
 * 专注于减少内存使用而不是增加清理代码
 */

(function() {
    'use strict';

    // 优化DOM操作的工具函数
    const DOMUtils = {
        // 批量DOM操作，减少重排重绘
        batchUpdate: function(element, updates) {
            const originalDisplay = element.style.display;
            element.style.display = 'none';
            updates();
            element.style.display = originalDisplay;
        },

        // 使用文档片段减少DOM操作
        createFragment: function(htmlString) {
            const template = document.createElement('template');
            template.innerHTML = htmlString;
            return template.content;
        },

        // 高效的元素查找，避免重复查询
        cache: new Map(),
        getElementById: function(id) {
            if (!this.cache.has(id)) {
                this.cache.set(id, document.getElementById(id));
            }
            return this.cache.get(id);
        }
    };

    // 表格渲染优化
    const TableRenderer = {
        // 分页渲染，避免一次性渲染大量数据
        renderWithPagination: function(data, container, pageSize = 20) {
            if (!data || !data.length) return;
            
            const totalPages = Math.ceil(data.length / pageSize);
            let currentPage = 1;
            
            const renderPage = (page) => {
                const start = (page - 1) * pageSize;
                const end = start + pageSize;
                const pageData = data.slice(start, end);
                
                // 使用文档片段提高性能
                const fragment = document.createDocumentFragment();
                pageData.forEach(item => {
                    const row = this.createTableRow(item);
                    fragment.appendChild(row);
                });
                
                // 清空并添加新内容
                container.innerHTML = '';
                container.appendChild(fragment);
            };
            
            renderPage(currentPage);
            
            // 返回分页控制器
            return {
                nextPage: () => {
                    if (currentPage < totalPages) {
                        currentPage++;
                        renderPage(currentPage);
                    }
                },
                prevPage: () => {
                    if (currentPage > 1) {
                        currentPage--;
                        renderPage(currentPage);
                    }
                },
                getCurrentPage: () => currentPage,
                getTotalPages: () => totalPages
            };
        },

        // 创建表格行，避免innerHTML
        createTableRow: function(data) {
            const row = document.createElement('tr');
            Object.values(data).forEach(value => {
                const cell = document.createElement('td');
                cell.textContent = value || '';
                row.appendChild(cell);
            });
            return row;
        }
    };

    // 事件管理优化
    const EventManager = {
        listeners: new Map(),
        
        // 事件委托，减少事件监听器数量
        delegate: function(container, selector, event, handler) {
            const delegateHandler = (e) => {
                if (e.target.matches(selector)) {
                    handler(e);
                }
            };
            
            container.addEventListener(event, delegateHandler);
            
            // 记录监听器以便清理
            const key = `${container.id || 'unknown'}-${event}`;
            if (!this.listeners.has(key)) {
                this.listeners.set(key, []);
            }
            this.listeners.get(key).push(delegateHandler);
            
            return delegateHandler;
        },

        // 清理所有事件监听器
        cleanup: function() {
            this.listeners.clear();
        }
    };

    // 数据缓存优化
    const DataCache = {
        cache: new Map(),
        maxSize: 10, // 限制缓存大小
        
        set: function(key, value) {
            // 如果缓存已满，删除最旧的项
            if (this.cache.size >= this.maxSize) {
                const firstKey = this.cache.keys().next().value;
                this.cache.delete(firstKey);
            }
            this.cache.set(key, value);
        },
        
        get: function(key) {
            return this.cache.get(key);
        },
        
        clear: function() {
            this.cache.clear();
        }
    };

    // 防抖和节流优化
    const ThrottleUtils = {
        // 防抖函数
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 节流函数
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    };

    // 内存使用监控（轻量级）
    const MemoryMonitor = {
        check: function() {
            if (performance.memory) {
                const used = Math.round(performance.memory.usedJSHeapSize / 1048576);
                if (used > 100) { // 只在真正需要时提醒
                    console.warn(`内存使用: ${used}MB`);
                    return true;
                }
            }
            return false;
        },

        // 简单的清理建议
        suggest: function() {
            if (this.check()) {
                console.log('建议: 刷新页面或关闭不必要的标签页');
            }
        }
    };

    // 优化现有表格
    function optimizeExistingTables() {
        const tables = document.querySelectorAll('table tbody');
        tables.forEach(tbody => {
            if (tbody.children.length > 50) {
                console.log(`表格行数过多 (${tbody.children.length})，建议使用分页`);
                // 不强制删除，而是建议优化
            }
        });
    }

    // 页面卸载时的简单清理
    function cleanup() {
        EventManager.cleanup();
        DataCache.clear();
        DOMUtils.cache.clear();
    }

    // 初始化性能优化
    function init() {
        console.log('性能优化工具已启动');
        
        // 检查现有表格
        optimizeExistingTables();
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', cleanup);
        
        // 定期检查内存（不频繁）
        setInterval(() => {
            MemoryMonitor.suggest();
        }, 300000); // 5分钟检查一次
    }

    // 暴露优化工具
    window.PerformanceOptimizer = {
        DOMUtils,
        TableRenderer,
        EventManager,
        DataCache,
        ThrottleUtils,
        MemoryMonitor,
        init
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
