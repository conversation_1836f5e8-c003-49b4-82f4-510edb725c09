/**
 * 静态页面管理器
 * 用于管理静态页面的生成、预览和清理
 */

(function() {
    'use strict';

    // 全局函数：生成静态页面
    window.generateStatic = function(reportId, type = 'both') {
        if (!reportId) {
            showMessage('报表ID不能为空', 'error');
            return;
        }

        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        
        // 显示加载状态
        button.disabled = true;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

        const data = {
            report_id: reportId,
            type: type
        };

        fetch(window.__STATIC_API__.generate, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage(result.message, 'success');
                
                // 显示生成结果详情
                if (result.results) {
                    let details = [];
                    if (result.results.entry) {
                        details.push(`录入页面: ${result.results.entry.file} (${formatFileSize(result.results.entry.size)})`);
                    }
                    if (result.results.query) {
                        details.push(`查询页面: ${result.results.query.file} (${formatFileSize(result.results.query.size)})`);
                    }
                    
                    if (details.length > 0) {
                        showMessage('生成详情:\n' + details.join('\n'), 'info');
                    }
                }
                
                // 刷新页面状态
                setTimeout(refreshStaticList, 1000);
            } else {
                showMessage(result.message || '生成失败', 'error');
            }
        })
        .catch(error => {
            console.error('生成静态页面失败:', error);
            showMessage('网络错误，生成失败', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            button.disabled = false;
            button.innerHTML = originalText;
        });
    };

    // 全局函数：生成设备专用静态页面
    window.generateDeviceStatic = function(reportId) {
        if (!reportId) {
            showMessage('报表ID不能为空', 'error');
            return;
        }

        const button = event.target.closest('button');
        const originalText = button.innerHTML;

        // 显示加载状态
        button.disabled = true;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

        const data = {
            report_id: reportId
        };

        fetch(window.__STATIC_API__.generateDevices, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage(result.message, 'success');

                // 显示生成结果详情
                if (result.results && result.results.length > 0) {
                    let details = [];
                    result.results.forEach(item => {
                        details.push(`${item.type === 'entry' ? '录入' : '查询'} - ${item.device_name}: ${item.file} (${formatFileSize(item.size)})`);
                    });

                    if (details.length > 0) {
                        const maxShow = 5;
                        const showDetails = details.slice(0, maxShow);
                        if (details.length > maxShow) {
                            showDetails.push(`... 还有 ${details.length - maxShow} 个文件`);
                        }
                        showMessage('生成详情:\n' + showDetails.join('\n'), 'info');
                    }
                }

                // 刷新页面状态
                setTimeout(refreshStaticList, 1000);
            } else {
                showMessage(result.message || '生成失败', 'error');
            }
        })
        .catch(error => {
            console.error('生成设备专用页面失败:', error);
            showMessage('网络错误，生成失败', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    };

    // 全局函数：批量生成所有静态页面
    window.generateAllStatic = function() {
        if (!confirm('确定要批量生成所有报表的静态页面吗？这可能需要一些时间。')) {
            return;
        }

        const button = event.target;
        const originalText = button.textContent;
        
        // 显示加载状态
        button.disabled = true;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 生成中...';

        // 获取所有报表ID
        const reportIds = [];
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const reportId = row.cells[0].textContent.trim();
            if (reportId && !isNaN(reportId)) {
                reportIds.push(parseInt(reportId));
            }
        });

        if (reportIds.length === 0) {
            showMessage('没有找到可生成的报表', 'warning');
            button.disabled = false;
            button.textContent = originalText;
            return;
        }

        // 批量生成
        batchGenerateStatic(reportIds, 0, originalText, button);
    };

    // 批量生成静态页面（递归处理）
    function batchGenerateStatic(reportIds, index, originalText, button) {
        if (index >= reportIds.length) {
            // 全部完成
            button.disabled = false;
            button.textContent = originalText;
            showMessage(`批量生成完成，共处理 ${reportIds.length} 个报表`, 'success');
            setTimeout(refreshStaticList, 1000);
            return;
        }

        const reportId = reportIds[index];
        const progress = Math.round(((index + 1) / reportIds.length) * 100);
        
        // 更新按钮状态
        button.innerHTML = `<i class="fa fa-spinner fa-spin"></i> 生成中... (${index + 1}/${reportIds.length})`;

        const data = {
            report_id: reportId,
            type: 'both'
        };

        fetch(window.__STATIC_API__.generate, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                console.log(`报表 ${reportId} 生成成功`);
            } else {
                console.warn(`报表 ${reportId} 生成失败:`, result.message);
            }
        })
        .catch(error => {
            console.error(`报表 ${reportId} 生成错误:`, error);
        })
        .finally(() => {
            // 继续下一个
            setTimeout(() => {
                batchGenerateStatic(reportIds, index + 1, originalText, button);
            }, 500); // 间隔500ms避免服务器压力过大
        });
    }

    // 全局函数：刷新静态文件列表
    window.refreshStaticList = function() {
        const button = event?.target;
        if (button) {
            const originalText = button.textContent;
            button.disabled = true;
            button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        }

        fetch(window.__STATIC_API__.status)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 刷新页面以显示最新状态
                window.location.reload();
            } else {
                showMessage(result.message || '刷新失败', 'error');
            }
        })
        .catch(error => {
            console.error('刷新列表失败:', error);
            showMessage('网络错误，刷新失败', 'error');
        })
        .finally(() => {
            if (button) {
                button.disabled = false;
                button.textContent = '刷新列表';
            }
        });
    };

    // 全局函数：清理静态文件
    window.cleanupStaticFiles = function() {
        if (!confirm('确定要清理所有静态文件吗？此操作不可撤销。')) {
            return;
        }

        const button = event.target;
        const originalText = button.textContent;
        
        button.disabled = true;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

        fetch(window.__STATIC_API__.cleanup, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage(result.message, 'success');
                setTimeout(refreshStaticList, 1000);
            } else {
                showMessage(result.message || '清理失败', 'error');
            }
        })
        .catch(error => {
            console.error('清理文件失败:', error);
            showMessage('网络错误，清理失败', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = originalText;
        });
    };

    // 工具函数：格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // 工具函数：显示消息
    function showMessage(message, type = 'info') {
        // 创建消息容器
        let container = document.getElementById('messageContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'messageContainer';
            container.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }

        // 创建消息元素
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const messageEl = document.createElement('div');
        messageEl.className = `alert ${alertClass} alert-dismissible fade show`;
        messageEl.innerHTML = `
            <div style="white-space: pre-line;">${escapeHtml(message)}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(messageEl);

        // 自动隐藏（除了错误消息）
        if (type !== 'error') {
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 5000);
        }
    }

    // 工具函数：HTML转义
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('静态页面管理器已加载');
        
        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl+G: 批量生成
            if (e.ctrlKey && e.key === 'g') {
                e.preventDefault();
                generateAllStatic();
            }
            
            // Ctrl+R: 刷新列表
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                refreshStaticList();
            }
        });

        // 添加工具提示
        const tooltips = [
            { selector: '[onclick*="generateStatic"]', title: '生成静态页面' },
            { selector: '[onclick*="generateAllStatic"]', title: '批量生成所有报表的静态页面 (Ctrl+G)' },
            { selector: '[onclick*="refreshStaticList"]', title: '刷新静态文件列表 (Ctrl+R)' },
            { selector: '[onclick*="cleanupStaticFiles"]', title: '清理所有静态文件' }
        ];

        tooltips.forEach(tip => {
            const elements = document.querySelectorAll(tip.selector);
            elements.forEach(el => {
                el.setAttribute('title', tip.title);
                el.setAttribute('data-bs-toggle', 'tooltip');
            });
        });

        // 初始化Bootstrap工具提示
        if (window.bootstrap && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });

})();
