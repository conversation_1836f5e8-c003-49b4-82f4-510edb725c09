/**
 * 表格优化器
 * 专门优化大表格的渲染和内存使用
 */

(function() {
    'use strict';

    // 表格优化配置
    const TABLE_CONFIG = {
        MAX_VISIBLE_ROWS: 20, // 最大可见行数
        VIRTUAL_SCROLL_THRESHOLD: 50, // 启用虚拟滚动的阈值
        LAZY_RENDER_DELAY: 100, // 延迟渲染时间 (ms)
        CHUNK_SIZE: 10, // 分块渲染大小
        DEBOUNCE_DELAY: 300 // 防抖延迟
    };

    /**
     * 优化表格渲染
     */
    function optimizeTable(table) {
        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        const rows = Array.from(tbody.children);
        if (rows.length <= TABLE_CONFIG.MAX_VISIBLE_ROWS) return;

        console.log(`优化表格: ${rows.length} 行数据`);

        // 如果行数超过阈值，启用虚拟滚动
        if (rows.length > TABLE_CONFIG.VIRTUAL_SCROLL_THRESHOLD) {
            enableVirtualScrolling(table, rows);
        } else {
            enableLazyRendering(table, rows);
        }
    }

    /**
     * 启用虚拟滚动
     */
    function enableVirtualScrolling(table, allRows) {
        const tbody = table.querySelector('tbody');
        const container = table.closest('.table-responsive') || table.parentElement;
        
        let startIndex = 0;
        let endIndex = TABLE_CONFIG.MAX_VISIBLE_ROWS;

        // 创建滚动容器
        if (!container.style.height) {
            container.style.height = '400px';
            container.style.overflowY = 'auto';
        }

        // 渲染可见行
        function renderVisibleRows() {
            // 清空tbody
            tbody.innerHTML = '';

            // 添加占位符（上方）
            if (startIndex > 0) {
                const topSpacer = document.createElement('tr');
                topSpacer.innerHTML = `<td colspan="100%" style="height: ${startIndex * 40}px; padding: 0; border: none;"></td>`;
                tbody.appendChild(topSpacer);
            }

            // 渲染可见行
            for (let i = startIndex; i < Math.min(endIndex, allRows.length); i++) {
                tbody.appendChild(allRows[i].cloneNode(true));
            }

            // 添加占位符（下方）
            if (endIndex < allRows.length) {
                const bottomSpacer = document.createElement('tr');
                const remainingHeight = (allRows.length - endIndex) * 40;
                bottomSpacer.innerHTML = `<td colspan="100%" style="height: ${remainingHeight}px; padding: 0; border: none;"></td>`;
                tbody.appendChild(bottomSpacer);
            }
        }

        // 滚动事件处理
        const handleScroll = debounce(() => {
            const scrollTop = container.scrollTop;
            const rowHeight = 40; // 假设每行40px高度
            const newStartIndex = Math.floor(scrollTop / rowHeight);
            const newEndIndex = newStartIndex + TABLE_CONFIG.MAX_VISIBLE_ROWS;

            if (newStartIndex !== startIndex) {
                startIndex = newStartIndex;
                endIndex = newEndIndex;
                renderVisibleRows();
            }
        }, TABLE_CONFIG.DEBOUNCE_DELAY);

        container.addEventListener('scroll', handleScroll);
        renderVisibleRows();

        // 标记为已优化
        table.dataset.optimized = 'virtual-scroll';
    }

    /**
     * 启用延迟渲染
     */
    function enableLazyRendering(table, allRows) {
        const tbody = table.querySelector('tbody');
        
        // 清空tbody
        tbody.innerHTML = '';

        // 分块渲染
        let currentIndex = 0;
        
        function renderNextChunk() {
            const endIndex = Math.min(currentIndex + TABLE_CONFIG.CHUNK_SIZE, allRows.length);
            
            for (let i = currentIndex; i < endIndex; i++) {
                tbody.appendChild(allRows[i]);
            }
            
            currentIndex = endIndex;
            
            // 如果还有更多行，继续渲染
            if (currentIndex < allRows.length) {
                setTimeout(renderNextChunk, TABLE_CONFIG.LAZY_RENDER_DELAY);
            }
        }

        // 开始渲染
        renderNextChunk();

        // 标记为已优化
        table.dataset.optimized = 'lazy-render';
    }

    /**
     * 防抖函数
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 优化所有表格
     */
    function optimizeAllTables() {
        const tables = document.querySelectorAll('table:not([data-optimized])');
        tables.forEach(table => {
            optimizeTable(table);
        });
    }

    /**
     * 清理表格优化
     */
    function cleanupTableOptimizations() {
        const optimizedTables = document.querySelectorAll('table[data-optimized]');
        optimizedTables.forEach(table => {
            const container = table.closest('.table-responsive') || table.parentElement;
            
            // 移除滚动事件监听器
            const newContainer = container.cloneNode(true);
            container.parentNode.replaceChild(newContainer, container);
            
            // 移除优化标记
            table.removeAttribute('data-optimized');
        });
    }

    /**
     * 监听DOM变化
     */
    function observeTableChanges() {
        if (!window.MutationObserver) return;

        const observer = new MutationObserver(debounce((mutations) => {
            let shouldOptimize = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    // 检查是否有新的表格添加
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.tagName === 'TABLE' || node.querySelector('table')) {
                                shouldOptimize = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldOptimize) {
                optimizeAllTables();
            }
        }, TABLE_CONFIG.DEBOUNCE_DELAY));

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        return observer;
    }

    /**
     * 获取表格统计信息
     */
    function getTableStats() {
        const allTables = document.querySelectorAll('table');
        const optimizedTables = document.querySelectorAll('table[data-optimized]');
        
        let totalRows = 0;
        let optimizedRows = 0;
        
        allTables.forEach(table => {
            const tbody = table.querySelector('tbody');
            if (tbody) {
                const rowCount = tbody.children.length;
                totalRows += rowCount;
                
                if (table.dataset.optimized) {
                    optimizedRows += rowCount;
                }
            }
        });

        return {
            totalTables: allTables.length,
            optimizedTables: optimizedTables.length,
            totalRows: totalRows,
            optimizedRows: optimizedRows,
            optimizationRate: totalRows > 0 ? (optimizedRows / totalRows * 100).toFixed(1) + '%' : '0%'
        };
    }

    /**
     * 初始化表格优化器
     */
    function initTableOptimizer() {
        console.log('表格优化器已启动');

        // 优化现有表格
        optimizeAllTables();

        // 监听DOM变化
        const observer = observeTableChanges();

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            cleanupTableOptimizations();
            if (observer) {
                observer.disconnect();
            }
        });

        // 暴露全局接口
        window.TableOptimizer = {
            optimize: optimizeAllTables,
            cleanup: cleanupTableOptimizations,
            stats: getTableStats,
            config: TABLE_CONFIG
        };
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTableOptimizer);
    } else {
        initTableOptimizer();
    }

})();
