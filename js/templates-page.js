// Templates page JS extracted from inline script for maintainability
(function(){
  if (!window.__TEMPLATES_API__) return;

  // 辅助函数
  function escapeHtml(str){ var div = document.createElement('div'); div.textContent = String(str||''); return div.innerHTML; }

  window.showTemplateModal = function(templateId){
    var modal = new bootstrap.Modal(document.getElementById('templateModal'));
    var form = document.getElementById('templateForm');
    var title = document.getElementById('templateModalTitle');
    if (templateId){
      title.textContent = '编辑模板';
      window.loadTemplate(templateId);
    } else {
      title.textContent = '新增模板';
      form.reset();
      document.getElementById('templateId').value = '';
      renderFieldEditor('{"fields":[]}');
    }
    modal.show();
  };

  window.editTemplate = function(templateId){ window.showTemplateModal(templateId); };

  window.viewTemplate = function(templateId){
    fetch(buildUrl(window.__TEMPLATES_API__.getTemplate, {id: templateId}))
      .then(function(res){ return res.json(); })
      .then(function(result){
        if (!result.success){ showMessage(result.message||'加载模板详情失败','error'); return; }
        var template = result.data;
        var fieldsConfig = {};
        try { fieldsConfig = JSON.parse(template.fields_config || '{"fields":[]}'); } catch(e){ fieldsConfig = {"fields":[]}; }
        var reportType = template.report_type || 'daily';
        var reportTypeLabel = reportType === 'continuous' ? '连续报表' : '日报表';
        var reportTypeBadge = reportType === 'continuous' ? 'bg-info' : 'bg-success';
        var detailHtml = ''
          + '<div class="row g-3">'
          +   '<div class="col-md-4"><strong>模板代码：</strong><code>'+escapeHtml(template.code)+'</code></div>'
          +   '<div class="col-md-4"><strong>模板名称：</strong>'+escapeHtml(template.name)+'</div>'
          +   '<div class="col-md-4"><strong>报表类型：</strong><span class="badge '+reportTypeBadge+'">'+reportTypeLabel+'</span></div>'
          +   '<div class="col-12"><strong>字段配置：</strong>'
          +     '<div class="table-responsive mt-2">'
          +       '<table class="table table-sm table-bordered">'
          +         '<thead class="table-light"><tr>'
          +           '<th style="width:15%">字段键</th>'
          +           '<th style="width:18%">字段名</th>'
          +           '<th style="width:12%">类型</th>'
          +           '<th style="width:10%">小数位</th>'
          +           '<th style="width:10%">最小值</th>'
          +           '<th style="width:10%">最大值</th>'
          +           '<th style="width:8%" class="text-center">必填</th>'
          +           '<th style="width:17%">选项/备注</th>'
          +         '</tr></thead>'
          +         '<tbody>';
        if (fieldsConfig.fields){
          var typeMap = {number:'数值', text:'文本', time:'时间', date:'日期', select:'下拉选择'};
          fieldsConfig.fields.forEach(function(field){
            var typeText = typeMap[field.type] || field.type;
            var stepText = field.step ? field.step : '-';
            var minText = field.min !== undefined && field.min !== '' ? field.min : '-';
            var maxText = field.max !== undefined && field.max !== '' ? field.max : '-';
            var requiredText = field.required ? '<span class="badge bg-success">是</span>' : '<span class="badge bg-secondary">否</span>';

            // 处理选项或其他属性
            var extraInfo = '';
            if (field.type === 'select' && field.options && field.options.length > 0) {
              extraInfo = '选项: ' + field.options.map(function(opt){ return opt.label || opt.value; }).join(', ');
            } else {
              var otherAttrs = Object.keys(field)
                .filter(function(k){ return ['key','label','type','step','min','max','required','options'].indexOf(k)===-1; })
                .map(function(k){ return k+': '+field[k]; });
              extraInfo = otherAttrs.length > 0 ? otherAttrs.join(', ') : '-';
            }

            detailHtml += '<tr>'
              + '<td><code>'+escapeHtml(field.key)+'</code></td>'
              + '<td>'+escapeHtml(field.label)+'</td>'
              + '<td><span class="badge bg-info">'+escapeHtml(typeText)+'</span></td>'
              + '<td class="text-center">'+escapeHtml(stepText)+'</td>'
              + '<td class="text-center">'+escapeHtml(minText)+'</td>'
              + '<td class="text-center">'+escapeHtml(maxText)+'</td>'
              + '<td class="text-center">'+requiredText+'</td>'
              + '<td><small>'+escapeHtml(extraInfo)+'</small></td>'
              + '</tr>';
          });
        }
        detailHtml += '</tbody></table></div></div>'
          + '<div class="col-12"><strong>创建时间：</strong>'+template.created_at+'</div>'
          + '<div class="col-12"><strong>更新时间：</strong>'+template.updated_at+'</div>'
          + '</div>';
        document.getElementById('templateDetail').innerHTML = detailHtml;
      })
      .catch(function(err){ console.error('加载模板详情失败:', err); showMessage('网络错误，加载失败','error'); });
  };

  window.loadTemplate = function(templateId){
    fetch(buildUrl(window.__TEMPLATES_API__.getTemplate, {id: templateId}))
      .then(function(r){ return r.json(); })
      .then(function(result){
        if (!result.success){ showMessage(result.message||'加载模板失败','error'); return; }
        var template = result.data;
        document.getElementById('templateId').value = template.id;
        document.getElementById('templateCode').value = template.code;
        document.getElementById('templateName').value = template.name;
        document.getElementById('templateReportType').value = template.report_type || 'daily';
        var fieldsJson = template.fields_config || '{"fields":[]}';
        document.getElementById('templateFields').value = fieldsJson;
        renderFieldEditor(fieldsJson);
      })
      .catch(function(err){ console.error('加载模板失败:', err); showMessage('网络错误，加载失败','error'); });
  };

  window.saveTemplate = function(){
    var json = collectFieldsToJson();
    if (!json) return;
    document.getElementById('templateFields').value = json;
    var form = document.getElementById('templateForm');
    var formData = new FormData(form);
    var data = Object.fromEntries(formData.entries());
    try { JSON.parse(data.fields_config); } catch(e){ showMessage('字段配置JSON格式错误','error'); return; }
    fetch(window.__TEMPLATES_API__.saveTemplate, { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(data) })
      .then(function(r){ return r.json(); })
      .then(function(result){
        if (result.success){ showMessage(result.message,'success'); bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide(); setTimeout(function(){ location.reload(); },800); }
        else { showMessage(result.message||'保存失败','error'); }
      })
      .catch(function(err){ console.error('保存失败:',err); showMessage('网络错误，保存失败','error'); });
  };

  window.deleteTemplate = function(templateId){
    if (!confirm('确定要删除这个模板吗？此操作不可撤销。')) return;
    fetch(window.__TEMPLATES_API__.deleteTemplate, { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({id: templateId}) })
      .then(function(r){ return r.json(); })
      .then(function(result){ if (result.success){ showMessage(result.message,'success'); setTimeout(function(){ location.reload(); },1000);} else { showMessage(result.message||'删除失败','error'); } })
      .catch(function(err){ console.error('删除失败:', err); showMessage('网络错误，删除失败','error'); });
  };

  window.showMessage = function(message, type){
    type = type || 'info';
    var cls = {success:'alert-success', error:'alert-danger', warning:'alert-warning', info:'alert-info'}[type]||'alert-info';
    var html = '<div class="alert '+cls+' alert-dismissible fade show" role="alert">'+String(message)+'<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';
    var c = document.getElementById('messageContainer');
    if (!c){ c=document.createElement('div'); c.id='messageContainer'; c.style.position='fixed'; c.style.top='80px'; c.style.right='20px'; c.style.zIndex='9999'; c.style.maxWidth='400px'; document.body.appendChild(c);} 
    c.innerHTML = html;
    if (type!=='error') setTimeout(function(){ var a=c.querySelector('.alert'); if(a){a.classList.remove('show'); setTimeout(function(){a.remove();},150);} }, 3000);
  };

  // 可视化编辑器
  window.renderFieldEditor = function(fieldsJson){
    var config; try { config = JSON.parse(fieldsJson||'{"fields":[]}'); } catch(e){ config={fields:[]}; }
    var tbody = document.querySelector('#fieldTable tbody');
    tbody.innerHTML = '';
    (config.fields||[]).forEach(window.addFieldRow);
  };

  window.addFieldRow = function(field){
    var tbody = document.querySelector('#fieldTable tbody');
    var tr = document.createElement('tr');
    var f = Object.assign({key:'',label:'',type:'number',step:'0.01',min:'',max:'',width:'',unit:'',group:'',required:false}, field||{});

    // 类型中文映射
    var typeOptions = [
      {value:'number', text:'数值'},
      {value:'text', text:'文本'},
      {value:'time', text:'时间'},
      {value:'date', text:'日期'},
      {value:'select', text:'下拉选择'}
    ];

    var typeSelectHtml = typeOptions.map(function(opt){
      return '<option value="'+opt.value+'" '+(f.type===opt.value?'selected':'')+'>'+opt.text+'</option>';
    }).join('');

    tr.innerHTML =
      '<td><input class="form-control form-control-sm" data-k="key" value="'+escapeHtml(f.key)+'" placeholder="voltage_v"/></td>'+
      '<td><input class="form-control form-control-sm" data-k="label" value="'+escapeHtml(f.label)+'" placeholder="电压(V)"/></td>'+
      '<td><select class="form-select form-select-sm" data-k="type" onchange="toggleSelectOptions(this)">'
      + typeSelectHtml
      + '</select>'+(f.type==='select'?'<div class="mt-1"><button type="button" class="btn btn-xs btn-outline-info" onclick="editSelectOptions(this)" style="font-size:9px;padding:1px 3px;">选项</button></div>':'')+'</td>'+
      '<td><input class="form-control form-control-sm" data-k="step" type="text" value="'+escapeHtml(String(f.step||''))+'" placeholder="0.01"/></td>'+
      '<td><input class="form-control form-control-sm" data-k="min" type="text" value="'+escapeHtml(String(f.min||''))+'" placeholder="0"/></td>'+
      '<td><input class="form-control form-control-sm" data-k="max" type="text" value="'+escapeHtml(String(f.max||''))+'" placeholder="100"/></td>'+
      '<td><input class="form-control form-control-sm" data-k="width" type="text" value="'+escapeHtml(String(f.width||''))+'" placeholder="80px"/></td>'+
      '<td><input class="form-control form-control-sm" data-k="unit" type="text" value="'+escapeHtml(String(f.unit||''))+'" placeholder="V"/></td>'+
      '<td><input class="form-control form-control-sm" data-k="group" type="text" value="'+escapeHtml(String(f.group||''))+'" placeholder="基础参数"/></td>'+
      '<td><div class="form-check form-switch d-flex justify-content-center"><input class="form-check-input" data-k="required" type="checkbox" '+(f.required?'checked':'')+'></div></td>'+
      '<td><div class="d-flex gap-1 justify-content-center">'
      + '<button type="button" class="btn btn-outline-danger btn-sm" onclick="this.closest(\'tr\').remove()" title="删除" style="padding:1px 4px;font-size:10px;"><i class="fa fa-trash"></i></button>'
      + '<button type="button" class="btn btn-outline-secondary btn-sm" onclick="moveRow(this,-1)" title="上移" style="padding:1px 4px;font-size:10px;"><i class="fa fa-arrow-up"></i></button>'
      + '<button type="button" class="btn btn-outline-secondary btn-sm" onclick="moveRow(this,1)" title="下移" style="padding:1px 4px;font-size:10px;"><i class="fa fa-arrow-down"></i></button>'
      + '</div></td>';
    if (f.options) tr.dataset.options = JSON.stringify(f.options);
    tbody.appendChild(tr);

    // 确保必填项开关状态正确显示
    const requiredCheckbox = tr.querySelector('[data-k="required"]');
    if (requiredCheckbox && f.required) {
        requiredCheckbox.checked = true;
    }
  };

  window.moveRow = function(btn, delta){
    var tr = btn.closest('tr');
    var tbody = tr.parentNode;
    var rows = Array.from(tbody.children);
    var i = rows.indexOf(tr);
    var j = i + delta; if (j<0 || j>=rows.length) return;
    if (delta<0) tbody.insertBefore(tr, rows[j]); else tbody.insertBefore(rows[j], tr);
  };

  window.collectFieldsToJson = function(){
    var rows = document.querySelectorAll('#fieldTable tbody tr');
    var fields = [];
    for (var k=0; k<rows.length; k++){
      var tr = rows[k];
      var get = function(sel){ return tr.querySelector(sel); };
      var key = get('[data-k="key"]').value.trim();
      var label = get('[data-k="label"]').value.trim();
      if (!key || !label){ showMessage('字段键和字段名不能为空','error'); return null; }
      var type = get('[data-k="type"]').value;
      var step = get('[data-k="step"]').value.trim();
      var min = get('[data-k="min"]').value.trim();
      var max = get('[data-k="max"]').value.trim();
      var width = get('[data-k="width"]').value.trim();
      var unit = get('[data-k="unit"]').value.trim();
      var group = get('[data-k="group"]').value.trim();
      var required = get('[data-k="required"]').checked;
      var field = {key:key, label:label, type:type};
      if (step) field.step = step;
      if (min!=='') field.min = isNaN(Number(min)) ? min : Number(min);
      if (max!=='') field.max = isNaN(Number(max)) ? max : Number(max);
      if (width) field.width = width;
      if (unit) field.unit = unit;
      if (group) field.group = group;
      field.required = required; // 明确保存必填项状态，无论是 true 还是 false
      if (type==='select' && tr.dataset.options){ try { field.options = JSON.parse(tr.dataset.options); } catch(e){} }
      fields.push(field);
    }
    return JSON.stringify({fields: fields});
  };

  window.toggleSelectOptions = function(selectEl){
    var td = selectEl.parentNode;
    var isSelect = selectEl.value === 'select';
    var btn = td.querySelector('.btn-outline-info');
    if (isSelect && !btn){
      btn = document.createElement('button');
      btn.type = 'button';
      btn.className = 'btn btn-sm btn-outline-info mt-1';
      btn.textContent = '管理选项';
      btn.onclick = function(){ window.editSelectOptions(btn); };
      td.appendChild(btn);
    } else if (!isSelect && btn){ btn.remove(); }
  };

  window.editSelectOptions = function(btn){
    var tr = btn.closest('tr');
    var field = (function(tr){ var g=function(s){return tr.querySelector(s)}; return { key:g('[data-k="key"]').value.trim(), label:g('[data-k="label"]').value.trim(), type:g('[data-k="type"]').value, step:g('[data-k="step"]').value.trim(), min:g('[data-k="min"]').value.trim(), max:g('[data-k="max"]').value.trim(), required:g('[data-k="required"]').checked, options: JSON.parse(tr.dataset.options||'[]') }; })(tr);
    var options = field.options || [];
    var modal = document.createElement('div'); modal.className='modal fade';
    modal.innerHTML = '<div class="modal-dialog"><div class="modal-content">'
      + '<div class="modal-header"><h5 class="modal-title">编辑选项 - '+escapeHtml(field.label||field.key)+'</h5>'
      + '<button type="button" class="btn-close" data-bs-dismiss="modal"></button></div>'
      + '<div class="modal-body">'
      +   '<div class="mb-2"><button type="button" class="btn btn-sm btn-primary" onclick="addOption()">新增选项</button></div>'
      +   '<div id="optionsList"></div></div>'
      + '<div class="modal-footer">'
      +   '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>'
      +   '<button type="button" class="btn btn-primary" onclick="saveOptions()">保存</button>'
      + '</div></div></div>';
    document.body.appendChild(modal);
    window.currentEditingRow = tr;
    window.currentOptions = options.slice();
    window.renderOptions();
    var bsModal = new bootstrap.Modal(modal); bsModal.show();
    modal.addEventListener('hidden.bs.modal', function(){ modal.remove(); });
  };

  window.renderOptions = function(){
    var container = document.getElementById('optionsList');
    container.innerHTML = '';
    window.currentOptions.forEach(function(opt, i){
      var div = document.createElement('div');
      div.className = 'input-group mb-2';
      div.innerHTML = '<input type="text" class="form-control" value="'+escapeHtml(opt.value||'')+'" placeholder="选项值" data-i="'+i+'" data-k="value">'
        + '<input type="text" class="form-control" value="'+escapeHtml(opt.label||'')+'" placeholder="显示文本" data-i="'+i+'" data-k="label">'
        + '<button type="button" class="btn btn-outline-danger" onclick="removeOption('+i+')">删除</button>';
      container.appendChild(div);
    });
  };

  window.addOption = function(){ window.currentOptions.push({value:'', label:''}); window.renderOptions(); };
  window.removeOption = function(i){ window.currentOptions.splice(i,1); window.renderOptions(); };
  window.saveOptions = function(){
    var inputs = document.querySelectorAll('#optionsList input'); var options = []; var optMap = {};
    inputs.forEach(function(inp){ var i=parseInt(inp.dataset.i); var k=inp.dataset.k; if(!optMap[i]) optMap[i]={}; optMap[i][k] = inp.value.trim(); });
    Object.values(optMap).forEach(function(opt){ if (opt.value) options.push(opt); });
    window.currentEditingRow.dataset.options = JSON.stringify(options);
    bootstrap.Modal.getInstance(document.querySelector('.modal.show')).hide();
  };

  window.buildUrl = function(base, params){ var hasQuery = base.indexOf('?')>=0; var sep=hasQuery?'&':'?'; var query = new URLSearchParams(params).toString(); return base+sep+query; };
})();

