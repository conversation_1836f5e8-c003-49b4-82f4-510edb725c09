<?php
// 一键初始化脚本（通过浏览器访问 /bbgl/scripts/install.php 执行）
// 将创建数据库、导入结构与种子、并创建管理员 admin/123456

declare(strict_types=1);
ini_set('display_errors', '1');
error_reporting(E_ALL);

date_default_timezone_set('Asia/Shanghai');

$cfg = require __DIR__ . '/../config/db.php';
$host = $cfg['host'];
$port = (int)$cfg['port'];
$dbname = $cfg['dbname'];
$user = $cfg['username'];
$pass = $cfg['password'];
$charset = $cfg['charset'] ?? 'utf8mb4';

header('Content-Type: text/plain; charset=UTF-8');

function p($msg){ echo '['.date('H:i:s')."] $msg\n"; }

try {
    // 1) 连接到服务器（不选库），尝试创建数据库
    $dsnServer = sprintf('mysql:host=%s;port=%d;charset=%s', $host, $port, $charset);
    $pdoServer = new PDO($dsnServer, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    p('连接到 MySQL 服务器成功');
    $pdoServer->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` DEFAULT CHARACTER SET {$charset}");
    p("数据库已准备：{$dbname}");

    // 2) 连接目标库
    $dsnDb = sprintf('mysql:host=%s;port=%d;dbname=%s;charset=%s', $host, $port, $dbname, $charset);
    $pdo = new PDO($dsnDb, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_MULTI_STATEMENTS => true,
    ]);

    // 3) 导入 schema 与 seed
    $base = realpath(__DIR__ . '/../database');
    $files = ['schema.sql','seed.sql'];
    foreach ($files as $f){
        $path = $base . DIRECTORY_SEPARATOR . $f;
        if (!is_file($path)) { p("跳过：{$f}（未找到）"); continue; }
        $sql = file_get_contents($path);
        // 兼容 Windows 换行，尽量温和拆分语句
        $stmts = preg_split('/;\s*(?:\r?\n)/', $sql);
        $count=0;
        foreach ($stmts as $stmt){
            $stmt = trim($stmt);
            if ($stmt === '' || str_starts_with($stmt, '--')) continue;
            $pdo->exec($stmt);
            $count++;
        }
        p("导入 {$f} 完成（{$count} 条语句）");
    }

    // 3.1 兜底：确保核心表存在（防止某些环境 SQL 被跳过）
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
      id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
      username VARCHAR(64) NOT NULL UNIQUE COMMENT '登录账号',
      password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
      real_name VARCHAR(64) NOT NULL COMMENT '姓名',
      email VARCHAR(128) NULL COMMENT '邮箱',
      status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1启用 0禁用',
      last_login_at DATETIME NULL COMMENT '上次登录时间',
      created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    ) ENGINE=InnoDB COMMENT='用户表'");
    $pdo->exec("CREATE TABLE IF NOT EXISTS roles (
      id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
      code VARCHAR(32) NOT NULL UNIQUE COMMENT '角色编码: admin/mod/user/site/ctrl/cb26',
      name VARCHAR(64) NOT NULL COMMENT '角色名称',
      created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) ENGINE=InnoDB COMMENT='角色表'");
    $pdo->exec("CREATE TABLE IF NOT EXISTS permissions (
      id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
      code VARCHAR(64) NOT NULL UNIQUE COMMENT '权限编码',
      name VARCHAR(128) NOT NULL COMMENT '权限名称',
      created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) ENGINE=InnoDB COMMENT='权限表'");
    $pdo->exec("CREATE TABLE IF NOT EXISTS user_role (
      user_id BIGINT NOT NULL COMMENT '用户ID',
      role_id BIGINT NOT NULL COMMENT '角色ID',
      PRIMARY KEY(user_id, role_id)
    ) ENGINE=InnoDB COMMENT='用户-角色关联'");
    $pdo->exec("CREATE TABLE IF NOT EXISTS role_permission (
      role_id BIGINT NOT NULL COMMENT '角色ID',
      permission_id BIGINT NOT NULL COMMENT '权限ID',
      PRIMARY KEY(role_id, permission_id)
    ) ENGINE=InnoDB COMMENT='角色-权限关联'");

    // 4) 创建管理员（使用 PHP password_hash）
    $username = 'admin';
    $hash = password_hash('123456', PASSWORD_DEFAULT);
    $realName = '管理员';
    $email = '<EMAIL>';

    $pdo->prepare("INSERT INTO users(username,password_hash,real_name,email,status)
                   VALUES(:u,:h,:r,:e,1)
                   ON DUPLICATE KEY UPDATE password_hash=VALUES(password_hash), real_name=VALUES(real_name), status=1")
        ->execute([':u'=>$username, ':h'=>$hash, ':r'=>$realName, ':e'=>$email]);
    p('管理员账户已创建/更新');

    // 5) 确保 admin 角色存在
    $roleId = $pdo->query("SELECT id FROM roles WHERE code='admin' LIMIT 1")->fetchColumn();
    if (!$roleId){
        $pdo->exec("INSERT INTO roles(code,name) VALUES('admin','管理员')");
        $roleId = (int)$pdo->lastInsertId();
        p('已补充 admin 角色');
    }

    // 6) 绑定用户-角色
    $userId = $pdo->query("SELECT id FROM users WHERE username='admin' LIMIT 1")->fetchColumn();
    if ($userId){
        $pdo->prepare("INSERT IGNORE INTO user_role(user_id,role_id) VALUES(?,?)")->execute([(int)$userId,(int)$roleId]);
        p('已绑定管理员角色');
    }

    p('初始化完成。建议删除 scripts/install.php 以提高安全性。');
} catch (Throwable $e){
    http_response_code(500);
    p('安装失败：' . $e->getMessage());
    p($e->getTraceAsString());
}

