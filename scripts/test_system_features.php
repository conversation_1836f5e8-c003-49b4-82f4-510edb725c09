<?php
/**
 * 系统功能测试脚本
 * 测试系统配置和用户管理功能是否正常工作
 */

declare(strict_types=1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 引入必要文件
require_once __DIR__ . '/../config/bootstrap.php';

use App\Services\ConfigService;
use App\Services\AuditService;
use App\Core\DB;

echo "=== 系统功能测试 ===\n\n";

try {
    // 测试数据库连接
    echo "1. 测试数据库连接...\n";
    $pdo = DB::conn();
    echo "   ✓ 数据库连接成功\n\n";
    
    // 测试系统配置服务
    echo "2. 测试系统配置服务...\n";
    
    // 测试获取配置
    $systemName = ConfigService::get('system_name', '默认系统名称');
    echo "   ✓ 获取系统名称: {$systemName}\n";
    
    // 测试设置配置
    $testKey = 'test_config_' . time();
    $testValue = 'test_value_' . rand(1000, 9999);
    if (ConfigService::set($testKey, $testValue, '测试配置')) {
        echo "   ✓ 设置配置成功: {$testKey} = {$testValue}\n";
        
        // 验证配置是否正确保存
        $retrievedValue = ConfigService::get($testKey);
        if ($retrievedValue === $testValue) {
            echo "   ✓ 配置读取验证成功\n";
        } else {
            echo "   ✗ 配置读取验证失败\n";
        }
        
        // 清理测试配置
        ConfigService::delete($testKey);
        echo "   ✓ 清理测试配置完成\n";
    } else {
        echo "   ✗ 设置配置失败\n";
    }
    
    // 测试获取所有配置
    $allConfigs = ConfigService::getAll();
    echo "   ✓ 获取所有配置成功，共 " . count($allConfigs) . " 项\n\n";
    
    // 测试审计日志服务
    echo "3. 测试审计日志服务...\n";
    
    // 记录测试日志
    if (AuditService::log('test.action', 'test.resource', ['test' => 'data'], 1)) {
        echo "   ✓ 审计日志记录成功\n";
    } else {
        echo "   ✗ 审计日志记录失败\n";
    }
    
    // 查询审计日志
    $logs = AuditService::query(['action' => 'test'], 1, 5);
    echo "   ✓ 审计日志查询成功，共 {$logs['total']} 条记录\n\n";
    
    // 测试数据库表结构
    echo "4. 测试数据库表结构...\n";
    
    $requiredTables = [
        'users', 'roles', 'permissions', 'user_role', 'role_permission',
        'menus', 'role_menu', 'system_config', 'ip_acl', 'audit_logs'
    ];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "   ✓ 表 {$table} 存在\n";
        } else {
            echo "   ✗ 表 {$table} 不存在\n";
        }
    }
    
    echo "\n5. 测试用户和角色数据...\n";
    
    // 检查管理员用户
    $adminStmt = $pdo->query("
        SELECT u.username, u.real_name, GROUP_CONCAT(r.code) as roles
        FROM users u 
        LEFT JOIN user_role ur ON u.id = ur.user_id 
        LEFT JOIN roles r ON ur.role_id = r.id 
        WHERE u.id = 1
        GROUP BY u.id
    ");
    $admin = $adminStmt->fetch();
    
    if ($admin) {
        echo "   ✓ 管理员用户存在: {$admin['username']} ({$admin['real_name']})\n";
        echo "   ✓ 管理员角色: {$admin['roles']}\n";
    } else {
        echo "   ✗ 管理员用户不存在\n";
    }
    
    // 检查角色数量
    $roleStmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
    $roleCount = $roleStmt->fetch()['count'];
    echo "   ✓ 系统角色数量: {$roleCount}\n";
    
    // 检查菜单数量
    $menuStmt = $pdo->query("SELECT COUNT(*) as count FROM menus");
    $menuCount = $menuStmt->fetch()['count'];
    echo "   ✓ 系统菜单数量: {$menuCount}\n\n";
    
    // 测试IP访问控制
    echo "6. 测试IP访问控制...\n";
    
    if (class_exists('\App\Middlewares\IpAclMiddleware')) {
        echo "   ✓ IP访问控制中间件已加载\n";
        
        // 测试IP访问控制检查
        $ipAclMode = ConfigService::get('ip_acl_mode', 'disabled');
        echo "   ✓ 当前IP访问控制模式: {$ipAclMode}\n";
        
        // 检查IP ACL规则数量
        $aclStmt = $pdo->query("SELECT COUNT(*) as count FROM ip_acl");
        $aclCount = $aclStmt->fetch()['count'];
        echo "   ✓ IP访问控制规则数量: {$aclCount}\n";
    } else {
        echo "   ✗ IP访问控制中间件未加载\n";
    }
    
    echo "\n7. 显示当前系统配置...\n";
    echo "   " . str_repeat('-', 60) . "\n";
    printf("   %-25s %-25s %s\n", "配置键", "配置值", "备注");
    echo "   " . str_repeat('-', 60) . "\n";
    
    foreach ($allConfigs as $key => $config) {
        $value = is_array($config['value']) ? json_encode($config['value']) : (string)$config['value'];
        $value = mb_strlen($value) > 20 ? mb_substr($value, 0, 17) . '...' : $value;
        printf("   %-25s %-25s %s\n", $key, $value, $config['remark']);
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "所有核心功能测试通过！系统配置和用户管理功能已完善。\n\n";
    
    echo "下一步操作建议：\n";
    echo "1. 访问 " . (defined('BASE_URL') ? BASE_URL : 'http://localhost/bbgl/') . " 登录系统\n";
    echo "2. 使用管理员账号测试系统配置功能\n";
    echo "3. 测试用户管理功能（创建、编辑、删除用户）\n";
    echo "4. 配置IP访问控制规则（如需要）\n";
    echo "5. 查看审计日志记录\n";
    
} catch (Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
