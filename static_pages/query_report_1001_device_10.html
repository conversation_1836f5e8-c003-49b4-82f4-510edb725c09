<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据查询 - 泵运行记录（连续） - 溶气泵 - 查询</title>
    <link href="http://localhost:8088/bbgl/css/bootstrap.min.css" rel="stylesheet">
    <link href="http://localhost:8088/bbgl/css/all.min.css" rel="stylesheet">
    <link href="http://localhost:8088/bbgl/css/app.css" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; background: #f8f9fa; }
        .container-fluid { padding: 20px; }
        .auth-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(248, 249, 250, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
    </style>
    <script>
        // 权限验证脚本 - 在页面加载前执行
        (function() {
            // 显示加载遮罩
            document.addEventListener("DOMContentLoaded", function() {
                const loadingDiv = document.createElement("div");
                loadingDiv.className = "auth-loading";
                loadingDiv.innerHTML = `
                    <div style="text-align: center;">
                        <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>验证权限中...</p>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // 检查用户登录状态
                fetch("http://localhost:8088/bbgl/index.php?r=api/user")
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.data) {
                            // 用户已登录，检查具体权限
                            const userRoles = data.data.roles || [];
                            const reportGroup = getReportGroup();

                            if (hasReportAccess(userRoles, reportGroup)) {
                                // 有权限访问此报表，移除加载遮罩
                                loadingDiv.remove();
                                // 初始化用户信息（与系统权限结构一致）
                                window.__USER_ROLES__ = userRoles;
                                window.__USER_PERMISSIONS__ = {
                                    isAdmin: userRoles.includes("admin"),
                                    isMod: userRoles.includes("mod"),
                                    canEdit: userRoles.includes("admin") || userRoles.includes("mod"),
                                    canView: true,
                                    roles: userRoles
                                };
                                // 更新用户显示信息
                                window.__CURRENT_USER__ = {
                                    id: data.data.id,
                                    username: data.data.username,
                                    real_name: data.data.real_name
                                };
                            } else {
                                // 没有权限访问此报表
                                showAccessDenied(reportGroup);
                            }
                        } else {
                            // 用户未登录，重定向到登录页面
                            redirectToLogin();
                        }
                    })
                    .catch(error => {
                        console.error("权限验证失败:", error);
                        redirectToLogin();
                    });
            });

            // 获取当前报表所属的分组
            function getReportGroup() {
                // 从URL中提取报表ID
                const url = window.location.href;
                const reportIdMatch = url.match(/entry_report_(\d+)_/);

                if (reportIdMatch) {
                    const reportId = parseInt(reportIdMatch[1]);
                    // 根据报表ID判断分组（基于数据库中的实际分类）
                    if (reportId === 2005) {
                        return "cb26"; // CB26设备
                    } else if (reportId === 2006) {
                        return "ctrl"; // 中控设备
                    } else {
                        return "site"; // 现场设备（1001, 1002, 2001等）
                    }
                }

                // 备用方案：从标题判断
                const title = document.title || "";
                if (title.includes("CB26") || title.toLowerCase().includes("cb26")) {
                    return "cb26";
                } else if (title.includes("中控") || title.toLowerCase().includes("ctrl")) {
                    return "ctrl";
                } else {
                    return "site"; // 默认为现场
                }
            }

            // 检查用户是否有权限访问指定分组的报表
            function hasReportAccess(userRoles, reportGroup) {
                // 管理员和普通管理员可以访问所有报表
                if (userRoles.includes("admin") || userRoles.includes("mod")) {
                    return true;
                }
                // 其他用户只能访问对应角色的报表
                return userRoles.includes(reportGroup);
            }

            function showAccessDenied(reportGroup) {
                const groupNames = {
                    "site": "现场",
                    "cb26": "CB26",
                    "ctrl": "中控"
                };
                const groupName = groupNames[reportGroup] || reportGroup;

                document.body.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <i class="fa fa-ban fa-3x mb-3 text-danger"></i>
                            <h4>权限不足</h4>
                            <p class="text-muted">您没有权限访问${groupName}相关的报表</p>
                            <p class="small text-muted">请联系管理员分配相应的角色权限</p>
                            <a href="http://localhost:8088/bbgl/" class="btn btn-primary">返回首页</a>
                        </div>
                    </div>
                `;
            }

            // 获取当前报表所属的分组
            function getReportGroup() {
                const title = document.title || "";
                if (title.includes("CB26") || title.toLowerCase().includes("cb26")) {
                    return "cb26";
                } else if (title.includes("中控") || title.toLowerCase().includes("ctrl")) {
                    return "ctrl";
                } else {
                    return "site"; // 默认为现场
                }
            }

            // 检查用户是否有权限访问指定分组的报表
            function hasReportAccess(userRoles, reportGroup) {
                // 管理员和普通管理员可以访问所有报表
                if (userRoles.includes("admin") || userRoles.includes("mod")) {
                    return true;
                }
                // 其他用户只能访问对应角色的报表
                return userRoles.includes(reportGroup);
            }

            function showAccessDenied(reportGroup) {
                const groupNames = {
                    "site": "现场",
                    "cb26": "CB26",
                    "ctrl": "中控"
                };
                const groupName = groupNames[reportGroup] || reportGroup;

                document.body.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <i class="fa fa-ban fa-3x mb-3 text-danger"></i>
                            <h4>权限不足</h4>
                            <p class="text-muted">您没有权限访问${groupName}相关的报表</p>
                            <p class="small text-muted">请联系管理员分配相应的角色权限</p>
                            <a href="http://localhost:8088/bbgl/" class="btn btn-primary">返回首页</a>
                        </div>
                    </div>
                `;
            }

            function redirectToLogin() {
                const currentUrl = encodeURIComponent(window.location.href);
                const loginUrl = "http://localhost:8088/bbgl/index.php?r=frontend/login&redirect=" + currentUrl;

                document.body.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <i class="fa fa-lock fa-3x mb-3 text-warning"></i>
                            <h4>需要登录</h4>
                            <p class="text-muted">此页面需要登录后才能访问</p>
                            <a href="${loginUrl}" class="btn btn-primary">前往登录</a>
                        </div>
                    </div>
                `;
            }
        })();
    </script>
</head>
<body><main class="container-fluid py-3" style="margin-left: 0; max-width: 100%;"><div class="page-head mb-3"><div class="text-center"><div class="page-title">泵运行记录（连续） - 溶气泵 - 查询</div></div></div><div class="card mb-3"><div class="card-body"><div class="row g-3"><div class="col-sm-3"><label class="form-label">设备选择</label><select id="deviceSelect" class="form-select"><option value="">请选择设备...</option></select></div><div class="col-sm-3"><label class="form-label">日期</label><input id="entryDate" type="date" class="form-control" value="2025-09-06"></div><div class="col-sm-5 d-flex align-items-end"><button id="btnQuery" class="btn btn-primary">查询数据</button></div></div></div></div><div class="card"><div class="card-body"><div id="entryTableContainer"><div class="text-center text-muted py-4"><i class="fa fa-info-circle fa-2x mb-2"></i><p>请选择设备和日期，数据将自动加载</p><p class="small">日报表：按固定时间段录入数据</p></div></div><div id="dutyStaffContainer" style="display:none;"></div></div></div></main><script>
window.__REPORT_API__ = {
  reportId: 1001,
  templateCode: "pumps",
  reportType: "daily",
  deviceId: 10,
  isQueryMode: true,
  getDevices: "/bbgl/index.php?r=api/reports/devices&report_id=1001&device_id=10",
  loadData: "/bbgl/index.php?r=api/entry/load",
  saveData: null
};

window.__USER_ROLES__ = []; // 将通过API动态更新
window.__USER_PERMISSIONS__ = {}; // 用户权限缓存

window.__SYSTEM_CONFIG__ = {
  show_entry_info: true,
  show_modify_info: true
};

window.__REPORT_CONFIG__ = {
  time_limit_days: 2,
  time_limit_type: "hour",
  duty_staff: {"enabled":true,"required":true,"day_label":"白班值班人","night_label":"夜班值班人"}
};
</script><script src="/bbgl/js/entry-generic.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
  console.log("Static page loaded, initializing duty staff form");
  // 初始化空的值班人员数据
  if (typeof showDutyStaffForm === "function") {
    showDutyStaffForm({});
  }
});
</script>
</body></html>