<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试登录页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .checkbox-group {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
        }
        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>管理后台登录测试</h2>
        <form>
            <div class="form-group">
                <label>管理员账号</label>
                <input type="text" name="username" placeholder="请输入管理员账号" value="admin">
            </div>
            
            <div class="form-group">
                <label>管理员密码</label>
                <input type="password" name="password" placeholder="请输入管理员密码" value="123456">
            </div>
            
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="remember_username" name="remember_username" checked>
                    <label for="remember_username">记住账号</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="remember_password" name="remember_password" checked>
                    <label for="remember_password">记住密码</label>
                </div>
            </div>
            
            <button type="submit" class="btn">登录管理后台</button>
        </form>
        
        <script>
            // 测试复选框功能
            document.getElementById('remember_password').addEventListener('change', function() {
                if (this.checked) {
                    document.getElementById('remember_username').checked = true;
                }
            });
            
            document.getElementById('remember_username').addEventListener('change', function() {
                if (!this.checked) {
                    document.getElementById('remember_password').checked = false;
                }
            });
        </script>
    </div>
</body>
</html>