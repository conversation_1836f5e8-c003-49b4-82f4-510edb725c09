<?php
/**
 * 静态化页面生成器
 * 根据报表配置生成静态的录入和查询页面
 */

class StaticPageGenerator {
    private $config;
    private $templates;
    
    public function __construct($config = []) {
        $this->config = array_merge([
            'output_dir' => '../static_pages/',
            'template_dir' => '../templates/',
            'cache_enabled' => true,
            'minify_html' => true,
            'minify_css' => true,
            'minify_js' => true
        ], $config);
        
        $this->templates = [];
    }
    
    /**
     * 生成静态录入页面（只包含主内容，不含导航）
     */
    public function generateEntryPage($reportId, $templateConfig, $deviceId = null) {
        $pageConfig = [
            'report_id' => $reportId,
            'template' => $templateConfig,
            'type' => 'entry',
            'device_id' => $deviceId,
            'generated_at' => date('Y-m-d H:i:s')
        ];

        // 生成页面内容（不含顶部、底部、左侧菜单）
        $fullPage = $this->buildContentOnlyPage($pageConfig);

        // 保存文件（使用设备ID）
        $filename = $deviceId
            ? "entry_report_{$reportId}_device_{$deviceId}.html"
            : "entry_report_{$reportId}.html";
        $filepath = $this->config['output_dir'] . $filename;

        if (!is_dir($this->config['output_dir'])) {
            mkdir($this->config['output_dir'], 0755, true);
        }

        file_put_contents($filepath, $fullPage);

        return [
            'success' => true,
            'file' => $filename,
            'path' => $filepath,
            'size' => filesize($filepath),
            'device_id' => $deviceId
        ];
    }
    
    /**
     * 生成静态查询页面（只包含主内容，不含导航）
     */
    public function generateQueryPage($reportId, $templateConfig, $deviceId = null) {
        $pageConfig = [
            'report_id' => $reportId,
            'template' => $templateConfig,
            'type' => 'query',
            'device_id' => $deviceId,
            'generated_at' => date('Y-m-d H:i:s')
        ];

        // 生成页面内容（不含顶部、底部、左侧菜单）
        $fullPage = $this->buildContentOnlyPage($pageConfig);

        // 保存文件（使用设备ID）
        $filename = $deviceId
            ? "query_report_{$reportId}_device_{$deviceId}.html"
            : "query_report_{$reportId}.html";
        $filepath = $this->config['output_dir'] . $filename;

        if (!is_dir($this->config['output_dir'])) {
            mkdir($this->config['output_dir'], 0755, true);
        }

        file_put_contents($filepath, $fullPage);

        return [
            'success' => true,
            'file' => $filename,
            'path' => $filepath,
            'size' => filesize($filepath),
            'device_id' => $deviceId
        ];
    }

    /**
     * 批量生成按设备分组的静态页面
     */
    public function generateByDevices($reportId, $templateConfig) {
        $results = [];

        // 获取该报表的所有设备
        $devices = $this->getDevicesForReport($reportId);

        foreach ($devices as $device) {
            $deviceId = $device['id'];
            $deviceName = $device['name'];

            // 生成录入页面
            $entryResult = $this->generateEntryPage($reportId, $templateConfig, $deviceId);
            $results[] = [
                'type' => 'entry',
                'device_id' => $deviceId,
                'device_name' => $deviceName,
                'result' => $entryResult
            ];

            // 生成查询页面
            $queryResult = $this->generateQueryPage($reportId, $templateConfig, $deviceId);
            $results[] = [
                'type' => 'query',
                'device_id' => $deviceId,
                'device_name' => $deviceName,
                'result' => $queryResult
            ];
        }

        // 不再生成通用页面，只生成设备专用页面

        return $results;
    }

    /**
     * 清理无效的静态页面
     */
    public function cleanupInvalidPages() {
        $results = [
            'deleted' => [],
            'kept' => [],
            'errors' => []
        ];

        $staticDir = $this->config['output_dir'];
        if (!is_dir($staticDir)) {
            return $results;
        }

        // 获取所有静态页面文件
        $files = glob($staticDir . '*.html');

        foreach ($files as $file) {
            $filename = basename($file);

            // 解析文件名获取报表ID和设备ID
            if (preg_match('/^(entry|query)_report_(\d+)(?:_device_(\d+))?\.html$/', $filename, $matches)) {
                $pageType = $matches[1];
                $reportId = (int)$matches[2];
                $deviceId = isset($matches[3]) ? (int)$matches[3] : null;

                // 检查报表是否仍然存在且启用
                if (!$this->isReportValid($reportId)) {
                    if (unlink($file)) {
                        $results['deleted'][] = $filename . ' (报表已删除或禁用)';
                    } else {
                        $results['errors'][] = $filename . ' (删除失败)';
                    }
                    continue;
                }

                // 如果是设备专用页面，检查设备是否仍然存在
                if ($deviceId && !$this->isDeviceValid($reportId, $deviceId)) {
                    if (unlink($file)) {
                        $results['deleted'][] = $filename . ' (设备已删除)';
                    } else {
                        $results['errors'][] = $filename . ' (删除失败)';
                    }
                    continue;
                }

                $results['kept'][] = $filename;
            } else {
                // 未知格式的文件，保留
                $results['kept'][] = $filename;
            }
        }

        return $results;
    }

    /**
     * 检查报表是否有效
     */
    private function isReportValid($reportId) {
        require_once __DIR__ . '/../app/Core/DB.php';
        $pdo = \App\Core\DB::conn();

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM reports WHERE id = ? AND enabled = 1");
        $stmt->execute([$reportId]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * 检查设备是否仍然有效
     */
    private function isDeviceValid($reportId, $deviceId) {
        require_once __DIR__ . '/../app/Core/DB.php';
        $pdo = \App\Core\DB::conn();

        // 检查设备是否存在且仍然关联到该报表
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM report_devices rd
            JOIN devices d ON rd.device_id = d.id
            WHERE rd.report_id = ? AND d.id = ?
        ");
        $stmt->execute([$reportId, $deviceId]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * 文件名安全化处理
     */
    private function sanitizeFilename($filename) {
        if (empty($filename)) {
            return '';
        }

        // 移除或替换不安全的字符，保留中文、英文、数字、下划线、连字符
        $filename = preg_replace('/[^\p{L}\p{N}_-]/u', '_', $filename);
        return trim($filename, '_');
    }

    /**
     * 获取报表关联的设备列表
     */
    private function getDevicesForReport($reportId) {
        require_once __DIR__ . '/../app/Core/DB.php';
        $pdo = \App\Core\DB::conn();

        // 获取报表关联的设备
        $stmt = $pdo->prepare("
            SELECT DISTINCT d.id, d.name
            FROM report_devices rd
            JOIN devices d ON rd.device_id = d.id
            WHERE rd.report_id = ?
            ORDER BY d.name
        ");
        $stmt->execute([$reportId]);
        return $stmt->fetchAll();
    }

    /**
     * 从设备名称中提取设备类型
     */
    private function extractDeviceType($deviceName) {
        // 移除数字前缀（如"1#"、"2#"等）
        $name = preg_replace('/^\d+#/', '', $deviceName);

        // 移除数字后缀
        $name = preg_replace('/\d+$/', '', $name);

        return trim($name);
    }

    /**
     * 获取设备类型的唯一ID（用于文件名）
     */
    private function getDeviceTypeId($deviceType) {
        // 创建设备类型映射表
        static $deviceTypeMap = [
            '溶气泵' => 'rongqibeng',
            '热媒泵' => 'remeibeng',
            '调水泵' => 'tiaoshuibeng',
            '热水循环泵' => 'reshuixunhuanbeng',
            '闭排泵' => 'bipaibeng',
            '开排泵' => 'kaipaibeng',
            '污水泵' => 'wushuibeng',
            '污泥泵' => 'wunibeng',
            '生产空压机' => 'shengchankongya',
            'CB26空压机' => 'cb26kongya'
        ];

        // 如果有预定义映射，使用映射
        if (isset($deviceTypeMap[$deviceType])) {
            return $deviceTypeMap[$deviceType];
        }

        // 否则生成基于CRC32的短ID
        return 'type_' . abs(crc32($deviceType));
    }

    /**
     * 构建只包含主内容的页面（删除顶部、底部、左侧菜单）
     */
    private function buildContentOnlyPage($config) {
        $reportId = $config['report_id'];
        $reportName = $config['template']['name'] ?? "报表 {$reportId}";
        $templateCode = $config['template']['template_code'] ?? 'generic';
        $reportType = $config['template']['report_type'] ?? 'daily';
        $isQueryMode = $config['type'] === 'query';

        // 获取设备名称用于标题
        $deviceName = '';
        $deviceId = $config['device_id'] ?? null;
        if ($deviceId) {
            try {
                $pdo = \App\Core\DB::conn();
                $stmt = $pdo->prepare("SELECT name FROM devices WHERE id=?");
                $stmt->execute([$deviceId]);
                $deviceName = $stmt->fetchColumn() ?: '';
            } catch (\Exception $e) {
                $deviceName = '';
            }
        }

        // 构建标题
        $fullTitle = $reportName . ($deviceName ? ' - ' . $deviceName : '') . ($isQueryMode ? ' - 查询' : '');

        // 构建HTML页面（只包含主内容）
        $html = $this->buildSimpleHeader($fullTitle, $isQueryMode);
        $html .= $this->buildMainContent($config);  // 保持原有的主内容
        $html .= $this->buildSystemScripts($config);  // 保持原有的脚本

        return $html;
    }

    /**
     * 构建完整的系统页面（包含header、sidebar、footer）
     */
    private function buildCompleteSystemPage($config) {
        $reportId = $config['report_id'];
        $reportName = $config['template']['name'] ?? "报表 {$reportId}";
        $templateCode = $config['template']['template_code'] ?? 'generic';
        $reportType = $config['template']['report_type'] ?? 'daily';
        $isQueryMode = $config['type'] === 'query';

        // 获取设备名称用于标题（与EntryController保持一致）
        $deviceName = '';
        $deviceId = $config['device_id'] ?? null;
        if ($deviceId) {
            try {
                $pdo = \App\Core\DB::conn();
                $stmt = $pdo->prepare("SELECT name FROM devices WHERE id=?");
                $stmt->execute([$deviceId]);
                $deviceName = $stmt->fetchColumn() ?: '';
            } catch (\Exception $e) {
                // 如果获取设备名称失败，继续使用空名称
                $deviceName = '';
            }
        }

        // 构建标题：报表名称 + 设备名称 + 查询标识（与EntryController完全一致）
        $fullTitle = $reportName . ($deviceName ? ' - ' . $deviceName : '') . ($isQueryMode ? ' - 查询' : '');

        // 构建HTML页面（删除顶部菜单、侧边栏和底部）
        $html = $this->buildSimpleHeader($fullTitle, $isQueryMode);
        // 删除顶部菜单：$html .= $this->buildSystemHeader($fullTitle, $isQueryMode);
        // 删除侧边栏：$html .= $this->buildSystemSidebar();
        $html .= $this->buildMainContent($config);
        // 删除底部：$html .= $this->buildSystemFooter();
        $html .= $this->buildSystemScripts($config);

        return $html;
    }



    /**
     * 构建简化头部（不含顶部菜单）
     */
    private function buildSimpleHeader($title, $isQueryMode = false) {
        $pageTitle = $isQueryMode ? "数据查询 - {$title}" : "数据录入 - {$title}";

        return '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($pageTitle) . '</title>
    <link href="' . BASE_URL . 'css/bootstrap.min.css" rel="stylesheet">
    <link href="' . BASE_URL . 'css/all.min.css" rel="stylesheet">
    <link href="' . BASE_URL . 'css/app.css" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; background: #f8f9fa; }
        .container-fluid { padding: 20px; }
        .auth-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(248, 249, 250, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
    </style>
    <script>
        // 权限验证脚本 - 在页面加载前执行
        (function() {
            // 显示加载遮罩
            document.addEventListener("DOMContentLoaded", function() {
                const loadingDiv = document.createElement("div");
                loadingDiv.className = "auth-loading";
                loadingDiv.innerHTML = `
                    <div style="text-align: center;">
                        <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>验证权限中...</p>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // 检查用户登录状态
                fetch("' . BASE_URL . 'index.php?r=api/user")
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.data) {
                            // 用户已登录，检查具体权限
                            const userRoles = data.data.roles || [];
                            const reportGroup = getReportGroup();

                            if (hasReportAccess(userRoles, reportGroup)) {
                                // 有权限访问此报表，移除加载遮罩
                                loadingDiv.remove();
                                // 初始化用户信息（与系统权限结构一致）
                                window.__USER_ROLES__ = userRoles;
                                window.__USER_PERMISSIONS__ = {
                                    isAdmin: userRoles.includes("admin"),
                                    isMod: userRoles.includes("mod"),
                                    canEdit: userRoles.includes("admin") || userRoles.includes("mod"),
                                    canView: true,
                                    roles: userRoles
                                };
                                // 更新用户显示信息
                                window.__CURRENT_USER__ = {
                                    id: data.data.id,
                                    username: data.data.username,
                                    real_name: data.data.real_name
                                };
                            } else {
                                // 没有权限访问此报表
                                showAccessDenied(reportGroup);
                            }
                        } else {
                            // 用户未登录，重定向到登录页面
                            redirectToLogin();
                        }
                    })
                    .catch(error => {
                        console.error("权限验证失败:", error);
                        redirectToLogin();
                    });
            });

            // 获取当前报表所属的分组
            function getReportGroup() {
                // 从URL中提取报表ID
                const url = window.location.href;
                const reportIdMatch = url.match(/entry_report_(\d+)_/);

                if (reportIdMatch) {
                    const reportId = parseInt(reportIdMatch[1]);
                    // 根据报表ID判断分组（基于数据库中的实际分类）
                    if (reportId === 2005) {
                        return "cb26"; // CB26设备
                    } else if (reportId === 2006) {
                        return "ctrl"; // 中控设备
                    } else {
                        return "site"; // 现场设备（1001, 1002, 2001等）
                    }
                }

                // 备用方案：从标题判断
                const title = document.title || "";
                if (title.includes("CB26") || title.toLowerCase().includes("cb26")) {
                    return "cb26";
                } else if (title.includes("中控") || title.toLowerCase().includes("ctrl")) {
                    return "ctrl";
                } else {
                    return "site"; // 默认为现场
                }
            }

            // 检查用户是否有权限访问指定分组的报表
            function hasReportAccess(userRoles, reportGroup) {
                // 管理员和普通管理员可以访问所有报表
                if (userRoles.includes("admin") || userRoles.includes("mod")) {
                    return true;
                }
                // 其他用户只能访问对应角色的报表
                return userRoles.includes(reportGroup);
            }

            function showAccessDenied(reportGroup) {
                const groupNames = {
                    "site": "现场",
                    "cb26": "CB26",
                    "ctrl": "中控"
                };
                const groupName = groupNames[reportGroup] || reportGroup;

                document.body.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <i class="fa fa-ban fa-3x mb-3 text-danger"></i>
                            <h4>权限不足</h4>
                            <p class="text-muted">您没有权限访问${groupName}相关的报表</p>
                            <p class="small text-muted">请联系管理员分配相应的角色权限</p>
                            <a href="' . BASE_URL . '" class="btn btn-primary">返回首页</a>
                        </div>
                    </div>
                `;
            }

            // 获取当前报表所属的分组
            function getReportGroup() {
                const title = document.title || "";
                if (title.includes("CB26") || title.toLowerCase().includes("cb26")) {
                    return "cb26";
                } else if (title.includes("中控") || title.toLowerCase().includes("ctrl")) {
                    return "ctrl";
                } else {
                    return "site"; // 默认为现场
                }
            }

            // 检查用户是否有权限访问指定分组的报表
            function hasReportAccess(userRoles, reportGroup) {
                // 管理员和普通管理员可以访问所有报表
                if (userRoles.includes("admin") || userRoles.includes("mod")) {
                    return true;
                }
                // 其他用户只能访问对应角色的报表
                return userRoles.includes(reportGroup);
            }

            function showAccessDenied(reportGroup) {
                const groupNames = {
                    "site": "现场",
                    "cb26": "CB26",
                    "ctrl": "中控"
                };
                const groupName = groupNames[reportGroup] || reportGroup;

                document.body.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <i class="fa fa-ban fa-3x mb-3 text-danger"></i>
                            <h4>权限不足</h4>
                            <p class="text-muted">您没有权限访问${groupName}相关的报表</p>
                            <p class="small text-muted">请联系管理员分配相应的角色权限</p>
                            <a href="' . BASE_URL . '" class="btn btn-primary">返回首页</a>
                        </div>
                    </div>
                `;
            }

            function redirectToLogin() {
                const currentUrl = encodeURIComponent(window.location.href);
                const loginUrl = "' . BASE_URL . 'index.php?r=frontend/login&redirect=" + currentUrl;

                document.body.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <i class="fa fa-lock fa-3x mb-3 text-warning"></i>
                            <h4>需要登录</h4>
                            <p class="text-muted">此页面需要登录后才能访问</p>
                            <a href="${loginUrl}" class="btn btn-primary">前往登录</a>
                        </div>
                    </div>
                `;
            }
        })();
    </script>
</head>
<body>';
    }

    /**
     * 构建系统头部
     */
    private function buildSystemHeader($title, $isQueryMode = false) {
        $pageTitle = $isQueryMode ? "数据查询 - {$title}" : "数据录入 - {$title}";

        return '<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>' . htmlspecialchars($pageTitle) . '</title>
  <link rel="stylesheet" href="../css/bootstrap.min.css">
  <link rel="stylesheet" href="../css/all.min.css">
  <link rel="stylesheet" href="../css/app.css">
</head>
<body class="sidebar-open" data-current-path="/bbgl/static_pages/" data-route="static">
<nav class="navbar navbar-expand-lg fixed-top apple-nav shadow-sm">
  <div class="container-fluid px-3">
    <button class="btn-icon me-2" id="sidebarToggle" aria-label="Toggle Sidebar"><span class="bar"></span><span class="bar"></span><span class="bar"></span></button>
    <a class="navbar-brand d-flex align-items-center gap-2 fw-semibold" href="../">
      <span class="brand-dot"></span>
      <span class="d-none d-sm-inline">设备资料录入管理系统</span>
    </a>
    <div class="d-flex align-items-center gap-3 ms-auto">
      <span class="text-muted small">v1.0</span>
      <span class="text-muted small me-2" id="currentUser">静态页面用户</span>
      <button class="btn btn-sm btn-ghost" onclick="history.back()"><i class="fa fa-arrow-left"></i> 返回</button>
      <button class="btn btn-sm btn-ghost" onclick="window.close()"><i class="fa fa-times"></i> 关闭</button>
    </div>
  </div>
</nav>
';
    }

    /**
     * 构建系统侧边栏
     */
    private function buildSystemSidebar() {
        return '
<aside class="sidebar" id="sidebar">
  <div class="sidebar-inner">
    <div class="sidebar-title text-muted small mb-2">导航</div>
    <ul class="menu">
      <li><a class="item" href="../"><i class="fa fa-home"></i><span>首页</span></a></li>
      <li class="group">静态页面</li>
      <li><a class="item depth-1" href="javascript:history.back()"><span>返回系统</span></a></li>
      <li><a class="item depth-1" href="javascript:window.close()"><span>关闭页面</span></a></li>
    </ul>
  </div>
</aside>
';
    }

    /**
     * 构建主要内容区域（完全复制EntryController的结构）
     */
    private function buildMainContent($config) {
        $reportId = $config['report_id'];
        $reportName = $config['template']['name'] ?? "报表 {$reportId}";
        $templateCode = $config['template']['template_code'] ?? 'generic';
        $reportType = $config['template']['report_type'] ?? 'daily';
        $isQueryMode = $config['type'] === 'query';

        // 获取设备名称用于标题（与EntryController保持一致）
        $deviceName = '';
        $deviceId = $config['device_id'] ?? null;
        if ($deviceId) {
            try {
                $pdo = \App\Core\DB::conn();
                $stmt = $pdo->prepare("SELECT name FROM devices WHERE id=?");
                $stmt->execute([$deviceId]);
                $deviceName = $stmt->fetchColumn() ?: '';
            } catch (\Exception $e) {
                // 如果获取设备名称失败，继续使用空名称
                $deviceName = '';
            }
        }

        // 构建标题：报表名称 + 设备名称 + 查询标识（与EntryController完全一致）
        $title = $reportName . ($deviceName ? ' - ' . $deviceName : '') . ($isQueryMode ? ' - 查询' : '');

        $html = '<main class="container-fluid py-3" style="margin-left: 0; max-width: 100%;">';

        // 删除面包屑导航
        // $html .= '<nav aria-label="breadcrumb" class="mb-3">';
        // $html .= '<ol class="breadcrumb">';
        // 删除面包屑导航内容

        // 页面标题（删除胶囊菜单）
        $html .= '<div class="page-head mb-3">';
        // 删除胶囊菜单：$html .= '<div class="breadcrumb small text-muted mb-2">' . ($isQueryMode ? '查询 / 报表查询' : '录入 / 报表录入') . '</div>';
        $html .= '<div class="text-center">';
        $html .= '<div class="page-title">' . htmlspecialchars($title) . '</div>';
        $html .= '</div>';
        $html .= '</div>';

        // 设备选择和日期选择区域（与EntryController完全一致）
        $html .= '<div class="card mb-3"><div class="card-body">';
        $html .= '<div class="row g-3">';
        $html .= '<div class="col-sm-3"><label class="form-label">设备选择</label><select id="deviceSelect" class="form-select">';
        $html .= '<option value="">请选择设备...</option>';
        $html .= '</select></div>';

        if ($reportType === 'continuous') {
            // 连续报表：显示月份选择和分页控制
            $html .= '<div class="col-sm-2"><label class="form-label">查看月份</label><input id="viewMonth" type="month" class="form-control" value="' . date('Y-m') . '"></div>';
            $html .= '<div class="col-sm-2"><label class="form-label">显示范围</label><select id="viewRange" class="form-select">';
            $html .= '<option value="month">当月</option>';
            $html .= '<option value="year">全年</option>';
            $html .= '</select></div>';
            $html .= '<div class="col-sm-2"><label class="form-label">排序方式</label><select id="sortOrder" class="form-select">';
            $html .= '<option value="desc">降序（最新在前）</option>';
            $html .= '<option value="asc">升序（最早在前）</option>';
            $html .= '</select></div>';
            $html .= '<div class="col-sm-3 d-flex align-items-end">';
            $html .= '<button id="btnQuery" class="btn btn-primary">查询数据</button>';
            $html .= '</div>';
        } else {
            // 日报表：显示日期选择
            $html .= '<div class="col-sm-3"><label class="form-label">日期</label><input id="entryDate" type="date" class="form-control" value="' . date('Y-m-d') . '"></div>';
            $html .= '<div class="col-sm-5 d-flex align-items-end"><button id="btnQuery" class="btn btn-primary">查询数据</button></div>';
        }

        $html .= '</div>';
        $html .= '</div></div>';

        // 数据录入表格容器（与EntryController完全一致）
        $html .= '<div class="card"><div class="card-body">';
        $html .= '<div id="entryTableContainer">';
        $html .= '<div class="text-center text-muted py-4">';
        $html .= '<i class="fa fa-info-circle fa-2x mb-2"></i>';

        if ($reportType === 'continuous') {
            $html .= '<p>请选择设备和查看月份，数据将自动加载</p>';
            $html .= '<p class="small">连续报表：每次运行记录一行，支持自定义日期时间</p>';
        } else {
            $html .= '<p>请选择设备和日期，数据将自动加载</p>';
            $html .= '<p class="small">日报表：按固定时间段录入数据</p>';
        }

        $html .= '</div>';
        $html .= '</div>';

        // 值班人员信息区域（空容器，由JavaScript动态填充）
        $html .= '<div id="dutyStaffContainer" style="display:none;"></div>';

        $html .= '</div></div>';

        $html .= '</main>';

        return $html;
    }

    /**
     * 生成值班人员HTML（静态版本）
     */
    private function generateDutyStaffHTML($config)
    {
        $dutyStaffConfig = $config['template']['duty_staff_config'] ?? [];

        // 如果duty_staff_config是字符串，尝试解析JSON
        if (is_string($dutyStaffConfig)) {
            $decoded = json_decode($dutyStaffConfig, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $dutyStaffConfig = $decoded;
            } else {
                $dutyStaffConfig = [];
            }
        }

        // 如果未启用值班人功能，返回空
        if (!($dutyStaffConfig['enabled'] ?? false)) {
            return '';
        }

        $dayLabel = $dutyStaffConfig['day_label'] ?? '白班值班人';
        $nightLabel = $dutyStaffConfig['night_label'] ?? '夜班值班人';
        $required = ($dutyStaffConfig['required'] ?? false) ? ' required' : '';
        $isQueryMode = $config['type'] === 'query';

        $html = '<div id="dutyStaffContainer" style="display:block;">';
        $html .= '<div class="mt-3 mb-3" style="padding: 15px; border-top: 1px solid #dee2e6; background-color: #f8f9fa;">';
        $html .= '<div class="d-flex align-items-center justify-content-between flex-wrap">';
        $html .= '<div class="d-flex align-items-center mb-2">';
        $html .= '<strong class="me-3">值班人员信息</strong>';
        $html .= '</div>';
        $html .= '<div class="d-flex align-items-center gap-4 flex-wrap">';

        if ($isQueryMode) {
            // 查询模式：只读显示
            $html .= '<div class="d-flex align-items-center">';
            $html .= '<span class="me-2 text-nowrap">' . htmlspecialchars($dayLabel) . '：</span>';
            $html .= '<span class="text-muted" id="dayShiftDisplay">未填写</span>';
            $html .= '</div>';
            $html .= '<div class="d-flex align-items-center">';
            $html .= '<span class="me-2 text-nowrap">' . htmlspecialchars($nightLabel) . '：</span>';
            $html .= '<span class="text-muted" id="nightShiftDisplay">未填写</span>';
            $html .= '</div>';
            $html .= '<small class="text-muted">数据仅供查看</small>';
        } else {
            // 录入模式：可编辑
            $html .= '<div class="d-flex align-items-center">';
            $html .= '<span class="me-2 text-nowrap">' . htmlspecialchars($dayLabel) . '：</span>';
            $html .= '<input id="dayShiftStaff" type="text" class="form-control form-control-sm" style="width: 120px;"';
            $html .= ' placeholder="请输入' . htmlspecialchars($dayLabel) . '"' . $required . '>';
            $html .= '</div>';
            $html .= '<div class="d-flex align-items-center">';
            $html .= '<span class="me-2 text-nowrap">' . htmlspecialchars($nightLabel) . '：</span>';
            $html .= '<input id="nightShiftStaff" type="text" class="form-control form-control-sm" style="width: 120px;"';
            $html .= ' placeholder="请输入' . htmlspecialchars($nightLabel) . '"' . $required . '>';
            $html .= '</div>';
            $html .= '<div class="d-flex align-items-center gap-2">';
            $html .= '<button class="btn btn-primary btn-sm" onclick="saveDutyStaff()" title="保存值班人员">';
            $html .= '<i class="fa fa-save me-1"></i>保存';
            $html .= '</button>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }



    /**
     * 构建系统Footer（包含完整的footer元素）
     */
    private function buildSystemFooter() {
        return '
<footer class="app-footer text-muted small">
  <div class="container-fluid px-3">&copy; ' . date('Y') . ' 设备资料录入管理系统</div>
</footer>
<script src="/bbgl/js/jquery-3.6.0.min.js"></script>
<script src="/bbgl/js/bootstrap.bundle.min.js"></script>
<script src="/bbgl/js/chart.min.js"></script>
<script src="/bbgl/js/app.js"></script>
<script>
// 侧边栏切换功能
document.getElementById("sidebarToggle")?.addEventListener("click", function() {
    document.body.classList.toggle("sidebar-open");
});




    .catch(error => {
        console.log("获取用户信息失败，设置为只读模式:", error);
        // 获取用户信息失败，设置为只读模式
        window.__USER_ROLES__ = [];
        window.__USER_PERMISSIONS__ = {
            isAdmin: false,
            canEdit: false,  // 无法确认用户身份，不允许编辑
            canView: true
        };

        // 更新用户显示名称
        const userElement = document.getElementById("currentUser");
        if (userElement) {
            userElement.textContent = "访客用户";
        }
    });
</script>
';
    }

    /**
     * 构建系统脚本
     */
    private function buildSystemScripts($config) {
        $reportId = $config['report_id'];
        $templateCode = $config['template']['template_code'] ?? 'generic';
        $reportType = $config['template']['report_type'] ?? 'daily';
        $isQueryMode = $config['type'] === 'query';

        // 从配置中提取报表配置信息
        $timeLimitDays = (int)($config['template']['time_limit_days'] ?? 2);
        $timeLimitType = $config['template']['time_limit_type'] ?? 'day';
        $dutyStaffConfig = $config['template']['duty_staff_config'] ?? [];

        // 如果duty_staff_config是字符串，尝试解析JSON
        if (is_string($dutyStaffConfig)) {
            $decoded = json_decode($dutyStaffConfig, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $dutyStaffConfig = $decoded;
            } else {
                $dutyStaffConfig = [];
            }
        }

        $script = '<script>';

        // 与EntryController完全一致的全局变量（使用绝对路径）
        $baseUrl = '/bbgl/';  // 根据实际部署路径调整
        $getDevicesUrl = $baseUrl . 'index.php?r=api/reports/devices&report_id=' . $reportId;

        // 如果指定了设备ID，添加过滤参数
        $deviceId = $config['device_id'] ?? null;
        if ($deviceId) {
            $getDevicesUrl .= '&device_id=' . $deviceId;
        }

        $script .= "\nwindow.__REPORT_API__ = {\n";
        $script .= '  reportId: ' . $reportId . ",\n";
        $script .= '  templateCode: "' . $templateCode . "\",\n";
        $script .= '  reportType: "' . $reportType . "\",\n";
        $script .= '  deviceId: ' . ($deviceId ?: 0) . ",\n";
        $script .= '  isQueryMode: ' . ($isQueryMode ? 'true' : 'false') . ",\n";
        $script .= '  getDevices: "' . $getDevicesUrl . "\",\n";
        $script .= '  loadData: "' . $baseUrl . 'index.php?r=api/entry/load",' . "\n";
        if ($isQueryMode) {
            $script .= '  saveData: null' . "\n";
        } else {
            $script .= '  saveData: "' . $baseUrl . 'index.php?r=api/entry/save"' . "\n";
        }
        $script .= "};\n";

        // 传递用户角色信息（动态获取）
        $script .= "\nwindow.__USER_ROLES__ = []; // 将通过API动态更新\n";
        $script .= "window.__USER_PERMISSIONS__ = {}; // 用户权限缓存\n";

        // 传递系统配置信息
        $script .= "\nwindow.__SYSTEM_CONFIG__ = {\n";
        $script .= "  show_entry_info: true,\n";
        $script .= "  show_modify_info: true\n";
        $script .= "};\n";

        // 传递报表配置信息
        $script .= "\nwindow.__REPORT_CONFIG__ = {\n";
        $script .= '  time_limit_days: ' . $timeLimitDays . ",\n";
        $script .= '  time_limit_type: "' . $timeLimitType . "\",\n";
        $script .= '  duty_staff: ' . json_encode($dutyStaffConfig, JSON_UNESCAPED_UNICODE) . "\n";
        $script .= "};\n";

        $script .= '</script>';

        // 与EntryController完全一致的脚本加载
        $script .= '<script src="/bbgl/js/entry-generic.js"></script>';

        // 静态页面特有：初始化值班人员表单
        $script .= "\n<script>\n";
        $script .= "document.addEventListener(\"DOMContentLoaded\", function() {\n";
        $script .= "  console.log(\"Static page loaded, initializing duty staff form\");\n";
        $script .= "  // 初始化空的值班人员数据\n";
        $script .= "  if (typeof showDutyStaffForm === \"function\") {\n";
        $script .= "    showDutyStaffForm({});\n";
        $script .= "  }\n";
        $script .= "});\n";
        $script .= "</script>\n";

        // 如果是连续报表，额外加载连续报表的JS
        if ($reportType === 'continuous') {
            $script .= '<script src="/bbgl/js/entry-continuous.js"></script>';
        }

        $script .= '</body></html>';

        return $script;
    }



    // 以下是原有的方法，保留用于参考
    private function buildQueryPageHTML_OLD($config) {
        $fields = $config['template']['fields'] ?? [];
        $reportId = $config['report_id'];

        $html = '<div class="container-fluid">';
        $html .= '<div class="row">';
        $html .= '<div class="col-12">';

        // 页面标题
        $html .= '<div class="d-flex justify-content-between align-items-center mb-4">';
        $html .= '<h4>数据查询 - 报表 ' . $reportId . '</h4>';
        $html .= '<div class="btn-group">';
        $html .= '<button class="btn btn-primary" id="btnQuery">查询数据</button>';
        $html .= '<button class="btn btn-success" id="btnExport">导出Excel</button>';
        $html .= '</div>';
        $html .= '</div>';
        
        // 查询条件
        $html .= '<div class="card mb-3">';
        $html .= '<div class="card-body">';
        $html .= '<div class="row">';
        $html .= '<div class="col-md-3">';
        $html .= '<label class="form-label">设备</label>';
        $html .= '<select class="form-select" id="deviceSelect">';
        $html .= '<option value="">全部设备</option>';
        $html .= '</select>';
        $html .= '</div>';
        $html .= '<div class="col-md-3">';
        $html .= '<label class="form-label">开始日期</label>';
        $html .= '<input type="date" class="form-control" id="startDate">';
        $html .= '</div>';
        $html .= '<div class="col-md-3">';
        $html .= '<label class="form-label">结束日期</label>';
        $html .= '<input type="date" class="form-control" id="endDate">';
        $html .= '</div>';
        $html .= '<div class="col-md-3">';
        $html .= '<label class="form-label">排序</label>';
        $html .= '<select class="form-select" id="sortOrder">';
        $html .= '<option value="desc">最新在前</option>';
        $html .= '<option value="asc">最旧在前</option>';
        $html .= '</select>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // 查询结果表格
        $html .= '<div class="card">';
        $html .= '<div class="card-body">';
        $html .= '<div id="queryResults">';
        $html .= '<div class="text-center text-muted py-5">';
        $html .= '<i class="fa fa-search fa-3x mb-3"></i>';
        $html .= '<p>请设置查询条件并点击"查询数据"按钮</p>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * 生成字段HTML
     */
    private function generateFieldHTML($field) {
        $colClass = 'col-md-' . ($field['width'] ?? 6);
        $required = $field['required'] ?? false;
        $requiredAttr = $required ? 'required' : '';
        $requiredMark = $required ? '<span class="text-danger">*</span>' : '';
        
        $html = '<div class="' . $colClass . ' mb-3">';
        $html .= '<label class="form-label">' . htmlspecialchars($field['label']) . $requiredMark . '</label>';
        
        switch ($field['type']) {
            case 'text':
                $html .= '<input type="text" class="form-control" name="' . $field['key'] . '" ' . $requiredAttr . '>';
                break;
            case 'number':
                $min = isset($field['min']) ? 'min="' . $field['min'] . '"' : '';
                $max = isset($field['max']) ? 'max="' . $field['max'] . '"' : '';
                $html .= '<input type="number" class="form-control" name="' . $field['key'] . '" ' . $min . ' ' . $max . ' ' . $requiredAttr . '>';
                break;
            case 'select':
                $html .= '<select class="form-select" name="' . $field['key'] . '" ' . $requiredAttr . '>';
                $html .= '<option value="">请选择...</option>';
                foreach ($field['options'] ?? [] as $option) {
                    $html .= '<option value="' . htmlspecialchars($option['value']) . '">' . htmlspecialchars($option['label']) . '</option>';
                }
                $html .= '</select>';
                break;
            case 'time':
                $html .= '<input type="time" class="form-control" name="' . $field['key'] . '" ' . $requiredAttr . '>';
                break;
            case 'date':
                $html .= '<input type="date" class="form-control" name="' . $field['key'] . '" ' . $requiredAttr . '>';
                break;
            default:
                $html .= '<input type="text" class="form-control" name="' . $field['key'] . '" ' . $requiredAttr . '>';
        }
        
        if (!empty($field['help'])) {
            $html .= '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * 按分组整理字段
     */
    private function groupFields($fields) {
        $groups = ['ungrouped' => []];
        
        foreach ($fields as $field) {
            $group = $field['group'] ?? 'ungrouped';
            if (!isset($groups[$group])) {
                $groups[$group] = [];
            }
            $groups[$group][] = $field;
        }
        
        return $groups;
    }
    
    /**
     * 构建CSS
     */
    private function buildEntryPageCSS($config) {
        return '
        .container-fluid { max-width: 1400px; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075); }
        .form-label { font-weight: 500; }
        .text-danger { color: #dc3545 !important; }
        .border-bottom { border-bottom: 1px solid #dee2e6 !important; }
        .btn-group .btn { margin-right: 0.5rem; }
        .btn-group .btn:last-child { margin-right: 0; }
        ';
    }
    
    private function buildQueryPageCSS($config) {
        return $this->buildEntryPageCSS($config) . '
        #queryResults table { font-size: 0.875rem; }
        #queryResults .table th { background-color: #f8f9fa; }
        .text-muted { color: #6c757d !important; }
        ';
    }
    
    /**
     * 构建JavaScript
     */
    private function buildEntryPageJS($config) {
        $reportId = $config['report_id'];
        
        return "
        // 静态页面配置
        const PAGE_CONFIG = " . json_encode($config) . ";
        
        // API端点（使用实际的系统API）
        const API_ENDPOINTS = {
            devices: '" . BASE_URL . "index.php?r=api/reports/devices&report_id={$reportId}',
            save: '" . BASE_URL . "index.php?r=api/entry/save',
            load: '" . BASE_URL . "index.php?r=api/entry/load'
        };
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDevices();
            bindEvents();
            setDefaultDate();
        });
        
        // 加载设备列表
        function loadDevices() {
            fetch(API_ENDPOINTS.devices)
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('deviceSelect');
                    data.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.id;
                        option.textContent = device.name;
                        select.appendChild(option);
                    });
                })
                .catch(error => console.error('加载设备失败:', error));
        }
        
        // 绑定事件
        function bindEvents() {
            document.getElementById('btnSave').addEventListener('click', saveData);
            document.getElementById('btnReset').addEventListener('click', resetForm);
        }
        
        // 设置默认日期
        function setDefaultDate() {
            document.getElementById('entryDate').value = new Date().toISOString().slice(0, 10);
        }
        
        // 保存数据
        function saveData() {
            const form = document.getElementById('entryForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            data.report_id = {$reportId};
            data.device_id = document.getElementById('deviceSelect').value;
            data.entry_date = document.getElementById('entryDate').value;
            
            fetch(API_ENDPOINTS.save, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('保存成功');
                    resetForm();
                } else {
                    alert('保存失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('网络错误，保存失败');
            });
        }
        
        // 重置表单
        function resetForm() {
            document.getElementById('entryForm').reset();
            setDefaultDate();
        }
        ";
    }
    
    private function buildQueryPageJS($config) {
        $reportId = $config['report_id'];
        
        return "
        // 静态页面配置
        const PAGE_CONFIG = " . json_encode($config) . ";
        
        // API端点（使用实际的系统API）
        const API_ENDPOINTS = {
            devices: '" . BASE_URL . "index.php?r=api/reports/devices&report_id={$reportId}',
            query: '" . BASE_URL . "index.php?r=api/entry/load',
            export: '" . BASE_URL . "index.php?r=api/entry/export'
        };
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDevices();
            bindEvents();
            setDefaultDates();
        });
        
        // 加载设备列表
        function loadDevices() {
            fetch(API_ENDPOINTS.devices)
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('deviceSelect');
                    data.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.id;
                        option.textContent = device.name;
                        select.appendChild(option);
                    });
                })
                .catch(error => console.error('加载设备失败:', error));
        }
        
        // 绑定事件
        function bindEvents() {
            document.getElementById('btnQuery').addEventListener('click', queryData);
            document.getElementById('btnExport').addEventListener('click', exportData);
        }
        
        // 设置默认日期
        function setDefaultDates() {
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('startDate').value = lastMonth.toISOString().slice(0, 10);
            document.getElementById('endDate').value = today.toISOString().slice(0, 10);
        }
        
        // 查询数据
        function queryData() {
            const params = {
                report_id: {$reportId},
                device_id: document.getElementById('deviceSelect').value,
                start_date: document.getElementById('startDate').value,
                end_date: document.getElementById('endDate').value,
                sort: document.getElementById('sortOrder').value
            };
            
            const queryString = new URLSearchParams(params).toString();
            
            fetch(API_ENDPOINTS.query + '?' + queryString)
                .then(response => response.json())
                .then(data => {
                    renderResults(data);
                })
                .catch(error => {
                    console.error('查询失败:', error);
                    alert('查询失败');
                });
        }
        
        // 渲染查询结果
        function renderResults(data) {
            const container = document.getElementById('queryResults');
            if (!data || data.length === 0) {
                container.innerHTML = '<div class=\"text-center text-muted py-5\"><p>没有找到符合条件的数据</p></div>';
                return;
            }
            
            // 生成表格HTML
            let html = '<div class=\"table-responsive\"><table class=\"table table-striped table-hover\">';
            html += '<thead><tr><th>日期</th><th>设备</th>';
            
            // 添加字段列
            const fields = PAGE_CONFIG.template.fields || [];
            fields.forEach(field => {
                html += '<th>' + field.label + '</th>';
            });
            
            html += '</tr></thead><tbody>';
            
            data.forEach(row => {
                html += '<tr>';
                html += '<td>' + row.entry_date + '</td>';
                html += '<td>' + row.device_name + '</td>';
                
                fields.forEach(field => {
                    html += '<td>' + (row[field.key] || '-') + '</td>';
                });
                
                html += '</tr>';
            });
            
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }
        
        // 导出数据
        function exportData() {
            const params = {
                report_id: {$reportId},
                device_id: document.getElementById('deviceSelect').value,
                start_date: document.getElementById('startDate').value,
                end_date: document.getElementById('endDate').value,
                sort: document.getElementById('sortOrder').value
            };
            
            const queryString = new URLSearchParams(params).toString();
            window.open(API_ENDPOINTS.export + '?' + queryString);
        }
        ";
    }
    
    /**
     * 组装完整页面
     */
    private function assembleFullPage($html, $css, $js, $config) {
        $title = $config['type'] === 'entry' ? '数据录入' : '数据查询';
        $title .= ' - 报表 ' . $config['report_id'];

        $page = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($title) . '</title>
    <link href="' . BASE_URL . 'css/bootstrap.min.css" rel="stylesheet">
    <link href="' . BASE_URL . 'css/all.min.css" rel="stylesheet">
    <style>' . $css . '</style>
</head>
<body>
    <div class="py-4">
        ' . $html . '
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>' . $js . '</script>
    
    <!-- 生成信息 -->
    <div class="text-center text-muted mt-4 small">
        页面生成时间: ' . $config['generated_at'] . ' | 静态化版本
    </div>
</body>
</html>';

        if ($this->config['minify_html']) {
            $page = $this->minifyHTML($page);
        }
        
        return $page;
    }
    
    /**
     * HTML压缩
     */
    private function minifyHTML($html) {
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);
        return trim($html);
    }
}
