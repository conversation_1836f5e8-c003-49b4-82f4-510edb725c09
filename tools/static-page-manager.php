<?php
/**
 * 静态页面管理工具
 * 支持生成、清理、状态检查等功能
 */

// 在命令行环境中定义 BASE_URL
if (!defined('BASE_URL')) {
    define('BASE_URL', 'http://localhost:8088/bbgl/');
}

require_once __DIR__ . '/static-generator.php';
require_once __DIR__ . '/../app/Core/DB.php';
use App\Core\DB;

class StaticPageManager
{
    private $generator;
    
    public function __construct()
    {
        $this->generator = new StaticPageGenerator([
            'output_dir' => __DIR__ . '/../static_pages/',
            'minify_html' => false
        ]);
    }
    
    /**
     * 生成所有静态页面
     */
    public function generateAll()
    {
        echo "=== 生成所有静态页面（使用友好文件名）===\n\n";
        
        $pdo = DB::conn();
        
        // 获取所有启用的报表
        $stmt = $pdo->query("
            SELECT r.*, rt.fields_config, rt.report_type 
            FROM reports r 
            LEFT JOIN report_templates rt ON r.template_code = rt.code 
            WHERE r.enabled = 1 
            ORDER BY r.id
        ");
        $reports = $stmt->fetchAll();
        
        if (empty($reports)) {
            echo "没有找到启用的报表\n";
            return;
        }
        
        $totalGenerated = 0;
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($reports as $report) {
            echo "处理报表: {$report['name']} (ID: {$report['id']})\n";
            
            // 构建模板配置
            $templateConfig = $this->buildTemplateConfig($report);
            
            try {
                // 批量生成按设备分组的页面
                $results = $this->generator->generateByDevices((int)$report['id'], $templateConfig);

                foreach ($results as $result) {
                    $totalGenerated++;
                    if ($result['result']['success']) {
                        $successCount++;
                        echo "  ✓ {$result['type']} - {$result['device_name']}: {$result['result']['file']}\n";
                    } else {
                        $errorCount++;
                        echo "  ✗ {$result['type']} - {$result['device_name']}: 生成失败\n";
                    }
                }
                
            } catch (Exception $e) {
                $errorCount++;
                echo "  ✗ 报表 {$report['id']} 生成失败: {$e->getMessage()}\n";
            }
            
            echo "\n";
        }
        
        echo "=== 生成完成 ===\n";
        echo "总计: {$totalGenerated} 个页面\n";
        echo "成功: {$successCount} 个\n";
        echo "失败: {$errorCount} 个\n\n";
        
        $this->showAccessInfo();
    }
    
    /**
     * 清理无效的静态页面
     */
    public function cleanup()
    {
        echo "=== 清理无效的静态页面 ===\n\n";
        
        $results = $this->generator->cleanupInvalidPages();
        
        if (!empty($results['deleted'])) {
            echo "已删除的页面:\n";
            foreach ($results['deleted'] as $file) {
                echo "  ✓ {$file}\n";
            }
            echo "\n";
        }
        
        if (!empty($results['errors'])) {
            echo "删除失败的页面:\n";
            foreach ($results['errors'] as $file) {
                echo "  ✗ {$file}\n";
            }
            echo "\n";
        }
        
        if (!empty($results['kept'])) {
            echo "保留的页面: " . count($results['kept']) . " 个\n";
        }
        
        echo "清理完成!\n";
        echo "删除: " . count($results['deleted']) . " 个\n";
        echo "保留: " . count($results['kept']) . " 个\n";
        echo "错误: " . count($results['errors']) . " 个\n\n";
    }
    
    /**
     * 显示静态页面状态
     */
    public function status()
    {
        echo "=== 静态页面状态 ===\n\n";
        
        $staticDir = __DIR__ . '/../static_pages/';
        if (!is_dir($staticDir)) {
            echo "静态页面目录不存在\n";
            return;
        }
        
        $files = glob($staticDir . '*.html');
        
        if (empty($files)) {
            echo "没有找到静态页面\n";
            return;
        }
        
        echo "总计: " . count($files) . " 个静态页面\n\n";
        
        // 按类型分组统计
        $stats = [
            'entry' => 0,
            'query' => 0,
            'device_specific' => 0,
            'general' => 0
        ];
        
        foreach ($files as $file) {
            $filename = basename($file);
            
            if (strpos($filename, 'entry_') === 0) {
                $stats['entry']++;
            } elseif (strpos($filename, 'query_') === 0) {
                $stats['query']++;
            }
            
            if (strpos($filename, '_device_') !== false) {
                $stats['device_specific']++;
            } else {
                $stats['general']++;
            }
        }
        
        echo "页面类型统计:\n";
        echo "  录入页面: {$stats['entry']} 个\n";
        echo "  查询页面: {$stats['query']} 个\n";
        echo "  设备专用: {$stats['device_specific']} 个\n";
        echo "  通用页面: {$stats['general']} 个\n\n";
        
        // 显示最近的文件
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        echo "最近生成的页面:\n";
        for ($i = 0; $i < min(5, count($files)); $i++) {
            $file = $files[$i];
            $filename = basename($file);
            $time = date('Y-m-d H:i:s', filemtime($file));
            $size = round(filesize($file) / 1024, 1);
            echo "  {$filename} ({$size}KB, {$time})\n";
        }
        
        echo "\n";
        $this->showAccessInfo();
    }
    
    /**
     * 构建模板配置
     */
    private function buildTemplateConfig($report)
    {
        $templateConfig = [
            'report_id' => (int)$report['id'],
            'name' => $report['name'],
            'template_code' => $report['template_code'],
            'report_type' => $report['report_type'] ?: 'daily',
            'fields' => [],
            'time_limit_days' => (int)($report['time_limit_days'] ?? 2),
            'time_limit_type' => $report['time_limit_type'] ?? 'day',
            'duty_staff_config' => []
        ];
        
        // 解析字段配置
        if ($report['fields_config']) {
            $fieldsConfig = json_decode($report['fields_config'], true);
            if ($fieldsConfig && isset($fieldsConfig['fields'])) {
                $templateConfig['fields'] = $fieldsConfig['fields'];
            }
        }
        
        // 解析值班人员配置
        if (!empty($report['duty_staff_config'])) {
            $dutyConfig = json_decode($report['duty_staff_config'], true);
            if ($dutyConfig) {
                $templateConfig['duty_staff_config'] = $dutyConfig;
            }
        }
        
        return $templateConfig;
    }
    
    /**
     * 显示访问信息
     */
    private function showAccessInfo()
    {
        echo "访问地址:\n";
        echo "- 前端首页: http://localhost:8088/bbgl/index.php?r=frontend\n";
        echo "- 管理后台: http://localhost:8088/bbgl/\n\n";
        
        echo "生成的静态页面示例:\n";
        $staticDir = __DIR__ . '/../static_pages/';
        if (is_dir($staticDir)) {
            $files = glob($staticDir . '*_device_*.html');
            $count = 0;
            foreach ($files as $file) {
                if ($count >= 3) break;
                echo "- " . basename($file) . "\n";
                $count++;
            }
            if (count($files) > 3) {
                echo "- ... 还有 " . (count($files) - 3) . " 个设备专用页面\n";
            }
        }
        echo "\n";
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $manager = new StaticPageManager();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'generate':
            $manager->generateAll();
            break;
        case 'cleanup':
            $manager->cleanup();
            break;
        case 'status':
            $manager->status();
            break;
        case 'help':
        default:
            echo "静态页面管理工具\n\n";
            echo "用法: php static-page-manager.php [command]\n\n";
            echo "命令:\n";
            echo "  generate  生成所有静态页面\n";
            echo "  cleanup   清理无效的静态页面\n";
            echo "  status    显示静态页面状态\n";
            echo "  help      显示帮助信息\n\n";
            break;
    }
} else {
    // Web访问
    $manager = new StaticPageManager();
    $manager->status();
}
?>
